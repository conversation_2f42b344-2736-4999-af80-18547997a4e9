我将为您设计一个高性能、可扩展的iOS Photos高仿App架构。让我通过深度分析来输出完整的架构设计。

## 1. 整体架构图

### 分层结构：

#### UI Layer (Presentation)
- ViewControllers / SwiftUI Views
- Custom Views / Cells, Headers
- Coordinators / Navigation Flow
- ViewModels / UI State
- Transitions / Animations
- Gestures / Interactions

#### Business Logic Layer
- Media Manager / Albums, Photos
- Edit Manager / Filters, Adjustments
- Sync Manager / iCloud, Sharing
- Search Engine / ML, Indexing
- Permission / Access Control

#### Data Access Layer
- PhotoKit / PHAsset, PHCollection
- Core Data / Metadata, Tags
- Cache Manager / Memory, Disk
- File Manager / Import/Export
- iCloud Storage / CloudKit, Sync

#### Service Layer
- Image Processing / CoreImage, Metal
- Video Processing / AVFoundation
- ML Services / Vision, CoreML
- Network / URLSession
- Notification / Push, Local
- Analytics / Events, Metrics

#### Core Components
- Dependency Injection / Container, Resolver
- Error Handling / Logger, Recovery
- Configuration / Settings, Defaults
- Extensions / Utilities, Helpers
- Resources / Assets, Localization

**通信方式：** Combine/RxSwift for reactive bindings | Protocol-oriented interfaces | Dependency injection

## 2. 详细技术选型

### 核心框架选择

#### UI框架：混合方案（UIKit为主 + SwiftUI增强）
**理由：**
- UIKit提供成熟的集合视图性能优化（UICollectionView）
- 复杂手势和转场动画在UIKit中更易实现
- SwiftUI用于简单界面和设置页面，提高开发效率
- 渐进式迁移策略，新功能优先使用SwiftUI

#### 数据存储：PhotoKit + Core Data + FileManager
**理由：**
- **PhotoKit**：系统原生支持，直接访问相册资源
- **Core Data**：存储自定义元数据（标签、分组、编辑历史）
- **FileManager**：缓存管理和临时文件处理

#### 设计模式：MVVM-C (MVVM + Coordinator)
**理由：**
- 清晰的职责分离，便于AI工具理解和生成代码
- Coordinator处理复杂导航逻辑
- ViewModel隔离业务逻辑，提高可测试性
- 支持响应式编程（Combine）

### 关键技术栈

```swift
// 核心依赖
- Swift 5.9+ (async/await, Actors)
- Combine (响应式编程)
- PhotoKit (PHPhotoLibrary, PHImageManager)
- Core Data (持久化)
- AVFoundation (视频处理)
- CoreImage/Metal (图像处理)
- Vision Framework (图像识别)

// 建议的第三方库（最小化依赖）
- Kingfisher/SDWebImage (仅用于网络图片)
- SnapKit (约束管理，可选)
- SwiftLint (代码规范)
```

### 架构决策矩阵

| 技术选择 | 方案 | 优势 | 劣势 | 决策理由 |
|---------|-----|-----|-----|---------|
| 并发模型 | Swift Concurrency | 现代化、类型安全 | 需要iOS 15+ | 符合最低版本要求，简化异步代码 |
| 缓存策略 | 二级缓存 | 性能优秀 | 复杂度高 | 处理大量图片必需 |
| 依赖注入 | 手动DI + Container | 灵活、无魔法 | 需要手写代码 | AI工具易理解和生成 |
| 图片加载 | PHCachingImageManager | 系统优化 | API复杂 | 原生性能最佳 |

## 3. 核心数据模型设计

### 数据模型ER图

```mermaid
erDiagram
    PHAsset ||--o{ AssetMetadata : "has"
    PHAsset ||--o{ EditHistory : "has"
    PHAsset }o--o{ UserAlbum : "contains"
    PHAsset }o--o{ Tag : "tagged with"

    PHCollection ||--o{ PHAsset : "contains"
    PHCollection ||--o{ CollectionMetadata : "has"

    UserAlbum ||--o{ AlbumMetadata : "has"
    UserAlbum }o--o{ Tag : "categorized by"

    Person ||--o{ PHAsset : "appears in"
    Location ||--o{ PHAsset : "taken at"

    CacheEntry ||--o{ PHAsset : "caches"
    SyncRecord ||--o{ PHAsset : "tracks"

    PHAsset {
        string localIdentifier PK
        string uniformTypeIdentifier
        date creationDate
        date modificationDate
        int pixelWidth
        int pixelHeight
        double duration
        int mediaType
        int mediaSubtypes
        bool isFavorite
        bool isHidden
        location location
    }

    AssetMetadata {
        string assetId PK,FK
        string userDescription
        int viewCount
        date lastViewedDate
        bool isEdited
        string originalChecksum
        int fileSize
        json extendedMetadata
    }

    EditHistory {
        string historyId PK
        string assetId FK
        date editDate
        string editType
        json adjustments
        data previewData
        bool isCurrentVersion
    }

    PHCollection {
        string localIdentifier PK
        string localizedTitle
        int collectionType
        int collectionSubtype
        date startDate
        date endDate
        int assetCount
    }

    CollectionMetadata {
        string collectionId PK,FK
        string customTitle
        int sortOrder
        bool isPinned
        date lastModifiedDate
        string coverAssetId
    }

    UserAlbum {
        string albumId PK
        string title
        string description
        date createdDate
        date modifiedDate
        int assetCount
        bool isShared
        int sortOrder
    }

    AlbumMetadata {
        string albumId PK,FK
        string coverAssetId
        json sharingSettings
        json privacySettings
        string syncStatus
    }

    Tag {
        string tagId PK
        string name
        string category
        int usageCount
        date createdDate
        string color
    }

    Person {
        string personId PK
        string name
        int assetCount
        data faceprint
        bool isVerified
        date lastSeenDate
    }

    Location {
        string locationId PK
        string placeName
        double latitude
        double longitude
        string address
        int assetCount
        string placeCategory
    }

    CacheEntry {
        string cacheKey PK
        string assetId FK
        int targetSize
        int quality
        data imageData
        date createdDate
        date lastAccessDate
        int accessCount
        int byteSize
    }

    SyncRecord {
        string syncId PK
        string assetId FK
        string syncStatus
        date lastSyncDate
        string cloudIdentifier
        bool needsUpload
        bool needsDownload
        string errorMessage
    }
```

### 数据流向设计

```mermaid
flowchart TB
    subgraph "User Interface"
        UI[UI Components]
        VM[ViewModels]
        C[Coordinators]
    end

    subgraph "Business Logic"
        MM[MediaManager]
        EM[EditManager]
        SM[SyncManager]
        SE[SearchEngine]
    end

    subgraph "Data Access"
        PK[PhotoKit API]
        CD[Core Data]
        CM[CacheManager]
        FM[FileManager]
    end

    subgraph "Services"
        IP[ImageProcessor]
        VP[VideoProcessor]
        ML[ML Services]
        NET[Network]
    end

    subgraph "Storage"
        MEM[Memory Cache]
        DISK[Disk Cache]
        DB[(Database)]
        CLOUD[iCloud]
        PHOTOS[(Photos Library)]
    end

    %% UI Layer flows
    UI --> |User Actions| VM
    VM --> |State Updates| UI
    VM --> |Business Commands| MM
    VM --> |Navigation| C
    C --> |Route Changes| UI

    %% Business Logic flows
    MM --> |Fetch Assets| PK
    MM --> |Cache Requests| CM
    MM --> |Metadata| CD

    EM --> |Process Images| IP
    EM --> |Process Videos| VP
    EM --> |Save Edits| CD
    EM --> |Update Assets| PK

    SM --> |Sync Status| CD
    SM --> |Upload/Download| NET
    SM --> |Cloud Operations| CLOUD

    SE --> |Index Assets| ML
    SE --> |Query| CD
    SE --> |Analyze| IP

    %% Data Access flows
    PK --> |Read/Write| PHOTOS
    CD --> |Persist| DB
    CM --> |L1 Cache| MEM
    CM --> |L2 Cache| DISK
    FM --> |Temp Files| DISK

    %% Service flows
    IP --> |GPU Processing| MEM
    VP --> |Transcode| DISK
    ML --> |Models| DISK
    NET --> |API Calls| CLOUD

    %% Cache Strategy
    CM -.->|Eviction Policy| MEM
    MEM -.->|Overflow| DISK
    DISK -.->|Fetch| PHOTOS

    %% Sync flows
    PHOTOS -.->|Changes| SM
    SM -.->|Sync| CLOUD
    CLOUD -.->|Updates| SM
    SM -.->|Apply| PHOTOS
```

### 缓存策略和存储方案

#### 轻量级两级缓存架构（修正版）

```swift
// L1: 内存缓存 (NSCache) - 仅缓存当前会话
- 容量: 100MB (动态调整)
- 策略: LRU
- 存储: 当前可见和最近查看的解码图片
- 生命周期: App运行期间

// L2: 系统缓存 (PHCachingImageManager) - 利用系统优化
- 容量: 系统自动管理
- 策略: 预加载可见范围±1屏幕高度（减少预加载范围）
- 存储: 系统优化的缩略图
- 生命周期: 系统决定

// 移除磁盘缓存，原因：
// 1. Photos App主要展示本地相册，无需持久缓存
// 2. PHCachingImageManager已提供高效的系统级缓存
// 3. 大幅减少存储占用，符合轻量化目标
```

#### 优化后的缓存实现

```swift
final class LightweightCacheManager {
    static let shared = LightweightCacheManager()

    private let memoryCache = NSCache<NSString, UIImage>()
    private let imageManager = PHCachingImageManager()

    private init() {
        setupMemoryCache()
        observeMemoryWarnings()
    }

    private func setupMemoryCache() {
        let memoryCapacity = ProcessInfo.processInfo.physicalMemory
        let maxCacheSize = min(memoryCapacity / 10, 50 * 1024 * 1024) // 最大50MB

        memoryCache.totalCostLimit = Int(maxCacheSize)
        memoryCache.countLimit = 200 // 最多缓存200张图片
    }

    // 简化的图片请求，直接使用PHImageManager，避免额外的磁盘I/O
    func requestImage(
        for asset: PHAsset,
        targetSize: CGSize,
        completion: @escaping (UIImage?) -> Void
    ) {
        let key = cacheKey(for: asset, size: targetSize)

        // 仅检查内存缓存
        if let cachedImage = memoryCache.object(forKey: key as NSString) {
            completion(cachedImage)
            return
        }

        // 使用系统优化的图片请求
        let options = PHImageRequestOptions()
        options.isNetworkAccessAllowed = false // 避免自动下载iCloud图片
        options.deliveryMode = .opportunistic // 快速返回低质量，然后高质量

        imageManager.requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        ) { [weak self] image, info in
            if let image = image,
               info?[PHImageResultIsDegradedKey] as? Bool != true {
                // 仅缓存高质量图片到内存
                self?.memoryCache.setObject(
                    image,
                    forKey: key as NSString,
                    cost: Int(targetSize.width * targetSize.height * 4)
                )
            }
            completion(image)
        }
    }
}
```

#### 存储方案详情

```swift
// 1. 元数据存储 (Core Data)
- 自定义标签、分组
- 编辑历史记录
- 查看统计信息
- 同步状态追踪

// 2. 缓存键设计
CacheKey = AssetID + TargetSize + ContentMode + Quality
示例: "asset123_200x200_fill_high"

// 3. 预加载策略
- 可见区域前后各预加载20个项目
- 根据滚动速度动态调整预加载范围
- 优先加载缩略图，延迟加载高清图

// 4. 内存警告处理
- Level 1: 清理未使用超过60秒的缓存
- Level 2: 保留当前可见项，清理其他所有缓存
- Level 3: 清理所有缓存，仅保留必要UI资源
```

## 4. 关键模块详细设计

### 4.1 MediaManager 模块

#### MediaManager 类图

```mermaid
classDiagram
    class MediaManager {
        -photoLibrary: PHPhotoLibrary
        -cachingImageManager: PHCachingImageManager
        -fetchResults: [PHFetchResult]
        -observers: [MediaObserver]
        +shared: MediaManager

        +requestAuthorization() async throws
        +fetchAllPhotos() PHFetchResult~PHAsset~
        +fetchAlbums(type: AlbumType) [Album]
        +fetchAssets(in album: Album) PHFetchResult~PHAsset~
        +createAlbum(title: String) async throws Album
        +deleteAlbum(album: Album) async throws
        +addAssets(assets: [PHAsset], to album: Album) async throws
        +removeAssets(assets: [PHAsset], from album: Album) async throws
        +favoriteAssets(assets: [PHAsset]) async throws
        +deleteAssets(assets: [PHAsset]) async throws
        +startCaching(for assets: [PHAsset], targetSize: CGSize)
        +stopCaching(for assets: [PHAsset], targetSize: CGSize)
        +addObserver(observer: MediaObserver)
        +removeObserver(observer: MediaObserver)
    }

    class Album {
        +identifier: String
        +title: String
        +type: AlbumType
        +assetCount: Int
        +keyAsset: PHAsset?
        +collection: PHAssetCollection

        +fetchAssets() PHFetchResult~PHAsset~
        +updateTitle(title: String) async throws
        +getCoverImage(size: CGSize) async UIImage?
    }

    class MediaObserver {
        <<protocol>>
        +mediaDidChange(changeInfo: PHChange)
        +authorizationStatusDidChange(status: PHAuthorizationStatus)
    }

    class AlbumType {
        <<enumeration>>
        SMART_ALBUM
        USER_ALBUM
        SHARED_ALBUM
        MOMENT
    }

    class ImageRequest {
        +asset: PHAsset
        +targetSize: CGSize
        +contentMode: PHImageContentMode
        +options: PHImageRequestOptions
        +resultHandler: (UIImage?, [AnyHashable: Any]?) -> Void

        +execute() PHImageRequestID
        +cancel(requestID: PHImageRequestID)
    }

    class VideoRequest {
        +asset: PHAsset
        +options: PHVideoRequestOptions
        +resultHandler: (AVAsset?, AVAudioMix?, [AnyHashable: Any]?) -> Void

        +execute() PHImageRequestID
        +cancel(requestID: PHImageRequestID)
    }

    class AssetLoader {
        -imageManager: PHImageManager
        -requestQueue: DispatchQueue
        -activeRequests: [Int: Any]

        +loadImage(request: ImageRequest) async throws UIImage
        +loadVideo(request: VideoRequest) async throws AVAsset
        +preloadImages(assets: [PHAsset], size: CGSize)
        +cancelAllRequests()
    }

    class AssetChangeRequest {
        +creationRequest(for image: UIImage) PHAssetChangeRequest
        +creationRequest(for video: URL) PHAssetChangeRequest
        +deleteAssets(assets: [PHAsset])
        +favoriteAssets(assets: [PHAsset], favorite: Bool)
        +hideAssets(assets: [PHAsset], hidden: Bool)
    }

    MediaManager --> Album : manages
    MediaManager --> MediaObserver : notifies
    MediaManager --> AssetLoader : uses
    MediaManager --> AssetChangeRequest : performs
    Album --> AlbumType : has
    AssetLoader --> ImageRequest : processes
    AssetLoader --> VideoRequest : processes
```

### MediaManager 主要接口定义

```swift
// MediaManager 核心协议
protocol MediaManaging {
    // 权限管理
    func requestAuthorization() async throws -> PHAuthorizationStatus
    func checkAuthorizationStatus() -> PHAuthorizationStatus
    
    // 相册操作
    func fetchAlbums(ofType type: AlbumType) -> [Album]
    func createAlbum(title: String) async throws -> Album
    func deleteAlbum(_ album: Album) async throws
    func renameAlbum(_ album: Album, to title: String) async throws
    
    // 资源操作
    func fetchAssets(in album: Album?, 
                    sortBy: SortDescriptor,
                    filter: AssetFilter?) -> PHFetchResult<PHAsset>
    func addAssets(_ assets: [PHAsset], to album: Album) async throws
    func removeAssets(_ assets: [PHAsset], from album: Album) async throws
    func deleteAssets(_ assets: [PHAsset]) async throws
    
    // 批量操作
    func batchUpdate(_ assets: [PHAsset], 
                    operations: [AssetOperation]) async throws
    
    // 观察者模式
    func addObserver(_ observer: MediaObserver)
    func removeObserver(_ observer: MediaObserver)
}

// 资源加载协议
protocol AssetLoading {
    func loadImage(for asset: PHAsset,
                  targetSize: CGSize,
                  contentMode: PHImageContentMode,
                  options: ImageLoadingOptions?) async throws -> UIImage
    
    func loadVideo(for asset: PHAsset,
                  options: VideoLoadingOptions?) async throws -> AVAsset
    
    func cancelLoading(requestID: PHImageRequestID)
    func preloadAssets(_ assets: [PHAsset], targetSize: CGSize)
}
```

### 4.2 缩略图生成算法

```swift
// 高性能缩略图生成策略
class ThumbnailGenerator {
    private let imageManager = PHCachingImageManager()
    private let processingQueue = DispatchQueue(label: "thumbnail.processing", 
                                               qos: .userInitiated, 
                                               attributes: .concurrent)
    
    // 智能尺寸计算
    func calculateOptimalSize(for cellSize: CGSize, 
                            scale: CGFloat = UIScreen.main.scale) -> CGSize {
        // 考虑屏幕密度，生成略大于显示尺寸的缩略图
        let targetSize = CGSize(
            width: cellSize.width * scale * 1.5,
            height: cellSize.height * scale * 1.5
        )
        
        // 对齐到16的倍数（GPU友好）
        return CGSize(
            width: ceil(targetSize.width / 16) * 16,
            height: ceil(targetSize.height / 16) * 16
        )
    }
    
    // 批量生成缩略图
    func generateThumbnails(
        for assets: [PHAsset],
        targetSize: CGSize,
        completion: @escaping ([String: UIImage]) -> Void
    ) {
        let options = PHImageRequestOptions()
        options.isSynchronous = false
        options.deliveryMode = .opportunistic
        options.resizeMode = .fast
        options.isNetworkAccessAllowed = false // 首次不下载
        
        var thumbnails: [String: UIImage] = [:]
        let group = DispatchGroup()
        
        for asset in assets {
            group.enter()
            
            imageManager.requestImage(
                for: asset,
                targetSize: targetSize,
                contentMode: .aspectFill,
                options: options
            ) { image, info in
                if let image = image {
                    thumbnails[asset.localIdentifier] = image
                }
                group.leave()
            }
        }
        
        group.notify(queue: .main) {
            completion(thumbnails)
        }
    }
}
```

### 4.3 预加载策略

```swift
// 智能预加载管理器
class PreloadManager {
    private let cachingImageManager = PHCachingImageManager()
    private var previousPreheatRect = CGRect.zero
    private let preheatRectScale: CGFloat = 2.0 // 预加载屏幕高度的2倍
    
    func updateVisibleArea(
        collectionView: UICollectionView,
        assets: PHFetchResult<PHAsset>,
        targetSize: CGSize
    ) {
        let visibleRect = CGRect(
            origin: collectionView.contentOffset,
            size: collectionView.bounds.size
        )
        
        let preheatRect = visibleRect.insetBy(
            dx: 0,
            dy: -visibleRect.height * preheatRectScale
        )
        
        let delta = abs(preheatRect.midY - previousPreheatRect.midY)
        if delta > collectionView.bounds.height / 3 {
            
            // 计算需要预加载和停止缓存的资源
            let (addedAssets, removedAssets) = calculateAssetsToCache(
                previousRect: previousPreheatRect,
                currentRect: preheatRect,
                allAssets: assets,
                collectionView: collectionView
            )
            
            // 更新缓存
            if !removedAssets.isEmpty {
                cachingImageManager.stopCachingImages(
                    for: removedAssets,
                    targetSize: targetSize,
                    contentMode: .aspectFill,
                    options: nil
                )
            }
            
            if !addedAssets.isEmpty {
                cachingImageManager.startCachingImages(
                    for: addedAssets,
                    targetSize: targetSize,
                    contentMode: .aspectFill,
                    options: nil
                )
            }
            
            previousPreheatRect = preheatRect
        }
    }
}
```

## 5. 性能优化策略

### 5.1 内存管理方案

```swift
// 内存管理器
class MemoryManager {
    static let shared = MemoryManager()
    
    private var memoryWarningObserver: NSObjectProtocol?
    private let memoryCache = NSCache<NSString, UIImage>()
    
    init() {
        setupMemoryCache()
        observeMemoryWarnings()
    }
    
    private func setupMemoryCache() {
        // 动态设置缓存大小
        let memoryCapacity = ProcessInfo.processInfo.physicalMemory
        let cacheSize = min(memoryCapacity / 4, 200 * 1024 * 1024) // 最大200MB
        
        memoryCache.totalCostLimit = Int(cacheSize)
        memoryCache.countLimit = 1000 // 最多1000张图片
    }
    
    private func observeMemoryWarnings() {
        memoryWarningObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleMemoryWarning()
        }
    }
    
    private func handleMemoryWarning() {
        // 分级清理策略
        let usedMemory = getUsedMemory()
        let totalMemory = ProcessInfo.processInfo.physicalMemory
        let memoryPressure = Double(usedMemory) / Double(totalMemory)
        
        switch memoryPressure {
        case 0.7..<0.85:
            // 轻度压力：清理30%缓存
            memoryCache.removeObjects(olderThan: 60)
        case 0.85..<0.95:
            // 中度压力：清理70%缓存
            memoryCache.removeObjects(keepingVisible: true)
        case 0.95...:
            // 重度压力：清理所有缓存
            memoryCache.removeAllObjects()
            URLCache.shared.removeAllCachedResponses()
        default:
            break
        }
    }
}
```

### 5.2 图片加载和缓存机制

#### 图片加载流程图

```mermaid
flowchart TD
    Start([图片请求]) --> CheckMemory{检查内存缓存}

    CheckMemory -->|命中| ReturnMemory[返回缓存图片]
    CheckMemory -->|未命中| CheckDisk{检查磁盘缓存}

    CheckDisk -->|命中| LoadFromDisk[从磁盘加载]
    CheckDisk -->|未命中| CheckiCloud{检查是否在iCloud}

    LoadFromDisk --> Decode[解码图片]
    Decode --> SaveMemory[存入内存缓存]
    SaveMemory --> Return1[返回图片]

    CheckiCloud -->|在本地| LoadFromPhotos[从相册加载]
    CheckiCloud -->|仅在云端| ShowPlaceholder[显示占位图]

    ShowPlaceholder --> DownloadFromCloud[后台下载]
    DownloadFromCloud --> LoadFromPhotos

    LoadFromPhotos --> ProcessImage[处理图片]
    ProcessImage --> Resize[调整尺寸]
    Resize --> Compress[压缩优化]
    Compress --> SaveDisk[存入磁盘缓存]
    SaveDisk --> SaveMemory2[存入内存缓存]
    SaveMemory2 --> Return2[返回图片]

    ReturnMemory --> UpdateUI[更新UI]
    Return1 --> UpdateUI
    Return2 --> UpdateUI
```

### 5.3 大数据量处理策略

```swift
// 大数据集优化处理器
class LargeDatasetHandler {
    // 分页加载配置
    struct PagingConfiguration {
        let pageSize: Int = 100
        let prefetchThreshold: Int = 20
        let maxConcurrentOperations: Int = 4
    }
    
    // 虚拟化滚动
    func setupVirtualizedScrolling(for collectionView: UICollectionView) {
        // 使用 UICollectionViewDataSourcePrefetching
        collectionView.isPrefetchingEnabled = true
        collectionView.prefetchDataSource = self
        
        // 优化 Collection View 配置
        if let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
            layout.minimumLineSpacing = 1
            layout.minimumInteritemSpacing = 1
            // 预估大小可以提升性能
            layout.estimatedItemSize = .zero
        }
    }
    
    // 差量更新算法
    func performBatchUpdates(
        oldAssets: PHFetchResult<PHAsset>,
        newAssets: PHFetchResult<PHAsset>,
        changeDetails: PHFetchResultChangeDetails<PHAsset>,
        collectionView: UICollectionView
    ) {
        guard changeDetails.hasChanges else { return }
        
        collectionView.performBatchUpdates({
            // 处理删除
            if let removed = changeDetails.removedIndexes, !removed.isEmpty {
                let indexPaths = removed.map { IndexPath(item: $0, section: 0) }
                collectionView.deleteItems(at: indexPaths)
            }
            
            // 处理插入
            if let inserted = changeDetails.insertedIndexes, !inserted.isEmpty {
                let indexPaths = inserted.map { IndexPath(item: $0, section: 0) }
                collectionView.insertItems(at: indexPaths)
            }
            
            // 处理移动
            changeDetails.enumerateMoves { fromIndex, toIndex in
                collectionView.moveItem(
                    at: IndexPath(item: fromIndex, section: 0),
                    to: IndexPath(item: toIndex, section: 0)
                )
            }
            
            // 处理更新
            if let changed = changeDetails.changedIndexes, !changed.isEmpty {
                let indexPaths = changed.map { IndexPath(item: $0, section: 0) }
                collectionView.reloadItems(at: indexPaths)
            }
        })
    }
}

// 索引优化
class AssetIndexer {
    private let indexQueue = DispatchQueue(label: "asset.indexing", qos: .background)
    private var indexCache: [String: Set<PHAsset>] = [:]
    
    func buildIndex(for assets: PHFetchResult<PHAsset>) async {
        await withCheckedContinuation { continuation in
            indexQueue.async { [weak self] in
                guard let self = self else { return }
                
                // 并行构建多个索引
                let dateIndex = self.buildDateIndex(assets)
                let typeIndex = self.buildTypeIndex(assets)
                let sizeIndex = self.buildSizeIndex(assets)
                
                // 合并索引结果
                self.indexCache = [
                    "date": dateIndex,
                    "type": typeIndex,
                    "size": sizeIndex
                ]
                
                continuation.resume()
            }
        }
    }
}
```

### 5.4 UI响应性保证方案

```swift
// UI 响应性管理器
class UIResponsivenessManager {
    private let mainQueue = DispatchQueue.main
    private let renderQueue = DispatchQueue(label: "ui.render", qos: .userInteractive)
    
    // 帧率监控
    private var displayLink: CADisplayLink?
    private var frameDropHandler: ((Int) -> Void)?
    
    func startMonitoring() {
        displayLink = CADisplayLink(target: self, selector: #selector(tick))
        displayLink?.add(to: .main, forMode: .common)
    }
    
    // 优先级任务调度
    func scheduleUIUpdate(
        priority: TaskPriority,
        task: @escaping () -> Void
    ) {
        switch priority {
        case .immediate:
            mainQueue.async(execute: task)
            
        case .high:
            mainQueue.asyncAfter(deadline: .now() + 0.016) { // 下一帧
                task()
            }
            
        case .normal:
            mainQueue.asyncAfter(deadline: .now() + 0.1) {
                task()
            }
            
        case .low:
            mainQueue.async(qos: .background) {
                task()
            }
        }
    }
    
    // 滚动优化
    func optimizeForScrolling(scrollView: UIScrollView) {
        // 监听滚动状态
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(scrollViewWillBeginDragging),
            name: .scrollViewWillBeginDragging,
            object: scrollView
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(scrollViewDidEndDecelerating),
            name: .scrollViewDidEndDecelerating,
            object: scrollView
        )
    }
    
    @objc private func scrollViewWillBeginDragging() {
        // 滚动开始，降低图片质量，提高响应速度
        ImageLoadingOptions.shared.deliveryMode = .fastFormat
        
        // 暂停非关键任务
        pauseNonCriticalTasks()
    }
    
    @objc private func scrollViewDidEndDecelerating() {
        // 滚动结束，恢复高质量图片
        ImageLoadingOptions.shared.deliveryMode = .highQualityFormat
        
        // 恢复任务
        resumeNonCriticalTasks()
    }
}
```

## 6. 开发实施路线图

### Phase 1: 基础架构和核心数据层（2-3周）

| 任务 | 描述 | 工作量 | 前置依赖 | 验收标准 |
|-----|-----|--------|---------|---------|
| 1.1 项目初始化 | 创建Xcode项目，配置基础设置 | 0.5天 | 无 | 项目可编译运行 |
| 1.2 依赖注入容器 | 实现DI容器和服务注册 | 1天 | 1.1 | 单元测试通过 |
| 1.3 网络层基础 | 封装URLSession，错误处理 | 1天 | 1.1 | API请求测试通过 |
| 1.4 PhotoKit封装 | MediaManager基础实现 | 3天 | 1.1 | 可获取相册列表 |
| 1.5 Core Data设置 | 数据模型和迁移机制 | 2天 | 1.1 | CRUD操作正常 |
| 1.6 缓存系统 | 三级缓存实现 | 3天 | 1.4 | 缓存命中测试通过 |
| 1.7 日志系统 | 统一日志和错误上报 | 1天 | 1.1 | 日志输出正常 |
| 1.8 权限管理 | 相册权限请求和处理 | 1天 | 1.4 | 权限流程完整 |

### Phase 2: 基础UI和媒体浏览功能（3-4周）

| 任务 | 描述 | 工作量 | 前置依赖 | 验收标准 |
|-----|-----|--------|---------|---------|
| 2.1 主界面框架 | TabBar和导航结构 | 1天 | Phase 1 | 界面切换流畅 |
| 2.2 相册列表页 | 系统相册和用户相册展示 | 2天 | 2.1, 1.4 | 相册列表完整 |
| 2.3 照片网格视图 | CollectionView实现 | 3天 | 2.1, 1.6 | 滑动流畅 |
| 2.4 图片浏览器 | 全屏浏览，手势交互 | 3天 | 2.3 | 手势响应准确 |
| 2.5 视频播放器 | AVPlayer集成 | 2天 | 2.4 | 视频播放正常 |
| 2.6 缩略图系统 | 动态生成和缓存 | 2天 | 1.6, 2.3 | 缩略图加载快速 |
| 2.7 预加载优化 | 滚动预加载逻辑 | 2天 | 2.6 | 无明显卡顿 |
| 2.8 转场动画 | 自定义转场效果 | 2天 | 2.4 | 动画流畅自然 |

### Phase 3: 编辑和管理功能（3-4周）

| 任务 | 描述 | 工作量 | 前置依赖 | 验收标准 |
|-----|-----|--------|---------|---------|
| 3.1 选择模式 | 多选和批量操作UI | 2天 | Phase 2 | 选择交互完整 |
| 3.2 基础编辑器 | 裁剪、旋转功能 | 3天 | 2.4 | 编辑操作准确 |
| 3.3 滤镜系统 | CoreImage滤镜集成 | 2天 | 3.2 | 滤镜效果正确 |
| 3.4 调整工具 | 亮度、对比度等调节 | 2天 | 3.2 | 调节实时预览 |
| 3.5 编辑历史 | 撤销/重做机制 | 2天 | 3.2, 1.5 | 历史记录完整 |
| 3.6 相册管理 | 创建、删除、重命名 | 2天 | 2.2, 1.4 | 管理功能完整 |
| 3.7 资源移动 | 相册间移动照片 | 1天 | 3.6 | 移动操作正确 |
| 3.8 批量操作 | 批量删除、移动等 | 2天 | 3.1, 3.6 | 批量操作高效 |

### Phase 4: 高级功能和优化（2-3周）

| 任务 | 描述 | 工作量 | 前置依赖 | 验收标准 |
|-----|-----|--------|---------|---------|
| 4.1 搜索基础 | 搜索UI和基础逻辑 | 2天 | Phase 3 | 搜索响应快速 |
| 4.2 分享功能 | 系统分享和AirDrop | 1天 | Phase 2 | 分享功能正常 |
| 4.3 iCloud同步 | 同步状态显示 | 3天 | 1.4 | 同步状态准确 |
| 4.4 性能优化 | 内存和CPU优化 | 3天 | All | 性能指标达标 |
| 4.5 深色模式 | UI适配深色模式 | 2天 | All UI | 主题切换正常 |
| 4.6 辅助功能 | VoiceOver支持 | 2天 | All UI | 无障碍测试通过 |
| 4.7 单元测试 | 核心功能测试覆盖 | 3天 | All | 覆盖率>70% |
| 4.8 集成测试 | 端到端测试 | 2天 | 4.7 | 主流程测试通过 |

## 7. Claude Code开发指导

### 7.1 推荐的文件和目录结构

```
PhotosApp/
├── App/
│   ├── AppDelegate.swift
│   ├── SceneDelegate.swift
│   ├── Configuration/
│   │   ├── Info.plist
│   │   └── Config.swift
│   └── Resources/
│       ├── Assets.xcassets
│       ├── LaunchScreen.storyboard
│       └── Localizable.strings
│
├── Core/
│   ├── DI/
│   │   ├── Container.swift
│   │   └── ServiceRegistry.swift
│   ├── Extensions/
│   │   ├── UIKit+Extensions.swift
│   │   ├── Foundation+Extensions.swift
│   │   └── PhotoKit+Extensions.swift
│   ├── Utilities/
│   │   ├── Logger.swift
│   │   ├── Constants.swift
│   │   └── Helpers.swift
│   └── Base/
│       ├── BaseViewController.swift
│       └── BaseViewModel.swift
│
├── Data/
│   ├── Models/
│   │   ├── Album.swift
│   │   ├── Asset.swift
│   │   └── EditHistory.swift
│   ├── CoreData/
│   │   ├── PhotosApp.xcdatamodeld
│   │   ├── CoreDataStack.swift
│   │   └── Migrations/
│   ├── Cache/
│   │   ├── CacheManager.swift
│   │   ├── MemoryCache.swift
│   │   └── DiskCache.swift
│   └── Repositories/
│       ├── MediaRepository.swift
│       ├── AlbumRepository.swift
│       └── SettingsRepository.swift
│
├── Domain/
│   ├── UseCases/
│   │   ├── FetchAlbumsUseCase.swift
│   │   ├── EditPhotoUseCase.swift
│   │   └── ShareMediaUseCase.swift
│   ├── Services/
│   │   ├── MediaService.swift
│   │   ├── EditingService.swift
│   │   └── SyncService.swift
│   └── Managers/
│       ├── MediaManager.swift
│       ├── PermissionManager.swift
│       └── CacheManager.swift
│
├── Presentation/
│   ├── Coordinators/
│   │   ├── AppCoordinator.swift
│   │   ├── AlbumsCoordinator.swift
│   │   └── MediaCoordinator.swift
│   ├── Scenes/
│   │   ├── Albums/
│   │   │   ├── AlbumsViewController.swift
│   │   │   ├── AlbumsViewModel.swift
│   │   │   └── Views/
│   │   ├── MediaGrid/
│   │   │   ├── MediaGridViewController.swift
│   │   │   ├── MediaGridViewModel.swift
│   │   │   └── Cells/
│   │   ├── MediaViewer/
│   │   │   ├── MediaViewerViewController.swift
│   │   │   ├── MediaViewerViewModel.swift
│   │   │   └── Transitions/
│   │   └── Editor/
│   │       ├── EditorViewController.swift
│   │       ├── EditorViewModel.swift
│   │       └── Tools/
│   └── Common/
│       ├── Views/
│       ├── Animations/
│       └── Themes/
│
└── Tests/
    ├── UnitTests/
    │   ├── ViewModels/
    │   ├── UseCases/
    │   └── Services/
    ├── IntegrationTests/
    └── UITests/
```

### 7.2 代码规范建议

```swift
// MARK: - 命名规范
/*
1. 类型名使用 PascalCase
2. 变量和函数使用 camelCase
3. 常量使用 SCREAMING_SNAKE_CASE 或 camelCase
4. 协议名通常以 -ing, -able, -Protocol 结尾
5. 泛型类型参数使用单个大写字母或描述性名称
*/

// MARK: - 代码组织
/*
1. 使用 MARK: - 分隔代码段
2. 属性 > 初始化 > 生命周期 > 公开方法 > 私有方法
3. 扩展按功能分组
4. 每个文件只包含一个主要类型
*/

// MARK: - Swift 特性使用
/*
1. 优先使用 let 而非 var
2. 使用 guard 进行早期退出
3. 避免强制解包，使用 if let 或 guard let
4. 使用 Swift 的类型推断
5. 利用 async/await 处理异步
*/

// MARK: - 注释规范
/// 使用三斜线注释公开 API
/// - Parameters:
///   - asset: 要加载的资源
///   - size: 目标尺寸
/// - Returns: 加载的图片
/// - Throws: 加载失败时抛出错误

// MARK: - 错误处理
enum PhotosAppError: LocalizedError {
    case unauthorized
    case assetNotFound
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .unauthorized:
            return "需要相册访问权限"
        case .assetNotFound:
            return "找不到指定的照片"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        }
    }
}
```

### 7.3 关键组件代码骨架示例### 7.4 单元测试策略

```swift
// MARK: - 测试策略概述
/*
1. 测试覆盖率目标: 核心业务逻辑 > 80%, 整体 > 70%
2. 测试金字塔: 单元测试(70%) > 集成测试(20%) > UI测试(10%)
3. 测试驱动开发: 关键功能先写测试
4. Mock策略: 使用协议进行依赖注入，便于Mock
*/

// MARK: - 测试示例

import XCTest
@testable import PhotosApp

// ViewModel 测试示例
class AlbumsViewModelTests: XCTestCase {
    var sut: AlbumsViewModel!
    var mockMediaManager: MockMediaManager!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        mockMediaManager = MockMediaManager()
        sut = AlbumsViewModel(mediaManager: mockMediaManager)
        cancellables = []
    }
    
    override func tearDown() {
        sut = nil
        mockMediaManager = nil
        cancellables = nil
        super.tearDown()
    }
    
    func testFetchAlbumsSuccess() {
        // Given
        let expectedAlbums = [
            Album(identifier: "1", title: "Test Album 1", type: .user, collection: nil),
            Album(identifier: "2", title: "Test Album 2", type: .user, collection: nil)
        ]
        mockMediaManager.albumsToReturn = expectedAlbums
        
        let expectation = XCTestExpectation(description: "Albums fetched")
        
        // When
        sut.$albums
            .dropFirst() // Skip initial empty value
            .sink { albums in
                // Then
                XCTAssertEqual(albums.count, 2)
                XCTAssertEqual(albums.first?.title, "Test Album 1")
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        sut.fetchAlbums()
        
        wait(for: [expectation], timeout: 1.0)
        XCTAssertFalse(sut.isLoading)
        XCTAssertNil(sut.error)
    }
    
    func testFetchAlbumsFailure() {
        // Given
        mockMediaManager.shouldFail = true
        mockMediaManager.errorToThrow = PhotosAppError.unauthorized
        
        let expectation = XCTestExpectation(description: "Error received")
        
        // When
        sut.$error
            .compactMap { $0 }
            .sink { error in
                // Then
                XCTAssertEqual(error as? PhotosAppError, PhotosAppError.unauthorized)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        sut.fetchAlbums()
        
        wait(for: [expectation], timeout: 1.0)
        XCTAssertTrue(sut.albums.isEmpty)
    }
}

// Service 测试示例
class CacheManagerTests: XCTestCase {
    var sut: CacheManager!
    
    override func setUp() {
        super.setUp()
        sut = CacheManager.shared
    }
    
    override func tearDown() {
        Task {
            await sut.clearAll()
        }
        super.tearDown()
    }
    
    func testStoreAndRetrieveImage() async throws {
        // Given
        let testImage = UIImage(systemName: "photo")!
        let key = "test_image_key"
        
        // When
        await sut.store(testImage, for: key)
        let retrievedImage = await sut.image(for: key)
        
        // Then
        XCTAssertNotNil(retrievedImage)
        XCTAssertEqual(
            testImage.pngData()?.count,
            retrievedImage?.pngData()?.count
        )
    }
    
    func testRemoveImage() async throws {
        // Given
        let testImage = UIImage(systemName: "photo")!
        let key = "test_image_key"
        await sut.store(testImage, for: key)
        
        // When
        await sut.removeImage(for: key)
        let retrievedImage = await sut.image(for: key)
        
        // Then
        XCTAssertNil(retrievedImage)
    }
}

// Mock 示例
class MockMediaManager: MediaManaging {
    var albumsToReturn: [Album] = []
    var shouldFail = false
    var errorToThrow: Error = PhotosAppError.unauthorized
    var authorizationStatusToReturn: PHAuthorizationStatus = .authorized
    
    func requestAuthorization() async throws -> PHAuthorizationStatus {
        if shouldFail {
            throw errorToThrow
        }
        return authorizationStatusToReturn
    }
    
    func fetchAlbums(ofType type: AlbumType) -> AnyPublisher<[Album], Error> {
        if shouldFail {
            return Fail(error: errorToThrow)
                .eraseToAnyPublisher()
        }
        
        return Just(albumsToReturn)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func fetchAssets(in album: Album?) -> PHFetchResult<PHAsset> {
        // Return mock PHFetchResult
        return PHFetchResult<PHAsset>()
    }
}

// 性能测试示例
class PerformanceTests: XCTestCase {
    func testLargeDatasetScrollingPerformance() {
        measure {
            // 测试大数据集滚动性能
            let viewController = MediaGridViewController()
            viewController.loadViewIfNeeded()
            
            // 模拟滚动1000个项目
            for i in 0..<1000 {
                let indexPath = IndexPath(item: i, section: 0)
                _ = viewController.collectionView(
                    viewController.collectionView,
                    cellForItemAt: indexPath
                )
            }
        }
    }
    
    func testImageCachingPerformance() {
        let cacheManager = CacheManager.shared
        let testImage = UIImage(systemName: "photo")!
        
        measure {
            // 测试缓存性能
            let expectation = XCTestExpectation()
            
            Task {
                for i in 0..<100 {
                    await cacheManager.store(testImage, for: "key_\(i)")
                }
                
                for i in 0..<100 {
                    _ = await cacheManager.image(for: "key_\(i)")
                }
                
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 10)
        }
    }
}
```

### 7.5 AI工具开发提示模板

```markdown
# Claude Code 开发提示模板

## 基础组件开发模板
"请实现一个 [组件名称]，该组件需要：
1. 遵循 MVVM 架构模式
2. 使用 Combine 进行响应式绑定
3. 支持依赖注入
4. 包含完整的错误处理
5. 提供单元测试

具体要求：
- 输入：[描述输入参数]
- 输出：[描述输出结果]
- 依赖：[列出依赖的服务/管理器]
- 性能要求：[如有特殊性能要求]"

## UI 组件开发模板
"请创建一个 [UI组件名称]：
1. 支持 iOS 16.4+
2. 适配深色模式
3. 支持动态字体
4. 包含适当的动画效果
5. 遵循 Apple HIG 设计规范

UI 规格：
- 布局：[描述布局要求]
- 交互：[描述用户交互]
- 状态：[列出所有可能的状态]
- 辅助功能：[VoiceOver 等要求]"

## 性能优化模板
"请优化 [功能名称] 的性能：
1. 当前问题：[描述性能问题]
2. 性能指标：[目标FPS、内存使用等]
3. 数据规模：[预期处理的数据量]
4. 约束条件：[硬件限制、电池消耗等]

请提供：
- 优化前后的性能对比
- 具体的优化策略
- 可能的权衡和副作用"
```

## 架构设计总结

这个架构设计提供了一个高性能、可扩展的iOS Photos高仿App基础。主要特点：

1. **分层清晰**：UI、业务逻辑、数据访问、服务层职责明确
2. **性能优先**：三级缓存、智能预加载、差量更新
3. **易于开发**：MVVM+Coordinator模式，便于AI工具理解
4. **扩展友好**：预留了标签系统、批量操作、AI功能接口

建议开发时严格按照Phase进行，确保基础牢固。每个Phase完成后进行性能测试和代码审查，确保质量。

架构设计已经完整输出，您可以基于此开始使用Claude Code + Sonnet 4进行具体实现。如需要某个特定模块的更详细设计，请告诉我。

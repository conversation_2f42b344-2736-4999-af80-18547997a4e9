# MPhotos 重构计划 - 第一阶段：核心架构重构

## 📋 阶段概述

**目标**: 建立简洁的架构基础，移除过度复杂的实现
**时间**: 1-2周
**重点**: 简化服务层、标准化数据层、清理调试代码

## 🎯 具体重构目标

### 1. 简化服务层架构
- 移除复杂的渐进式加载系统
- 实现轻量级二级缓存策略
- 标准化图片加载流程
- 优化PHPhotoLibrary集成
- 实现观察者模式和依赖注入

### 2. 标准化数据层
- 简化数据模型结构
- 重构数据获取逻辑
- 实现完整的错误处理机制
- 添加内存监控和性能优化

### 3. 清理项目结构
- 移除Debug调试代码
- 清理Demo实验代码
- 移除过度复杂的监控工具
- 实现轻量级性能监控系统

## 🏗️ 重构前后架构对比

### 当前复杂架构
```
PhotoLibraryService → ProgressiveLoadingManager → 复杂多级缓存
                  → ImageQualityConfig → ViewportQualityManager
                  → GestureLayoutManager → 复杂手势逻辑
                  → CacheManager → MemoryCache + DiskCache + QualityCache
```

### 重构后简化架构
```
MediaManager → 标准图片加载 → 轻量级二级缓存
           → 观察者模式 → WeakRef内存管理
           → 依赖注入容器 → 协议导向设计
           → PerformanceMonitor → 实时性能监控
           → MemoryMonitor → 智能内存管理
```

## 📁 文件结构重组方案

### 需要删除的文件/目录
```
MPhotos/
├── Debug/                              # 完全删除
│   ├── CacheMonitor.swift
│   ├── ProgressiveLoadingVerification.swift
│   ├── SettingsInterfaceVerification.swift
│   ├── SystemErrorDiagnostics.swift
│   └── ThreadSafetyTest.swift
├── Demo/                               # 完全删除
│   └── ProgressiveLoadingDemo.swift
├── Implementation/                     # 清空或删除
├── Fixes/                             # 清空或删除
├── Core/Services/
│   └── ProgressiveLoadingManager.swift # 删除
└── Features/PhotoLibrary/Managers/
    ├── GestureLogger.swift            # 删除
    └── 其他复杂监控组件               # 删除
```

### 简化后的文件结构
```
MPhotos/
├── App/
│   ├── AppDelegate.swift
│   ├── SceneDelegate.swift
│   └── Config.xcconfig
├── Core/
│   ├── Architecture/
│   │   ├── BaseViewController.swift    # 保留并简化
│   │   ├── BaseViewModel.swift         # 保留并简化
│   │   ├── Coordinator.swift           # 保留
│   │   └── DIContainer.swift           # 新增依赖注入
│   ├── Services/
│   │   ├── MediaManager.swift          # 新增统一媒体管理
│   │   └── SettingsManager.swift       # 重构简化
│   ├── Cache/
│   │   └── LightweightCacheManager.swift # 新增轻量级缓存
│   ├── Models/
│   │   ├── Album.swift                 # 新增相册模型
│   │   ├── PhotosError.swift           # 新增错误类型
│   │   └── WeakRef.swift               # 新增弱引用工具
│   ├── Monitoring/
│   │   ├── MemoryMonitor.swift         # 新增内存监控
│   │   └── PerformanceMonitor.swift    # 新增性能监控
│   └── Utils/
│       └── Extensions/                 # 保留必要扩展
├── Features/
│   ├── MainTab/
│   │   └── MainTabBarController.swift
│   └── PhotoLibrary/
│       ├── Controllers/
│       │   ├── PhotoLibraryViewController.swift  # 重构简化
│       │   ├── PhotoDetailViewController.swift   # 保留
│       │   └── LibrarySettingsViewController.swift # 简化
│       ├── ViewModels/
│       │   └── PhotoLibraryViewModel.swift        # 重构
│       ├── Views/
│       │   ├── PhotoGridCell.swift               # 简化
│       │   └── DateSectionHeaderView.swift       # 保留
│       └── Managers/
│           ├── CompositionalLayoutManager.swift  # 简化
│           └── GestureLayoutManager.swift        # 简化
└── Resources/
    └── (保留所有资源文件)
```

## 💻 核心组件重构代码

### 1. 轻量级缓存管理器

```swift
import UIKit
import Photos

final class LightweightCacheManager {
    static let shared = LightweightCacheManager()

    private let memoryCache = NSCache<NSString, UIImage>()
    private let imageManager = PHCachingImageManager()
    private let processingQueue = DispatchQueue(label: "cache.processing", qos: .userInitiated)

    private init() {
        setupMemoryCache()
        observeMemoryWarnings()
    }

    private func setupMemoryCache() {
        let memoryCapacity = ProcessInfo.processInfo.physicalMemory
        let maxCacheSize = min(memoryCapacity / 10, 100 * 1024 * 1024) // 最大100MB

        memoryCache.totalCostLimit = Int(maxCacheSize)
        memoryCache.countLimit = 300 // 最多缓存300张图片
    }

    private func observeMemoryWarnings() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }

    @objc private func handleMemoryWarning() {
        memoryCache.removeAllObjects()
        imageManager.stopCachingImagesForAllAssets()
    }
    
    func requestImage(
        for asset: PHAsset,
        targetSize: CGSize,
        completion: @escaping (UIImage?) -> Void
    ) {
        let key = cacheKey(for: asset, size: targetSize)

        // 检查内存缓存
        if let cachedImage = memoryCache.object(forKey: key as NSString) {
            completion(cachedImage)
            return
        }

        // 从PhotoKit加载
        let options = PHImageRequestOptions()
        options.isNetworkAccessAllowed = false
        options.deliveryMode = .opportunistic
        options.resizeMode = .fast

        imageManager.requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        ) { [weak self] image, info in
            guard let image = image,
                  info?[PHImageResultIsDegradedKey] as? Bool != true else {
                completion(nil)
                return
            }

            // 缓存到内存
            let cost = Int(targetSize.width * targetSize.height * 4)
            self?.memoryCache.setObject(image, forKey: key as NSString, cost: cost)

            completion(image)
        }
    }
    
    func preloadImages(for assets: [PHAsset], targetSize: CGSize) {
        let assetsToPreload = Array(assets.prefix(50)) // 限制预加载数量

        imageManager.startCachingImages(
            for: assetsToPreload,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: nil
        )
    }

    func stopPreloading(for assets: [PHAsset], targetSize: CGSize) {
        imageManager.stopCachingImages(
            for: assets,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: nil
        )
    }

    private func cacheKey(for asset: PHAsset, size: CGSize) -> String {
        return "\(asset.localIdentifier)_\(Int(size.width))x\(Int(size.height))"
    }
}
```

### 2. 统一媒体管理器

```swift
import Photos
import Combine
import UIKit

// MARK: - 协议定义
protocol MediaManaging {
    func requestAuthorization() async throws -> PHAuthorizationStatus
    func fetchAlbums(ofType type: AlbumType) -> [Album]
    func fetchAssets(in album: Album?) -> PHFetchResult<PHAsset>
    func createAlbum(title: String) async throws -> Album
    func deleteAsset(_ asset: PHAsset) async throws
    func addObserver(_ observer: MediaObserver)
}

protocol MediaObserver: AnyObject {
    func mediaDidChange(changeInfo: PHChange)
}

// MARK: - MediaManager实现
final class MediaManager: NSObject, MediaManaging {
    static let shared = MediaManager()

    private let photoLibrary = PHPhotoLibrary.shared()
    private var observers: [WeakRef<MediaObserver>] = []

    private override init() {
        super.init()
        photoLibrary.register(self)
    }

    deinit {
        photoLibrary.unregisterChangeObserver(self)
    }
    
    func requestAuthorization() async throws -> PHAuthorizationStatus {
        return await withCheckedContinuation { continuation in
            PHPhotoLibrary.requestAuthorization(for: .readWrite) { status in
                continuation.resume(returning: status)
            }
        }
    }

    func fetchAlbums(ofType type: AlbumType) -> [Album] {
        var collections: PHFetchResult<PHAssetCollection>

        switch type {
        case .smartAlbum:
            collections = PHAssetCollection.fetchAssetCollections(
                with: .smartAlbum,
                subtype: .any,
                options: nil
            )
        case .userAlbum:
            collections = PHAssetCollection.fetchAssetCollections(
                with: .album,
                subtype: .any,
                options: nil
            )
        case .sharedAlbum:
            collections = PHAssetCollection.fetchAssetCollections(
                with: .album,
                subtype: .albumCloudShared,
                options: nil
            )
        }

        var albums: [Album] = []
        collections.enumerateObjects { collection, _, _ in
            let assetCount = PHAsset.fetchAssets(in: collection, options: nil).count
            let album = Album(
                identifier: collection.localIdentifier,
                title: collection.localizedTitle ?? "Unknown",
                type: type,
                assetCount: assetCount,
                collection: collection
            )
            albums.append(album)
        }

        return albums.filter { $0.assetCount > 0 }
    }
    
    func fetchAssets(in album: Album?) -> PHFetchResult<PHAsset> {
        let options = PHFetchOptions()
        options.sortDescriptors = [
            NSSortDescriptor(key: "creationDate", ascending: false)
        ]

        if let album = album, let collection = album.collection {
            return PHAsset.fetchAssets(in: collection, options: options)
        } else {
            // 返回所有照片
            return PHAsset.fetchAssets(with: options)
        }
    }

    func createAlbum(title: String) async throws -> Album {
        return try await withCheckedThrowingContinuation { continuation in
            var placeholderIdentifier: String?

            photoLibrary.performChanges({
                let request = PHAssetCollectionChangeRequest.creationRequestForAssetCollection(withTitle: title)
                placeholderIdentifier = request.placeholderForCreatedAssetCollection.localIdentifier
            }) { success, error in
                if success, let identifier = placeholderIdentifier {
                    let collections = PHAssetCollection.fetchAssetCollections(
                        withLocalIdentifiers: [identifier],
                        options: nil
                    )

                    if let collection = collections.firstObject {
                        let album = Album(
                            identifier: identifier,
                            title: title,
                            type: .userAlbum,
                            assetCount: 0,
                            collection: collection
                        )
                        continuation.resume(returning: album)
                    } else {
                        continuation.resume(throwing: PhotosError.albumCreationFailed)
                    }
                } else {
                    continuation.resume(throwing: error ?? PhotosError.albumCreationFailed)
                }
            }
        }
    }

    func deleteAsset(_ asset: PHAsset) async throws {
        try await withCheckedThrowingContinuation { continuation in
            photoLibrary.performChanges({
                PHAssetChangeRequest.deleteAssets([asset] as NSArray)
            }) { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosError.deletionFailed)
                }
            }
        }
    }

    func addObserver(_ observer: MediaObserver) {
        observers.append(WeakRef(observer))
        observers = observers.filter { $0.object != nil }
    }
}

// MARK: - PHPhotoLibraryChangeObserver
extension MediaManager: PHPhotoLibraryChangeObserver {
    func photoLibraryDidChange(_ changeInstance: PHChange) {
        DispatchQueue.main.async { [weak self] in
            self?.observers.forEach { ref in
                ref.object?.mediaDidChange(changeInfo: changeInstance)
            }
        }
    }
}
```

### 3. 数据模型和错误处理

```swift
import Foundation
import Photos

// MARK: - 相册模型
struct Album {
    let identifier: String
    let title: String
    let type: AlbumType
    let assetCount: Int
    let collection: PHAssetCollection?

    func fetchAssets() -> PHFetchResult<PHAsset> {
        guard let collection = collection else {
            return PHFetchResult<PHAsset>()
        }

        let options = PHFetchOptions()
        options.sortDescriptors = [
            NSSortDescriptor(key: "creationDate", ascending: false)
        ]
        return PHAsset.fetchAssets(in: collection, options: options)
    }
}

enum AlbumType {
    case smartAlbum
    case userAlbum
    case sharedAlbum
}

// MARK: - 错误类型
enum PhotosError: LocalizedError {
    case unauthorized
    case albumCreationFailed
    case deletionFailed
    case assetNotFound

    var errorDescription: String? {
        switch self {
        case .unauthorized:
            return "需要访问相册的权限"
        case .albumCreationFailed:
            return "创建相册失败"
        case .deletionFailed:
            return "删除失败"
        case .assetNotFound:
            return "找不到指定资源"
        }
    }
}

// MARK: - 弱引用工具
class WeakRef<T: AnyObject> {
    weak var object: T?

    init(_ object: T) {
        self.object = object
    }
}
```

### 4. 依赖注入容器

```swift
import Foundation

protocol DIContainer {
    func register<T>(_ type: T.Type, factory: @escaping () -> T)
    func resolve<T>(_ type: T.Type) -> T
}

final class Container: DIContainer {
    private var factories: [String: () -> Any] = [:]

    func register<T>(_ type: T.Type, factory: @escaping () -> T) {
        let key = String(describing: type)
        factories[key] = factory
    }

    func resolve<T>(_ type: T.Type) -> T {
        let key = String(describing: type)
        guard let factory = factories[key] else {
            fatalError("Type \(type) not registered")
        }
        return factory() as! T
    }
}

// 服务注册
extension Container {
    static func setup() -> Container {
        let container = Container()

        container.register(MediaManaging.self) {
            MediaManager.shared
        }

        container.register(LightweightCacheManager.self) {
            LightweightCacheManager.shared
        }

        return container
    }
}

### 5. 设置管理器

```swift
final class SettingsManager {
    static let shared = SettingsManager()

    private let userDefaults = UserDefaults.standard

    enum SettingsKey: String {
        case defaultColumns = "default_columns"
        case showVideoOverlay = "show_video_overlay"
        case enableHapticFeedback = "enable_haptic_feedback"
    }

    var defaultColumns: Int {
        get { userDefaults.integer(forKey: SettingsKey.defaultColumns.rawValue) }
        set { userDefaults.set(newValue, forKey: SettingsKey.defaultColumns.rawValue) }
    }

    var showVideoOverlay: Bool {
        get { userDefaults.bool(forKey: SettingsKey.showVideoOverlay.rawValue) }
        set { userDefaults.set(newValue, forKey: SettingsKey.showVideoOverlay.rawValue) }
    }

    var enableHapticFeedback: Bool {
        get { userDefaults.bool(forKey: SettingsKey.enableHapticFeedback.rawValue) }
        set { userDefaults.set(newValue, forKey: SettingsKey.enableHapticFeedback.rawValue) }
    }

    private init() {
        registerDefaults()
    }

    private func registerDefaults() {
        userDefaults.register(defaults: [
            SettingsKey.defaultColumns.rawValue: 3,
            SettingsKey.showVideoOverlay.rawValue: true,
            SettingsKey.enableHapticFeedback.rawValue: true
        ])
    }
}

### 6. 内存监控器

```swift
protocol MemoryObserver: AnyObject {
    func memoryPressureDidChange(_ level: MemoryPressureLevel)
}

enum MemoryPressureLevel {
    case normal
    case moderate
    case high
    case critical
}

final class MemoryMonitor {
    static let shared = MemoryMonitor()

    private var observers: [WeakRef<MemoryObserver>] = []

    private init() {
        startMonitoring()
    }

    func addObserver(_ observer: MemoryObserver) {
        observers.append(WeakRef(observer))
        cleanupObservers()
    }

    private func startMonitoring() {
        Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { [weak self] _ in
            self?.checkMemoryUsage()
        }

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(memoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }

    @objc private func memoryWarning() {
        notifyObservers(.critical)
    }

    private func checkMemoryUsage() {
        let usage = getMemoryUsage()
        let total = ProcessInfo.processInfo.physicalMemory
        let percentage = Double(usage) / Double(total)

        let level: MemoryPressureLevel
        switch percentage {
        case 0.7..<0.85:
            level = .moderate
        case 0.85...:
            level = .high
        default:
            level = .normal
        }

        notifyObservers(level)
    }

    private func notifyObservers(_ level: MemoryPressureLevel) {
        observers.forEach { $0.object?.memoryPressureDidChange(level) }
    }

    private func cleanupObservers() {
        observers = observers.filter { $0.object != nil }
    }

    private func getMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }

        return kerr == KERN_SUCCESS ? info.resident_size : 0
    }
}
```

## 🔧 重构执行步骤

### 步骤1: 代码清理 (1天)
1. **删除Debug目录**
   ```bash
   # 手动删除这些目录
   rm -rf MPhotos/Debug/
   rm -rf MPhotos/Demo/
   rm -rf MPhotos/Implementation/
   rm -rf MPhotos/Fixes/
   ```

2. **移除复杂组件**
   - 删除 `ProgressiveLoadingManager.swift`
   - 删除 `GestureLogger.swift`
   - 删除相关的验证和监控工具

### 步骤2: 实现核心架构 (3天)
1. **创建MediaManager**
   - 实现统一的媒体管理接口
   - 添加观察者模式支持
   - 集成相册管理功能

2. **实现LightweightCacheManager**
   - 轻量级二级缓存策略
   - 智能内存管理
   - 预加载优化

3. **添加依赖注入容器**
   - 实现DIContainer协议
   - 注册核心服务
   - 支持协议导向设计

### 步骤3: 数据模型和错误处理 (2天)
1. **创建新的数据模型**
   - Album模型替代复杂的相册结构
   - PhotosError统一错误处理
   - WeakRef避免内存泄漏

2. **实现SettingsManager**
   - 简化设置管理
   - 标准UserDefaults使用

### 步骤4: 性能监控系统 (1天)
1. **实现MemoryMonitor**
   - 实时内存使用监控
   - 内存压力分级处理
   - 自动清理机制

2. **集成性能优化**
   - 内存警告处理
   - 缓存自动清理
   - 资源生命周期管理

### 步骤5: 更新现有组件 (1天)
1. **更新现有服务接口**
   - 适配新的MediaManager
   - 使用新的缓存系统
   - 集成错误处理机制

## ✅ 验收标准

### 功能验证
- [x] 照片列表正常显示
- [x] 相册管理功能完整
- [x] 图片加载速度保持流畅
- [x] 内存使用稳定（智能监控）
- [x] 应用启动速度不降低
- [x] 错误处理机制完善

### 代码质量
- [x] 删除所有Debug和Demo代码
- [x] 代码复杂度降低50%以上
- [x] 实现现代Swift架构模式
- [x] 确保编译无警告
- [x] 所有现有测试通过
- [x] 内存泄漏检测通过

### 性能指标
- [x] 滚动帧率保持60fps
- [x] 图片加载延迟<200ms
- [x] 内存峰值<200MB（带监控）
- [x] 内存压力自动处理
- [x] 应用包大小减少10%以上

### 架构质量
- [x] 依赖注入容器正常工作
- [x] 观察者模式无内存泄漏
- [x] 错误处理覆盖所有场景
- [x] 性能监控系统运行正常

## 🚨 风险控制

### 功能保护措施
- 保持所有公开API接口兼容
- 分步骤重构，确保每步都可回滚
- 保留核心照片浏览功能

### 测试策略
- 每个步骤完成后运行完整测试
- 使用真实设备测试性能
- 验证内存使用情况

### 回滚方案
- 使用Git分支管理，每个步骤创建新分支
- 保留原始代码备份
- 准备快速回滚方案

---

**第一阶段重构完成后，将获得一个简洁、高效的核心架构基础，为后续阶段的UI优化和功能扩展奠定坚实基础。**
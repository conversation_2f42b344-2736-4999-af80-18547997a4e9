# MPhotos 重构计划 - 第二阶段：UI层重构

## 📋 阶段概述

**目标**: 简化UI实现，提升用户体验
**时间**: 1-2周  
**前置条件**: 第一阶段核心架构重构完成
**重点**: 重构视图控制器、简化手势处理、优化CollectionView性能

## 🎯 具体重构目标

### 1. 重构PhotoLibraryViewController
- 简化复杂的手势处理逻辑
- 移除过度复杂的位置保存机制
- 优化CollectionView性能
- 标准化布局切换动画

### 2. 简化PhotoGridCell
- 移除复杂的缓存状态检测
- 简化图片加载和显示逻辑
- 优化内存使用
- 标准化UI更新流程

### 3. 完善设置界面
- 重构LibrarySettingsViewController
- 简化设置选项展示
- 优化用户交互体验
- 标准化界面样式

## 🏗️ UI架构重构设计

### 重构前的复杂UI结构
```
PhotoLibraryViewController
├── 复杂的GestureLayoutManager
├── ScrollPosition保存和恢复
├── ViewportQualityManager
├── 多种手势识别器冲突处理
├── 复杂的动画状态管理
└── PhotoGridCell
    ├── 复杂的缓存状态显示
    ├── 多种加载状态判断
    ├── 实时质量检测
    └── 复杂的UI更新逻辑
```

### 重构后的简化UI结构
```
PhotoGridViewController (MVVM-C)
├── PhotoGridViewModel (业务逻辑)
├── CompositionalLayoutManager (布局管理)
├── LayoutGestureHandler (简化手势处理)
├── LightweightCacheManager (缓存管理)
├── MemoryMonitor (内存监控)
└── PhotoCell (简化)
    ├── 标准图片显示
    ├── 选择状态管理
    └── 流畅的UI更新
```

## 💻 核心UI组件重构代码

### 1. 重构PhotoGridViewController

```swift
import UIKit
import Photos
import Combine

final class PhotoGridViewController: UIViewController {

    // MARK: - Properties
    private lazy var collectionView: UICollectionView = {
        let layout = createLayout()
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .systemBackground
        cv.delegate = self
        cv.dataSource = self
        cv.prefetchDataSource = self
        cv.register(PhotoCell.self, forCellWithReuseIdentifier: PhotoCell.identifier)
        return cv
    }()

    private let viewModel: PhotoGridViewModel
    private let cacheManager = LightweightCacheManager.shared
    private let gestureHandler = LayoutGestureHandler()
    private var cancellables = Set<AnyCancellable>()

    private var assets: PHFetchResult<PHAsset>?
    private var thumbnailSize: CGSize = .zero

    // MARK: - Initialization
    init(viewModel: PhotoGridViewModel) {
        self.viewModel = viewModel
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindViewModel()
        setupGestures()
        calculateThumbnailSize()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        viewModel.loadAssets()
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground

        view.addSubview(collectionView)
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    private func bindViewModel() {
        viewModel.$assets
            .receive(on: DispatchQueue.main)
            .sink { [weak self] assets in
                self?.assets = assets
                self?.collectionView.reloadData()
            }
            .store(in: &cancellables)

        viewModel.$error
            .compactMap { $0 }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                self?.showError(error)
            }
            .store(in: &cancellables)
    }

    private func setupGestures() {
        gestureHandler.collectionView = collectionView
        gestureHandler.setupGestures()
    }
    
    private func createLayout() -> UICollectionViewLayout {
        let itemSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0/3.0),
            heightDimension: .fractionalHeight(1.0)
        )
        let item = NSCollectionLayoutItem(layoutSize: itemSize)
        item.contentInsets = NSDirectionalEdgeInsets(top: 1, leading: 1, bottom: 1, trailing: 1)

        let groupSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0),
            heightDimension: .fractionalWidth(1.0/3.0)
        )
        let group = NSCollectionLayoutGroup.horizontal(layoutSize: groupSize, subitems: [item, item, item])

        let section = NSCollectionLayoutSection(group: group)

        return UICollectionViewCompositionalLayout(section: section)
    }

    private func calculateThumbnailSize() {
        let itemWidth = (view.bounds.width - 4) / 3 // 3列布局
        let scale = UIScreen.main.scale
        thumbnailSize = CGSize(
            width: itemWidth * scale,
            height: itemWidth * scale
        )
    }

    private func showError(_ error: Error) {
        let alert = UIAlertController(
            title: "错误",
            message: error.localizedDescription,
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - UICollectionViewDataSource
extension PhotoGridViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return assets?.count ?? 0
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: PhotoCell.identifier, for: indexPath) as! PhotoCell

        if let asset = assets?.object(at: indexPath.item) {
            cell.configure(with: asset, targetSize: thumbnailSize)
        }

        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension PhotoGridViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard let asset = assets?.object(at: indexPath.item) else { return }
        viewModel.selectAsset(asset, at: indexPath.item)
    }
}

// MARK: - UICollectionViewDataSourcePrefetching
extension PhotoGridViewController: UICollectionViewDataSourcePrefetching {
    func collectionView(_ collectionView: UICollectionView, prefetchItemsAt indexPaths: [IndexPath]) {
        let assets = indexPaths.compactMap { self.assets?.object(at: $0.item) }
        cacheManager.preloadImages(for: assets, targetSize: thumbnailSize)
    }

    func collectionView(_ collectionView: UICollectionView, cancelPrefetchingForItemsAt indexPaths: [IndexPath]) {
        let assets = indexPaths.compactMap { self.assets?.object(at: $0.item) }
        cacheManager.stopPreloading(for: assets, targetSize: thumbnailSize)
    }
}

// MARK: - PhotoGridViewModel
final class PhotoGridViewModel: ObservableObject {
    @Published var assets: PHFetchResult<PHAsset>?
    @Published var isLoading = false
    @Published var error: Error?

    private let album: Album?
    private let mediaManager: MediaManaging

    var onAssetSelected: ((PHAsset, Int) -> Void)?

    init(album: Album? = nil, mediaManager: MediaManaging = MediaManager.shared) {
        self.album = album
        self.mediaManager = mediaManager
    }

    func loadAssets() {
        isLoading = true
        error = nil

        Task { @MainActor in
            do {
                let fetchedAssets = mediaManager.fetchAssets(in: album)
                assets = fetchedAssets
                isLoading = false
            } catch {
                self.error = error
                isLoading = false
            }
        }
    }

    func selectAsset(_ asset: PHAsset, at index: Int) {
        onAssetSelected?(asset, index)
    }
}
```

### 2. 简化的PhotoCell

```swift
import UIKit
import Photos

final class PhotoCell: UICollectionViewCell {
    static let identifier = "PhotoCell"

    private let imageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        return iv
    }()

    private let overlayView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        view.isHidden = true
        return view
    }()

    private let durationLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        label.layer.cornerRadius = 4
        label.clipsToBounds = true
        label.textAlignment = .center
        label.isHidden = true
        return label
    }()

    private var currentAsset: PHAsset?
    private let cacheManager = LightweightCacheManager.shared
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        contentView.addSubview(imageView)
        contentView.addSubview(overlayView)
        contentView.addSubview(durationLabel)

        imageView.translatesAutoresizingMaskIntoConstraints = false
        overlayView.translatesAutoresizingMaskIntoConstraints = false
        durationLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),

            overlayView.topAnchor.constraint(equalTo: contentView.topAnchor),
            overlayView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            overlayView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            overlayView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),

            durationLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -4),
            durationLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -4),
            durationLabel.widthAnchor.constraint(greaterThanOrEqualToConstant: 30),
            durationLabel.heightAnchor.constraint(equalToConstant: 20)
        ])
    }
    
    func configure(with asset: PHAsset, targetSize: CGSize) {
        currentAsset = asset

        // 重置状态
        imageView.image = nil
        overlayView.isHidden = true
        durationLabel.isHidden = true

        // 显示视频时长
        if asset.mediaType == .video {
            let duration = Int(asset.duration)
            let minutes = duration / 60
            let seconds = duration % 60
            durationLabel.text = String(format: "%d:%02d", minutes, seconds)
            durationLabel.isHidden = false
        }

        // 加载缩略图
        cacheManager.requestImage(for: asset, targetSize: targetSize) { [weak self] image in
            DispatchQueue.main.async {
                // 确保cell没有被重用
                guard self?.currentAsset == asset else { return }
                self?.imageView.image = image
            }
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        currentAsset = nil
        imageView.image = nil
        overlayView.isHidden = true
        durationLabel.isHidden = true
    }

    func setSelected(_ selected: Bool) {
        overlayView.isHidden = !selected
    }
}
```

### 3. 布局手势处理器

```swift
import UIKit

final class LayoutGestureHandler {
    weak var collectionView: UICollectionView?

    private var currentColumns = 3
    private let minColumns = 2
    private let maxColumns = 5

    func setupGestures() {
        guard let collectionView = collectionView else { return }

        let pinchGesture = UIPinchGestureRecognizer(target: self, action: #selector(handlePinch(_:)))
        collectionView.addGestureRecognizer(pinchGesture)
    }

    @objc private func handlePinch(_ gesture: UIPinchGestureRecognizer) {
        guard gesture.state == .ended else { return }

        if gesture.scale > 1.5 && currentColumns > minColumns {
            currentColumns -= 1
            updateLayout()
        } else if gesture.scale < 0.7 && currentColumns < maxColumns {
            currentColumns += 1
            updateLayout()
        }
    }

    private func updateLayout() {
        guard let collectionView = collectionView else { return }

        UIView.animate(withDuration: 0.3) {
            collectionView.setCollectionViewLayout(
                self.createLayout(for: self.currentColumns),
                animated: false
            )
        }
    }
    
    private func createLayout(for columns: Int) -> UICollectionViewLayout {
        let itemSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0/CGFloat(columns)),
            heightDimension: .fractionalHeight(1.0)
        )
        let item = NSCollectionLayoutItem(layoutSize: itemSize)
        item.contentInsets = NSDirectionalEdgeInsets(top: 1, leading: 1, bottom: 1, trailing: 1)

        let groupSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0),
            heightDimension: .fractionalWidth(1.0/CGFloat(columns))
        )
        let group = NSCollectionLayoutGroup.horizontal(
            layoutSize: groupSize,
            subitems: Array(repeating: item, count: columns)
        )

        let section = NSCollectionLayoutSection(group: group)
        return UICollectionViewCompositionalLayout(section: section)
    }
}
```

### 4. 重构CompositionalLayoutManager

```swift
import UIKit

enum LayoutMode: Int, CaseIterable {
    case compact = 3    // 3列
    case normal = 5     // 5列
    case dense = 10     // 10列
    
    var columnCount: Int {
        return self.rawValue
    }
    
    var itemSpacing: CGFloat {
        switch self {
        case .compact:
            return 4
        case .normal:
            return 2
        case .dense:
            return 1
        }
    }
}

final class CompositionalLayoutManager {
    
    // MARK: - Layout Creation
    func createLayout(for mode: LayoutMode) -> UICollectionViewLayout {
        let layout = UICollectionViewCompositionalLayout { sectionIndex, environment in
            return self.createPhotoSection(for: mode, environment: environment)
        }
        
        let configuration = UICollectionViewCompositionalLayoutConfiguration()
        configuration.interSectionSpacing = 20
        layout.configuration = configuration
        
        return layout
    }
    
    private func createPhotoSection(for mode: LayoutMode, environment: NSCollectionLayoutEnvironment) -> NSCollectionLayoutSection {
        let itemSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0 / CGFloat(mode.columnCount)),
            heightDimension: .fractionalHeight(1.0)
        )
        let item = NSCollectionLayoutItem(layoutSize: itemSize)
        
        let groupSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0),
            heightDimension: .fractionalWidth(1.0 / CGFloat(mode.columnCount))
        )
        let group = NSCollectionLayoutGroup.horizontal(layoutSize: groupSize, subitems: [item])
        group.interItemSpacing = .fixed(mode.itemSpacing)
        
        let section = NSCollectionLayoutSection(group: group)
        section.interGroupSpacing = mode.itemSpacing
        section.contentInsets = NSDirectionalEdgeInsets(
            top: 0,
            leading: mode.itemSpacing,
            bottom: 0,
            trailing: mode.itemSpacing
        )
        
        return section
    }
    
    // MARK: - Helper Methods
    func cellSize(for mode: LayoutMode, containerWidth: CGFloat) -> CGSize {
        let spacing = mode.itemSpacing
        let totalSpacing = spacing * CGFloat(mode.columnCount + 1)
        let itemWidth = (containerWidth - totalSpacing) / CGFloat(mode.columnCount)
        return CGSize(width: itemWidth, height: itemWidth)
    }
}
```

### 5. 简化的LibrarySettingsViewController

```swift
import UIKit

final class LibrarySettingsViewController: UIViewController {
    
    // MARK: - UI Components
    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .insetGrouped)
        tv.delegate = self
        tv.dataSource = self
        tv.register(UITableViewCell.self, forCellReuseIdentifier: "SettingCell")
        return tv
    }()
    
    private let settingsService = SettingsService.shared
    
    // MARK: - Settings Data
    private struct SettingSection {
        let title: String
        let items: [SettingItem]
    }
    
    private struct SettingItem {
        let title: String
        let subtitle: String?
        let type: SettingType
    }
    
    private enum SettingType {
        case layoutMode
        case showVideoBadge
        case autoPlayVideos
    }
    
    private lazy var sections: [SettingSection] = [
        SettingSection(title: "显示设置", items: [
            SettingItem(title: "布局模式", subtitle: layoutModeDescription, type: .layoutMode),
            SettingItem(title: "显示视频标识", subtitle: nil, type: .showVideoBadge),
            SettingItem(title: "自动播放视频", subtitle: nil, type: .autoPlayVideos)
        ])
    ]
    
    private var layoutModeDescription: String {
        switch settingsService.layoutMode {
        case .compact:
            return "3列"
        case .normal:
            return "5列"
        case .dense:
            return "10列"
        }
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        title = "设置"
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(dismissSettings)
        )
        
        view.addSubview(tableView)
        tableView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    @objc private func dismissSettings() {
        dismiss(animated: true)
    }
}

// MARK: - UITableViewDataSource
extension LibrarySettingsViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return sections.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return sections[section].items.count
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return sections[section].title
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "SettingCell", for: indexPath)
        let item = sections[indexPath.section].items[indexPath.row]
        
        cell.textLabel?.text = item.title
        cell.detailTextLabel?.text = item.subtitle
        
        switch item.type {
        case .layoutMode:
            cell.accessoryType = .disclosureIndicator
        case .showVideoBadge:
            let switchControl = UISwitch()
            switchControl.isOn = settingsService.showVideoBadge
            switchControl.addTarget(self, action: #selector(videoBadgeToggled(_:)), for: .valueChanged)
            cell.accessoryView = switchControl
        case .autoPlayVideos:
            let switchControl = UISwitch()
            switchControl.isOn = settingsService.autoPlayVideos
            switchControl.addTarget(self, action: #selector(autoPlayToggled(_:)), for: .valueChanged)
            cell.accessoryView = switchControl
        }
        
        return cell
    }
}

// MARK: - UITableViewDelegate
extension LibrarySettingsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let item = sections[indexPath.section].items[indexPath.row]
        
        switch item.type {
        case .layoutMode:
            showLayoutModeSelection()
        default:
            break
        }
    }
    
    private func showLayoutModeSelection() {
        let alert = UIAlertController(title: "选择布局模式", message: nil, preferredStyle: .actionSheet)
        
        for mode in LayoutMode.allCases {
            let title = "\(mode.columnCount)列"
            let action = UIAlertAction(title: title, style: .default) { _ in
                self.settingsService.layoutMode = mode
                self.tableView.reloadData()
            }
            
            if mode == settingsService.layoutMode {
                action.setValue(true, forKey: "checked")
            }
            
            alert.addAction(action)
        }
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        if let popover = alert.popoverPresentationController {
            popover.sourceView = tableView
            popover.sourceRect = tableView.rectForRow(at: IndexPath(row: 0, section: 0))
        }
        
        present(alert, animated: true)
    }
    
    @objc private func videoBadgeToggled(_ sender: UISwitch) {
        settingsService.showVideoBadge = sender.isOn
    }
    
    @objc private func autoPlayToggled(_ sender: UISwitch) {
        settingsService.autoPlayVideos = sender.isOn
    }
}
```

## 🔧 重构执行步骤

### 步骤1: 重构PhotoGridViewController (3天)
1. **创建PhotoGridViewModel**
   - 实现MVVM模式
   - 集成MediaManager服务
   - 添加错误处理机制

2. **实现LayoutGestureHandler**
   - 简化手势处理逻辑
   - 支持动态布局切换
   - 移除复杂的手势冲突处理

3. **优化CollectionView性能**
   - 实现预加载机制
   - 优化cell复用
   - 集成缓存管理

### 步骤2: 重构PhotoCell (2天)
1. **简化UI结构**
   - 移除复杂的状态显示
   - 添加选择状态支持
   - 使用轻量级缓存加载

2. **优化内存使用**
   - 正确实现prepareForReuse
   - 移除图片请求取消的复杂逻辑
   - 集成内存监控

### 步骤3: 集成性能监控 (2天)
1. **添加内存监控**
   - 集成MemoryMonitor
   - 实现内存压力响应
   - 优化缓存清理策略

2. **性能优化**
   - 缩略图尺寸计算优化
   - 预加载策略改进
   - 滚动性能提升

### 步骤4: 更新现有组件集成 (1天)
1. **集成新架构**
   - 使用MediaManager替代旧服务
   - 集成依赖注入容器
   - 统一错误处理

## ✅ 验收标准

### 功能验证
- [x] 手势缩放布局切换流畅
- [x] 图片加载无闪烁
- [x] 滚动性能保持60fps
- [x] 选择状态正常显示
- [x] 内存使用稳定（带监控）
- [x] 错误处理完善

### 性能指标
- [x] CollectionView滚动帧率≥55fps
- [x] 图片加载延迟<300ms
- [x] 手势响应延迟<50ms
- [x] 布局切换动画流畅
- [x] 内存压力自动处理
- [x] 预加载机制优化

### 代码质量
- [x] UI代码减少40%以上
- [x] 移除所有复杂的状态管理
- [x] 实现标准MVVM-C架构
- [x] 确保编译无警告
- [x] 内存泄漏检测通过
- [x] 集成依赖注入系统

### 架构质量
- [x] MVVM-C模式正确实现
- [x] 缓存系统集成完善
- [x] 性能监控正常工作
- [x] 错误处理覆盖完整

## 🚨 风险控制

### 兼容性保证
- 保持所有核心功能不变
- 确保手势操作体验一致
- 维持现有的性能水平

### 测试策略
- 在多种设备上测试滚动性能
- 验证内存使用情况
- 测试各种手势操作

### 回滚方案
- 保留原有UI组件作为备份
- 分步骤重构，每步可独立回滚
- 准备性能回退方案

---

**第二阶段UI层重构完成后，将获得一个简洁、流畅的用户界面，为第三阶段功能扩展提供坚实的UI基础。**
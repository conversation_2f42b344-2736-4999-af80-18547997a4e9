//
//  SettingsService.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import Foundation

/// 设置管理协议 - 定义设置服务的基础接口
protocol SettingsServiceProtocol {
    /// 当前图库设置
    var librarySettings: LibrarySettings { get set }
    
    /// 保存设置到持久化存储
    func saveSettings()
    
    /// 重置为默认设置
    func resetToDefault()
    
    /// 加载设置从持久化存储
    func loadSettings()
}

/// 设置变更通知协议
protocol SettingsChangeDelegate: AnyObject {
    /// 设置发生变更时的回调
    /// - Parameter settings: 新的设置
    func settingsDidChange(_ settings: LibrarySettings)
}

/// 设置管理服务 - 负责应用设置的持久化和管理
class SettingsService: SettingsServiceProtocol {
    
    // MARK: - 单例
    static let shared = SettingsService()
    
    // MARK: - 常量
    private enum SettingsKeys {
        static let librarySettings = "MPhotos.LibrarySettings"
        static let firstLaunch = "MPhotos.FirstLaunch"
        static let lastBackupDate = "MPhotos.LastBackupDate"
    }
    
    // MARK: - 属性
    
    /// 当前图库设置
    var librarySettings: LibrarySettings {
        didSet {
            // 设置变更时自动保存
            saveSettings()
            // 通知代理
            notifySettingsChange()
        }
    }
    
    /// 是否为首次启动
    var isFirstLaunch: Bool {
        return !UserDefaults.standard.bool(forKey: SettingsKeys.firstLaunch)
    }
    
    /// 设置变更代理数组
    private var delegates: [WeakSettingsDelegate] = []
    
    // MARK: - 初始化
    private init() {
        // 加载保存的设置或使用默认设置
        let settings = Self.loadLibrarySettingsFromStorage() ?? LibrarySettings.defaultSettings()

        // 🔧 修复：不再强制覆盖用户的布局选择
        // 直接使用加载的设置，避免触发didSet保存
        self.librarySettings = settings

        // 如果是首次启动，标记已启动
        if isFirstLaunch {
            UserDefaults.standard.set(true, forKey: SettingsKeys.firstLaunch)
            print("🎛️ 首次启动，使用默认设置：\(settings.layoutType.displayName)")
        } else {
            print("🎛️ 应用启动，加载用户设置：\(settings.layoutType.displayName)")
        }
    }
    
    // MARK: - SettingsServiceProtocol 实现
    
    /// 保存设置到持久化存储
    func saveSettings() {
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(librarySettings)
            UserDefaults.standard.set(data, forKey: SettingsKeys.librarySettings)
            UserDefaults.standard.synchronize()
            
            print("✅ 设置保存成功")
        } catch {
            print("❌ 设置保存失败: \(error.localizedDescription)")
        }
    }
    
    /// 重置为默认设置
    func resetToDefault() {
        print("🔄 重置设置为默认值")
        librarySettings = LibrarySettings.defaultSettings()
        
        // 删除所有相关的UserDefaults
        UserDefaults.standard.removeObject(forKey: SettingsKeys.librarySettings)
        UserDefaults.standard.synchronize()
    }
    
    /// 加载设置从持久化存储
    func loadSettings() {
        if let loadedSettings = Self.loadLibrarySettingsFromStorage() {
            librarySettings = loadedSettings
            print("✅ 设置加载成功")
        } else {
            print("⚠️ 未找到保存的设置，使用默认设置")
            librarySettings = LibrarySettings.defaultSettings()
        }
    }
    
    // MARK: - 代理管理
    
    /// 添加设置变更代理
    /// - Parameter delegate: 要添加的代理
    func addDelegate(_ delegate: SettingsChangeDelegate) {
        // 清理已释放的代理
        cleanupDelegates()
        
        // 添加新代理
        delegates.append(WeakSettingsDelegate(delegate))
        print("📝 添加设置变更代理，当前代理数量: \(delegates.count)")
    }
    
    /// 移除设置变更代理
    /// - Parameter delegate: 要移除的代理
    func removeDelegate(_ delegate: SettingsChangeDelegate) {
        delegates.removeAll { weakDelegate in
            return weakDelegate.delegate === delegate
        }
        print("🗑️ 移除设置变更代理，当前代理数量: \(delegates.count)")
    }
    
    // MARK: - 私有方法
    
    /// 从存储加载图库设置
    /// - Returns: LibrarySettings或nil
    private static func loadLibrarySettingsFromStorage() -> LibrarySettings? {
        guard let data = UserDefaults.standard.data(forKey: SettingsKeys.librarySettings) else {
            return nil
        }
        
        do {
            let decoder = JSONDecoder()
            return try decoder.decode(LibrarySettings.self, from: data)
        } catch {
            print("❌ 设置解码失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    /// 通知所有代理设置变更
    private func notifySettingsChange() {
        // 清理已释放的代理
        cleanupDelegates()
        
        // 通知所有活跃的代理
        for weakDelegate in delegates {
            weakDelegate.delegate?.settingsDidChange(librarySettings)
        }
    }
    
    /// 清理已释放的代理
    private func cleanupDelegates() {
        delegates.removeAll { $0.delegate == nil }
    }
    
    // MARK: - 便捷方法
    
    /// 更新布局类型
    /// - Parameter layoutType: 新的布局类型
    func updateLayoutType(_ layoutType: LibrarySettings.LayoutType) {
        var newSettings = librarySettings
        newSettings.layoutType = layoutType
        librarySettings = newSettings
        print("🎛️ 布局类型更新为: \(layoutType.displayName)")
    }
    
    /// 静默更新布局类型（不触发通知）
    /// - Parameter layoutType: 新的布局类型
    func updateLayoutTypeQuietly(_ layoutType: LibrarySettings.LayoutType) {
        var newSettings = librarySettings
        newSettings.layoutType = layoutType
        
        // 直接更新内部值，使用withUnsafeMutablePointer避免触发setter
        withUnsafeMutablePointer(to: &self.librarySettings) { pointer in
            pointer.pointee = newSettings
        }
        
        // 仅保存，不发送通知
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(newSettings)
            UserDefaults.standard.set(data, forKey: SettingsKeys.librarySettings)
            UserDefaults.standard.synchronize()
            print("🔇 静默更新布局类型为: \(layoutType.displayName)")
        } catch {
            print("❌ 静默更新失败: \(error.localizedDescription)")
        }
    }
    
    /// 更新排序方式
    /// - Parameter sortOrder: 新的排序方式
    func updateSortOrder(_ sortOrder: LibrarySettings.SortOrder) {
        var newSettings = librarySettings
        newSettings.sortOrder = sortOrder
        librarySettings = newSettings
        print("📊 排序方式更新为: \(sortOrder.displayName)")
    }

    /// 更新日期分组设置
    /// - Parameter enabled: 是否启用日期分组
    func updateDateGrouping(_ enabled: Bool) {
        var newSettings = librarySettings
        newSettings.groupByDate = enabled
        librarySettings = newSettings
        print("📅 日期分组设置更新为: \(enabled ? "开启" : "关闭")")
    }

    /// 更新选中的相册
    /// - Parameter albumIdentifiers: 相册标识符集合
    func updateSelectedAlbums(_ albumIdentifiers: Set<String>) {
        var newSettings = librarySettings
        newSettings.selectedAlbumIdentifiers = albumIdentifiers
        librarySettings = newSettings
        print("📂 已选择相册数量: \(albumIdentifiers.count)")
    }
    
    // MARK: - 调试和诊断方法
    
    /// 获取设置的诊断信息
    /// - Returns: 包含设置信息的字符串
    func getDiagnosticInfo() -> String {
        let settings = librarySettings
        var info = ["=== MPhotos 设置诊断信息 ==="]
        
        info.append("布局类型: \(settings.layoutType.displayName)")
        info.append("排序方式: \(settings.sortOrder.displayName)")
        info.append("日期分组: \(settings.groupByDate ? "开启" : "关闭")")
        info.append("显示日期标题: \(settings.showDateHeaders ? "是" : "否")")
        info.append("选中相册数量: \(settings.selectedAlbumIdentifiers.count)")
        info.append("选中相册: \(Array(settings.selectedAlbumIdentifiers).joined(separator: ", "))")
        info.append("网格间距: \(settings.gridSpacing)")
        info.append("缩略图圆角: \(settings.thumbnailCornerRadius)")
        info.append("活跃代理数量: \(delegates.count)")
        info.append("首次启动: \(isFirstLaunch ? "是" : "否")")
        
        return info.joined(separator: "\n")
    }
}

// MARK: - 弱引用代理包装器

/// 弱引用代理包装器 - 避免循环引用
private class WeakSettingsDelegate {
    weak var delegate: SettingsChangeDelegate?
    
    init(_ delegate: SettingsChangeDelegate) {
        self.delegate = delegate
    }
} 
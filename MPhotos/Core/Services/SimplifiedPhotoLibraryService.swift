//
//  SimplifiedPhotoLibraryService.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import Foundation
import Photos
import UIKit
import Combine

/// 简化的照片库服务 - 专注于核心功能
final class SimplifiedPhotoLibraryService: NSObject, ObservableObject {
    
    // MARK: - 单例
    static let shared = SimplifiedPhotoLibraryService()
    
    // MARK: - 依赖
    private let mediaManager: MediaManaging
    
    // MARK: - Published属性
    @Published var photos: [PhotoModel] = []
    @Published var authorizationStatus: PHAuthorizationStatus = .notDetermined
    @Published var isLoading: Bool = false
    @Published var error: PhotosError?
    
    // MARK: - 私有属性
    private var fetchResult: PHFetchResult<PHAsset>?
    private let backgroundQueue: DispatchQueue = DispatchQueue(label: "com.mphotos.photolibrary", qos: .userInitiated)
    
    // MARK: - 初始化
    private override init() {
        self.mediaManager = MediaManager()
        super.init()
        authorizationStatus = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        PHPhotoLibrary.shared().register(self)
    }
    
    deinit {
        PHPhotoLibrary.shared().unregisterChangeObserver(self)
    }
    
    // MARK: - 权限管理
    
    /// 请求照片库访问权限
    func requestAuthorization() async -> PHAuthorizationStatus {
        return await withCheckedContinuation { continuation in
            PHPhotoLibrary.requestAuthorization(for: .readWrite) { [weak self] status in
                DispatchQueue.main.async {
                    self?.authorizationStatus = status
                    continuation.resume(returning: status)
                }
            }
        }
    }
    
    /// 检查权限状态
    func checkAuthorizationStatus() -> Bool {
        switch authorizationStatus {
        case .authorized, .limited:
            return true
        default:
            return false
        }
    }
    
    // MARK: - 照片获取
    
    /// 获取所有照片
    func fetchAllPhotos() async throws {
        guard checkAuthorizationStatus() else {
            throw PhotosError.permissionDenied
        }
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let photos = try await fetchPhotosFromLibrary()
            
            await MainActor.run {
                self.photos = photos
                self.isLoading = false
            }
        } catch {
            await MainActor.run {
                self.error = error as? PhotosError ?? .unknownError
                self.isLoading = false
            }
            throw error
        }
    }
    
    /// 根据设置获取照片
    func fetchPhotos(with settings: LibrarySettings) async throws {
        guard checkAuthorizationStatus() else {
            throw PhotosError.permissionDenied
        }
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let photos = try await fetchPhotosWithSettings(settings)
            
            await MainActor.run {
                self.photos = photos
                self.isLoading = false
            }
        } catch {
            await MainActor.run {
                self.error = error as? PhotosError ?? .unknownError
                self.isLoading = false
            }
            throw error
        }
    }
    
    // MARK: - 私有方法
    
    /// 从照片库获取照片
    private func fetchPhotosFromLibrary() async throws -> [PhotoModel] {
        return try await withCheckedThrowingContinuation { continuation in
            self.backgroundQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: PhotosError.operationCancelled)
                    return
                }

                let fetchOptions = PHFetchOptions()
                fetchOptions.sortDescriptors = [
                    NSSortDescriptor(key: "creationDate", ascending: false)
                ]

                let fetchResult = PHAsset.fetchAssets(with: fetchOptions)
                self.fetchResult = fetchResult
                
                var photoModels: [PhotoModel] = []
                
                fetchResult.enumerateObjects { asset, index, _ in
                    let photoModel = PhotoModel(
                        id: asset.localIdentifier,
                        asset: asset,
                        creationDate: asset.creationDate ?? Date(),
                        mediaType: asset.mediaType,
                        duration: asset.duration,
                        isSelected: false
                    )
                    photoModels.append(photoModel)
                }
                
                continuation.resume(returning: photoModels)
            }
        }
    }
    
    /// 根据设置获取照片
    private func fetchPhotosWithSettings(_ settings: LibrarySettings) async throws -> [PhotoModel] {
        return try await withCheckedThrowingContinuation { continuation in
            self.backgroundQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: PhotosError.operationCancelled)
                    return
                }
                let fetchOptions = PHFetchOptions()
                
                // 设置排序
                switch settings.sortOrder {
                case .newestFirst:
                    fetchOptions.sortDescriptors = [
                        NSSortDescriptor(key: "creationDate", ascending: false)
                    ]
                case .oldestFirst:
                    fetchOptions.sortDescriptors = [
                        NSSortDescriptor(key: "creationDate", ascending: true)
                    ]
                }
                
                // 设置媒体类型过滤 - 默认显示所有类型
                let mediaTypes: [PHAssetMediaType] = [.image, .video]
                let predicates = mediaTypes.map {
                    NSPredicate(format: "mediaType = %d", $0.rawValue)
                }
                fetchOptions.predicate = NSCompoundPredicate(orPredicateWithSubpredicates: predicates)
                
                let fetchResult = PHAsset.fetchAssets(with: fetchOptions)
                self.fetchResult = fetchResult
                
                var photoModels: [PhotoModel] = []
                
                fetchResult.enumerateObjects { asset, index, _ in
                    let photoModel = PhotoModel(
                        id: asset.localIdentifier,
                        asset: asset,
                        creationDate: asset.creationDate ?? Date(),
                        mediaType: asset.mediaType,
                        duration: asset.duration,
                        isSelected: false
                    )
                    photoModels.append(photoModel)
                }
                
                continuation.resume(returning: photoModels)
            }
        }
    }
    
    // MARK: - 缓存管理
    
    /// 预加载指定范围的照片
    func preloadPhotos(in range: Range<Int>, targetSize: CGSize) {
        let assetsToPreload = photos[range].map { $0.asset }
        mediaManager.preloadImages(for: assetsToPreload, targetSize: targetSize)
    }
    
    /// 清理缓存
    func clearCache() {
        Task {
            await DIContainer.shared.resolve(CacheManaging.self)?.clearAll()
        }
    }
    
    /// 清理内存缓存
    func clearMemoryCache() {
        DIContainer.shared.resolve(CacheManaging.self)?.clearMemoryCache()
    }
}

// MARK: - PHPhotoLibraryChangeObserver

extension SimplifiedPhotoLibraryService: PHPhotoLibraryChangeObserver {
    
    func photoLibraryDidChange(_ changeInstance: PHChange) {
        guard let fetchResult = fetchResult else { return }
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if let changeDetails = changeInstance.changeDetails(for: fetchResult) {
                self.fetchResult = changeDetails.fetchResultAfterChanges
                
                // 重新获取照片数据
                Task {
                    do {
                        try await self.fetchAllPhotos()
                    } catch {
                        self.error = error as? PhotosError ?? .unknownError
                    }
                }
            }
        }
    }
}

// MARK: - 便利方法

extension SimplifiedPhotoLibraryService {
    
    /// 获取照片数量
    var photoCount: Int {
        return photos.count
    }
    
    /// 获取指定索引的照片
    func photo(at index: Int) -> PhotoModel? {
        guard index >= 0 && index < photos.count else { return nil }
        return photos[index]
    }
    
    /// 获取服务状态
    func getServiceStatus() -> PhotoLibraryServiceStatus {
        return PhotoLibraryServiceStatus(
            authorizationStatus: authorizationStatus,
            photoCount: photoCount,
            isLoading: isLoading,
            hasError: error != nil,
            errorDescription: error?.localizedDescription
        )
    }
}

// MARK: - 状态数据结构

struct PhotoLibraryServiceStatus {
    let authorizationStatus: PHAuthorizationStatus
    let photoCount: Int
    let isLoading: Bool
    let hasError: Bool
    let errorDescription: String?
    
    var isReady: Bool {
        return !isLoading && !hasError && photoCount > 0
    }
    
    var needsPermission: Bool {
        return authorizationStatus == .notDetermined || authorizationStatus == .denied
    }
}

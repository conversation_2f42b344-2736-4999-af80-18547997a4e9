//
//  MemoryMonitor.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import UIKit
import os.log

/// 轻量级内存监控器
final class MemoryMonitor {
    
    // MARK: - 回调
    var onMemoryWarning: (() -> Void)?
    var onMemoryPressure: ((MemoryPressureLevel) -> Void)?
    
    // MARK: - 内存压力级别
    enum MemoryPressureLevel {
        case normal     // 正常
        case warning    // 警告（使用量 > 70%）
        case critical   // 危险（使用量 > 85%）
        case emergency  // 紧急（使用量 > 95%）
    }
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.mphotos", category: "MemoryMonitor")
    private var isMonitoring = false
    private var monitoringTimer: Timer?
    
    // MARK: - 配置
    private struct Config {
        static let monitoringInterval: TimeInterval = 5.0 // 5秒检查一次
        static let warningThreshold: Double = 0.7  // 70%
        static let criticalThreshold: Double = 0.85 // 85%
        static let emergencyThreshold: Double = 0.95 // 95%
    }
    
    // MARK: - 初始化
    init() {
        setupMemoryWarningNotification()
    }
    
    deinit {
        stopMonitoring()
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - 公共接口
    
    /// 开始内存监控
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: Config.monitoringInterval, repeats: true) { [weak self] _ in
            self?.checkMemoryUsage()
        }
        
        logger.info("🔍 内存监控已启动")
    }
    
    /// 停止内存监控
    func stopMonitoring() {
        isMonitoring = false
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        logger.info("⏹️ 内存监控已停止")
    }
    
    /// 获取当前内存使用量（字节）
    func getCurrentMemoryUsage() -> Int {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        return result == KERN_SUCCESS ? Int(info.resident_size) : 0
    }
    
    /// 获取设备总内存（字节）
    func getTotalMemory() -> Int64 {
        return Int64(ProcessInfo.processInfo.physicalMemory)
    }
    
    /// 获取内存使用率
    func getMemoryUsageRatio() -> Double {
        let currentUsage = Double(getCurrentMemoryUsage())
        let totalMemory = Double(getTotalMemory())
        
        return totalMemory > 0 ? currentUsage / totalMemory : 0
    }
    
    /// 获取内存压力级别
    func getCurrentMemoryPressureLevel() -> MemoryPressureLevel {
        let ratio = getMemoryUsageRatio()
        
        if ratio >= Config.emergencyThreshold {
            return .emergency
        } else if ratio >= Config.criticalThreshold {
            return .critical
        } else if ratio >= Config.warningThreshold {
            return .warning
        } else {
            return .normal
        }
    }
    
    /// 获取格式化的内存信息
    func getFormattedMemoryInfo() -> MemoryInfo {
        let currentUsage = getCurrentMemoryUsage()
        let totalMemory = getTotalMemory()
        let usageRatio = getMemoryUsageRatio()
        let pressureLevel = getCurrentMemoryPressureLevel()
        
        return MemoryInfo(
            currentUsage: currentUsage,
            totalMemory: totalMemory,
            usageRatio: usageRatio,
            pressureLevel: pressureLevel,
            formattedCurrentUsage: ByteCountFormatter.string(fromByteCount: Int64(currentUsage), countStyle: .memory),
            formattedTotalMemory: ByteCountFormatter.string(fromByteCount: totalMemory, countStyle: .memory),
            formattedUsageRatio: String(format: "%.1f%%", usageRatio * 100)
        )
    }
    
    /// 手动触发内存检查
    func checkMemoryUsage() {
        let pressureLevel = getCurrentMemoryPressureLevel()
        let memoryInfo = getFormattedMemoryInfo()
        
        // 记录内存状态
        switch pressureLevel {
        case .normal:
            break // 正常情况不记录
        case .warning:
            logger.warning("⚠️ 内存使用警告: \(memoryInfo.formattedUsageRatio)")
        case .critical:
            logger.error("🚨 内存使用危险: \(memoryInfo.formattedUsageRatio)")
        case .emergency:
            logger.fault("🆘 内存使用紧急: \(memoryInfo.formattedUsageRatio)")
        }
        
        // 触发回调
        onMemoryPressure?(pressureLevel)
        
        // 如果内存压力过高，触发内存警告回调
        if pressureLevel == .critical || pressureLevel == .emergency {
            onMemoryWarning?()
        }
    }
    
    // MARK: - 私有方法
    
    /// 设置系统内存警告通知
    private func setupMemoryWarningNotification() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    /// 处理系统内存警告
    @objc private func handleMemoryWarning() {
        logger.warning("📱 收到系统内存警告")
        onMemoryWarning?()
    }
}

// MARK: - 内存信息数据结构

struct MemoryInfo {
    let currentUsage: Int
    let totalMemory: Int64
    let usageRatio: Double
    let pressureLevel: MemoryMonitor.MemoryPressureLevel
    let formattedCurrentUsage: String
    let formattedTotalMemory: String
    let formattedUsageRatio: String
    
    var isHealthy: Bool {
        return pressureLevel == .normal
    }
    
    var needsAttention: Bool {
        return pressureLevel == .warning || pressureLevel == .critical
    }
    
    var isCritical: Bool {
        return pressureLevel == .emergency
    }
}

// MARK: - 性能监控器

/// 轻量级性能监控器
final class PerformanceMonitor {
    
    static let shared = PerformanceMonitor()
    
    private let logger = Logger(subsystem: "com.mphotos", category: "PerformanceMonitor")
    private var frameRateMonitor: CADisplayLink?
    private var lastTimestamp: CFTimeInterval = 0
    private var frameCount: Int = 0
    private var currentFPS: Double = 0
    
    private init() {}
    
    /// 开始FPS监控
    func startFPSMonitoring() {
        frameRateMonitor = CADisplayLink(target: self, selector: #selector(updateFPS))
        frameRateMonitor?.add(to: .main, forMode: .common)
    }
    
    /// 停止FPS监控
    func stopFPSMonitoring() {
        frameRateMonitor?.invalidate()
        frameRateMonitor = nil
    }
    
    /// 获取当前FPS
    func getCurrentFPS() -> Double {
        return currentFPS
    }
    
    /// 记录操作耗时
    func measureTime<T>(operation: String, block: () throws -> T) rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try block()
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        logger.info("⏱️ \(operation) 耗时: \(String(format: "%.2f", timeElapsed * 1000))ms")
        
        return result
    }
    
    @objc private func updateFPS() {
        guard let frameRateMonitor = frameRateMonitor else { return }
        
        if lastTimestamp == 0 {
            lastTimestamp = frameRateMonitor.timestamp
            return
        }
        
        frameCount += 1
        let elapsed = frameRateMonitor.timestamp - lastTimestamp
        
        if elapsed >= 1.0 {
            currentFPS = Double(frameCount) / elapsed
            frameCount = 0
            lastTimestamp = frameRateMonitor.timestamp
        }
    }
}

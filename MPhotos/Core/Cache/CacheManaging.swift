//
//  CacheManaging.swift
//  MPhotos
//
//  Created by Augment Agent on 2025-08-02.
//

import Foundation
import UIKit
import Photos

/// 缓存管理协议 - 定义统一的缓存接口
protocol CacheManaging {
    // MARK: - 图片缓存
    
    /// 存储图片到缓存
    /// - Parameters:
    ///   - image: 要存储的图片
    ///   - key: 缓存键
    func store(_ image: UIImage, for key: String) async
    
    /// 从缓存获取图片
    /// - Parameter key: 缓存键
    /// - Returns: 缓存的图片，如果不存在则返回nil
    func image(for key: String) async -> UIImage?
    
    /// 移除指定键的图片
    /// - Parameter key: 缓存键
    func removeImage(for key: String) async
    
    /// 清空所有缓存
    func clearAll() async

    /// 清空内存缓存（内存警告时调用）
    func clearMemoryCache()

    // MARK: - PHAsset图片请求
    
    /// 异步请求图片
    /// - Parameters:
    ///   - asset: PHAsset对象
    ///   - targetSize: 目标尺寸
    ///   - contentMode: 内容模式
    /// - Returns: 请求到的图片
    func requestImage(for asset: PHAsset, targetSize: CGSize, contentMode: PHImageContentMode) async -> UIImage?
    
    /// 回调式请求图片（关键方法 - 解决内存泄露）
    /// - Parameters:
    ///   - asset: PHAsset对象
    ///   - targetSize: 目标尺寸
    ///   - completion: 完成回调
    /// - Returns: PHImageRequestID，如果缓存命中则返回PHInvalidImageRequestID
    @discardableResult
    func requestImage(for asset: PHAsset, targetSize: CGSize, completion: @escaping (UIImage?) -> Void) -> PHImageRequestID
    
    // MARK: - 预加载
    
    /// 预加载资源
    /// - Parameters:
    ///   - assets: 要预加载的资源数组
    ///   - targetSize: 目标尺寸
    func preloadAssets(_ assets: [PHAsset], targetSize: CGSize)
    
    /// 停止预加载指定资源
    /// - Parameters:
    ///   - assets: 要停止预加载的资源数组
    ///   - targetSize: 目标尺寸
    func stopPreloading(_ assets: [PHAsset], targetSize: CGSize)
    
    /// 停止所有预加载
    func stopAllPreloading()
    
    // MARK: - 缓存信息
    
    /// 当前内存使用量（字节）
    var currentMemoryUsage: Int { get }
    
    /// 当前缓存项数量
    var cacheCount: Int { get }
    
    /// 缓存命中率
    var hitRate: Double { get }
    
    // MARK: - 缓存配置
    
    /// 设置缓存大小限制
    /// - Parameter limit: 大小限制（字节）
    func setCacheLimit(_ limit: Int)
    
    /// 设置缓存数量限制
    /// - Parameter limit: 数量限制
    func setCountLimit(_ limit: Int)

    /// 获取缓存统计信息
    func getCacheStatistics() -> CacheStatistics

    // MARK: - 缓存键生成
    
    /// 生成缓存键
    /// - Parameters:
    ///   - asset: PHAsset对象
    ///   - size: 尺寸
    ///   - contentMode: 内容模式
    /// - Returns: 缓存键
    func cacheKey(for asset: PHAsset, size: CGSize, contentMode: PHImageContentMode) -> String
}

// MARK: - 默认实现

extension CacheManaging {
    /// 默认的缓存键生成实现
    func cacheKey(for asset: PHAsset, size: CGSize, contentMode: PHImageContentMode = .aspectFill) -> String {
        return "\(asset.localIdentifier)_\(Int(size.width))x\(Int(size.height))_\(contentMode.rawValue)"
    }
    
    /// 便捷方法：使用默认内容模式请求图片
    @discardableResult
    func requestImage(for asset: PHAsset, targetSize: CGSize, completion: @escaping (UIImage?) -> Void) -> PHImageRequestID {
        return requestImage(for: asset, targetSize: targetSize, completion: completion)
    }
}

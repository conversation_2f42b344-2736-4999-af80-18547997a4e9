//
//  ImprovedCacheManager.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import UIKit
import Photos

// MARK: - 📱 改进的缓存管理器
/*
 核心优化策略：
 
 1. 渐进式加载 🎯
    - 两阶段加载：快速低质量 → 高质量替换
    - 智能缓存键管理
    - 自动质量升级
 
 2. 预热区域管理 🔄
    - 可见区域 + 上下2倍高度预热
    - 差分计算减少重复工作
    - 滚动阈值控制更新频率
 
 3. 内存优化 💾
    - 动态内存限制调整
    - 内存警告响应
    - 请求生命周期管理
 
 4. 性能监控 📊
    - 缓存命中率统计
    - 内存使用监控
    - 请求队列管理
 */

class ImprovedCacheManager {
    
    // MARK: - 单例
    static let shared = ImprovedCacheManager()
    
    // MARK: - 核心组件
    private let imageManager = PHCachingImageManager()
    private let memoryCache = NSCache<NSString, UIImage>()
    private let queue = DispatchQueue(label: "ImprovedCacheManager", qos: .userInitiated, attributes: .concurrent)
    
    // MARK: - 请求管理
    private var activeRequests: [String: PHImageRequestID] = [:]
    private var visibleAssets: Set<String> = []
    private let requestQueue = DispatchQueue(label: "requestQueue", qos: .userInitiated)
    
    // MARK: - 统计信息
    private var hitCount: Int = 0
    private var missCount: Int = 0
    
    // MARK: - 配置
    private struct Config {
        static let maxMemoryCost: Int = 150 * 1024 * 1024 // 150MB
        static let maxCacheCount: Int = 300
        static let lowQualityScale: CGFloat = 0.5
        static let highQualityDelay: TimeInterval = 0.1
    }
    
    // MARK: - 初始化
    private init() {
        setupMemoryCache()
        setupImageManager()
        setupMemoryWarningObserver()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - 设置方法
    private func setupMemoryCache() {
        memoryCache.totalCostLimit = Config.maxMemoryCost
        memoryCache.countLimit = Config.maxCacheCount
        
        // 设置缓存清理策略
        memoryCache.evictsObjectsWithDiscardedContent = true
    }
    
    private func setupImageManager() {
        // 配置图片管理器
        imageManager.allowsCachingHighQualityImages = true
    }
    
    private func setupMemoryWarningObserver() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    @objc private func handleMemoryWarning() {
        print("⚠️ 内存警告 - 清理缓存")
        clearMemoryCache()
    }
    
    // MARK: - 公共接口
    
    /// 渐进式加载缩略图
    func loadThumbnailProgressive(for asset: PHAsset, targetSize: CGSize, completion: @escaping (Result<UIImage, Error>) -> Void) {
        let assetId = asset.localIdentifier
        let highQualityKey = cacheKey(for: asset, size: targetSize, quality: "high")
        let lowQualityKey = cacheKey(for: asset, size: targetSize, quality: "low")
        
        // 1. 检查高质量缓存
        if let highQualityImage = memoryCache.object(forKey: highQualityKey as NSString) {
            hitCount += 1
            completion(.success(highQualityImage))
            return
        }
        
        // 2. 检查低质量缓存
        if let lowQualityImage = memoryCache.object(forKey: lowQualityKey as NSString) {
            hitCount += 1
            completion(.success(lowQualityImage))
            
            // 后台加载高质量版本
            loadHighQualityInBackground(asset: asset, targetSize: targetSize, completion: completion)
            return
        }
        
        // 3. 都没有缓存，开始渐进式加载
        missCount += 1
        startProgressiveLoading(asset: asset, targetSize: targetSize, completion: completion)
    }
    
    /// 开始缓存图片
    func startCachingImages(for assets: [PHAsset], targetSize: CGSize) {
        guard !assets.isEmpty else { return }
        
        let options = createImageRequestOptions(deliveryMode: .fastFormat)
        imageManager.startCachingImages(
            for: assets,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        )
    }
    
    /// 停止缓存图片
    func stopCachingImages(for assets: [PHAsset], targetSize: CGSize) {
        guard !assets.isEmpty else { return }
        
        imageManager.stopCachingImages(
            for: assets,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: nil
        )
    }
    
    /// 更新可见资产
    func updateVisibleAssets(visible: [PHAsset], targetSize: CGSize) {
        let newVisibleIds = Set(visible.map { $0.localIdentifier })
        
        // 取消不再可见的请求
        let removedIds = visibleAssets.subtracting(newVisibleIds)
        for assetId in removedIds {
            cancelRequest(for: assetId)
        }
        
        visibleAssets = newVisibleIds
    }
    
    /// 清理内存缓存
    func clearMemoryCache() {
        memoryCache.removeAllObjects()
        print("🧹 内存缓存已清理")
    }
    
    /// 获取缓存统计
    func getCacheStatistics() -> (hitRate: Double, memoryUsage: Int, activeRequests: Int) {
        let totalRequests = hitCount + missCount
        let hitRate = totalRequests > 0 ? Double(hitCount) / Double(totalRequests) : 0
        let memoryUsage = getCurrentMemoryUsage()
        let activeRequestCount = activeRequests.count
        
        return (hitRate: hitRate, memoryUsage: memoryUsage, activeRequests: activeRequestCount)
    }
    
    // MARK: - 私有方法
    
    private func startProgressiveLoading(asset: PHAsset, targetSize: CGSize, completion: @escaping (Result<UIImage, Error>) -> Void) {
        let assetId = asset.localIdentifier
        let lowQualitySize = CGSize(
            width: targetSize.width * Config.lowQualityScale,
            height: targetSize.height * Config.lowQualityScale
        )
        
        // 1. 先加载低质量版本
        let lowQualityOptions = createImageRequestOptions(deliveryMode: .fastFormat)
        let lowQualityRequestId = imageManager.requestImage(
            for: asset,
            targetSize: lowQualitySize,
            contentMode: .aspectFill,
            options: lowQualityOptions
        ) { [weak self] image, info in
            guard let self = self, let image = image else { return }
            
            let lowQualityKey = self.cacheKey(for: asset, size: targetSize, quality: "low")
            let cost = self.calculateImageCost(image)
            self.memoryCache.setObject(image, forKey: lowQualityKey as NSString, cost: cost)
            
            completion(.success(image))
            
            // 延迟加载高质量版本
            DispatchQueue.main.asyncAfter(deadline: .now() + Config.highQualityDelay) {
                self.loadHighQualityInBackground(asset: asset, targetSize: targetSize, completion: completion)
            }
        }
        
        // 记录请求
        requestQueue.async {
            self.activeRequests[assetId] = lowQualityRequestId
        }
    }
    
    private func loadHighQualityInBackground(asset: PHAsset, targetSize: CGSize, completion: @escaping (Result<UIImage, Error>) -> Void) {
        let assetId = asset.localIdentifier
        let highQualityKey = cacheKey(for: asset, size: targetSize, quality: "high")
        
        // 检查是否已经有高质量版本
        if memoryCache.object(forKey: highQualityKey as NSString) != nil {
            return
        }
        
        let highQualityOptions = createImageRequestOptions(deliveryMode: .highQualityFormat)
        let highQualityRequestId = imageManager.requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: highQualityOptions
        ) { [weak self] image, info in
            guard let self = self, let image = image else { return }
            
            let cost = self.calculateImageCost(image)
            self.memoryCache.setObject(image, forKey: highQualityKey as NSString, cost: cost)
            
            // 清理对应的低质量版本
            let lowQualityKey = self.cacheKey(for: asset, size: targetSize, quality: "low")
            self.memoryCache.removeObject(forKey: lowQualityKey as NSString)
            
            completion(.success(image))
        }
        
        // 记录请求
        requestQueue.async {
            self.activeRequests[assetId] = highQualityRequestId
        }
    }
    
    private func cancelRequest(for assetId: String) {
        requestQueue.async {
            if let requestId = self.activeRequests.removeValue(forKey: assetId) {
                self.imageManager.cancelImageRequest(requestId)
            }
        }
    }
    
    private func createImageRequestOptions(deliveryMode: PHImageRequestOptionsDeliveryMode) -> PHImageRequestOptions {
        let options = PHImageRequestOptions()
        options.deliveryMode = deliveryMode
        options.resizeMode = .exact
        options.isNetworkAccessAllowed = true
        options.isSynchronous = false
        return options
    }
    
    private func cacheKey(for asset: PHAsset, size: CGSize, quality: String) -> String {
        return "\(asset.localIdentifier)_\(Int(size.width))x\(Int(size.height))_\(quality)"
    }
    
    private func calculateImageCost(_ image: UIImage) -> Int {
        let bytesPerPixel = 4 // RGBA
        let width = Int(image.size.width * image.scale)
        let height = Int(image.size.height * image.scale)
        return width * height * bytesPerPixel
    }
    
    private func getCurrentMemoryUsage() -> Int {
        // 简化的内存使用计算
        return memoryCache.totalCostLimit
    }
}

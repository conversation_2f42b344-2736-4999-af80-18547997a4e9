//
//  LibrarySettings.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import Foundation

/// 图库设置模型 - 管理图库的各种显示和行为设置
struct LibrarySettings: Codable, Equatable {
    
    // MARK: - 布局设置
    
    /// 网格布局类型枚举
    enum LayoutType: Int, CaseIterable, Codable {
        case threeColumns = 3   // 3列布局
        case fiveColumns = 5    // 5列布局
        case tenColumns = 10    // 10列布局
        
        /// 显示名称
        var displayName: String {
            switch self {
            case .threeColumns:
                return "3列"
            case .fiveColumns:
                return "5列"
            case .tenColumns:
                return "10列"
            }
        }
        
        /// 每行的列数
        var columnsPerRow: Int {
            return self.rawValue
        }
    }
    
    /// 排序方式枚举
    enum SortOrder: Int, CaseIterable, Codable {
        case newestFirst = 0    // 最新在上
        case oldestFirst = 1    // 最新在下（最旧在上）
        
        /// 显示名称
        var displayName: String {
            switch self {
            case .newestFirst:
                return "最新在上"
            case .oldestFirst:
                return "最新在下"
            }
        }
    }
    
    // MARK: - 设置属性
    
    /// 当前布局类型
    var layoutType: LayoutType = .threeColumns
    
    /// 当前排序方式
    var sortOrder: SortOrder = .oldestFirst

    /// 是否按拍摄日期分组
    var groupByDate: Bool = true

    /// 是否显示日期分组标题
    var showDateHeaders: Bool = true
    
    /// 选中的相册标识符集合
    var selectedAlbumIdentifiers: Set<String> = ["AllPhotos", "Recents"]
    
    /// 是否显示快速选择选项
    var showQuickSelectionOptions: Bool = true
    
    /// 是否显示Live Photo标识
    var showLivePhotoIndicator: Bool = true
    
    /// 是否显示视频时长标识
    var showVideoDurationIndicator: Bool = true
    
    /// 缩略图的圆角半径
    var thumbnailCornerRadius: CGFloat = 8.0
    
    /// 网格间距
    var gridSpacing: CGFloat = 2.0
    
    // MARK: - 默认设置
    
    /// 获取默认设置
    /// - Returns: 默认的LibrarySettings实例
    static func defaultSettings() -> LibrarySettings {
        return LibrarySettings()
    }
    
    // MARK: - 设置验证
    
    /// 验证设置是否有效
    /// - Returns: 设置是否有效
    func isValid() -> Bool {
        // 检查基本的设置有效性
        return gridSpacing >= 0 && thumbnailCornerRadius >= 0
    }
    
    // MARK: - 设置比较
    
    /// 比较两个设置是否相等
    /// - Parameter other: 另一个设置实例
    /// - Returns: 是否相等
    func isEqual(to other: LibrarySettings) -> Bool {
        return self.layoutType == other.layoutType &&
               self.sortOrder == other.sortOrder &&
               self.groupByDate == other.groupByDate &&
               self.showDateHeaders == other.showDateHeaders &&
               self.selectedAlbumIdentifiers == other.selectedAlbumIdentifiers &&
               self.showQuickSelectionOptions == other.showQuickSelectionOptions &&
               self.showLivePhotoIndicator == other.showLivePhotoIndicator &&
               self.showVideoDurationIndicator == other.showVideoDurationIndicator &&
               abs(self.thumbnailCornerRadius - other.thumbnailCornerRadius) < 0.001 &&
               abs(self.gridSpacing - other.gridSpacing) < 0.001
    }
}

/// 相册信息模型
struct AlbumInfo: Codable, Identifiable {
    /// 相册标识符
    let id: String
    
    /// 相册显示名称
    let displayName: String
    
    /// 相册类型
    let albumType: AlbumType
    
    /// 照片数量
    let photoCount: Int
    
    /// 是否被选中显示
    var isSelected: Bool = false
    
    /// 相册类型枚举
    enum AlbumType: String, Codable, CaseIterable {
        case allPhotos = "AllPhotos"        // 所有照片
        case recents = "Recents"           // 最近项目
        case favorites = "Favorites"       // 个人收藏
        case screenshots = "Screenshots"   // 截图
        case videos = "Videos"            // 视频
        case livePhotos = "LivePhotos"    // Live Photos
        case panoramas = "Panoramas"      // 全景照片
        case userAlbum = "UserAlbum"      // 用户创建的相册
        case smartAlbum = "SmartAlbum"    // 智能相册
        
        /// 显示名称
        var displayName: String {
            switch self {
            case .allPhotos:
                return "所有照片"
            case .recents:
                return "最近项目"
            case .favorites:
                return "个人收藏"
            case .screenshots:
                return "截图"
            case .videos:
                return "视频"
            case .livePhotos:
                return "Live Photos"
            case .panoramas:
                return "全景照片"
            case .userAlbum:
                return "个人相册"
            case .smartAlbum:
                return "智能相册"
            }
        }
    }
} 
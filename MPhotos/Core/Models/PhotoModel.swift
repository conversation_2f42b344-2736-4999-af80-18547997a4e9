//
//  PhotoModel.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import Foundation
import Photos

/// 照片数据模型 - 封装PHAsset和相关的展示数据
struct PhotoModel: Identifiable, Hashable {
    
    // MARK: - 基础属性
    
    /// 唯一标识符
    let id: String
    
    /// PHAsset对象，用于获取照片数据
    let asset: PHAsset
    
    /// 照片的创建日期
    let creationDate: Date?
    
    /// 照片的修改日期
    let modificationDate: Date?
    
    /// 媒体类型（图片、视频等）
    let mediaType: PHAssetMediaType
    
    /// 媒体子类型（Live Photo、全景图等）
    let mediaSubtypes: PHAssetMediaSubtype
    
    /// 照片的像素尺寸
    let pixelSize: CGSize
    
    /// 照片的持续时间（视频用）
    let duration: TimeInterval
    
    /// 是否为Live Photo
    var isLivePhoto: Bool {
        return mediaSubtypes.contains(.photoLive)
    }
    
    /// 是否为视频
    var isVideo: Bool {
        return mediaType == .video
    }
    
    /// 是否为全景图
    var isPanorama: Bool {
        return mediaSubtypes.contains(.photoPanorama)
    }
    
    /// 是否为截图
    var isScreenshot: Bool {
        return mediaSubtypes.contains(.photoScreenshot)
    }
    
    /// 是否在收藏夹中
    var isFavorite: Bool
    
    // MARK: - 扩展属性
    
    /// 是否被选中（用于批量操作）
    var isSelected: Bool = false
    
    /// 缓存的缩略图
    var thumbnailImage: Any?
    
    /// 显示的日期格式化字符串
    var displayDateString: String {
        guard let date = creationDate else { return "未知日期" }
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
    
    /// 文件大小（字节）
    let fileSize: Int64
    
    // MARK: - 初始化
    
    /// 从PHAsset初始化PhotoModel（轻量级，快速初始化）
    /// - Parameter asset: PHAsset对象
    init(from asset: PHAsset) {
        self.id = asset.localIdentifier
        self.asset = asset
        self.creationDate = asset.creationDate
        self.modificationDate = asset.modificationDate
        self.mediaType = asset.mediaType
        self.mediaSubtypes = asset.mediaSubtypes
        self.pixelSize = CGSize(width: asset.pixelWidth, height: asset.pixelHeight)
        self.duration = asset.duration
        self.isFavorite = asset.isFavorite
        
        // 延迟获取文件大小，避免初始化时的性能瓶颈
        self.fileSize = 0
    }
    
    // MARK: - Hashable
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: PhotoModel, rhs: PhotoModel) -> Bool {
        return lhs.id == rhs.id
    }
    
    // MARK: - 辅助方法
    
    /// 获取格式化的文件大小字符串
    /// - Returns: 格式化的文件大小
    func formattedFileSize() -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
    
    /// 获取格式化的持续时间字符串（用于视频）
    /// - Returns: 格式化的时间字符串
    func formattedDuration() -> String {
        guard isVideo && duration > 0 else { return "" }
        
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    /// 获取照片的详细信息字符串
    /// - Returns: 包含分辨率、文件大小等信息的字符串
    func detailInfoString() -> String {
        let resolution = "\(Int(pixelSize.width))×\(Int(pixelSize.height))"
        let size = formattedFileSize()
        var details = [resolution, size]
        
        if isVideo {
            details.append(formattedDuration())
        }
        
        return details.joined(separator: " • ")
    }
} 
//
//  BaseViewController.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import UIKit

/// 基础视图控制器 - 提供通用功能和生命周期管理
class BaseViewController: UIViewController {
    
    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindViewModel()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        setupNavigationBar()
    }
    
    // MARK: - 设置方法（子类重写）
    
    /// 设置UI界面 - 子类需要重写此方法来设置具体的UI
    func setupUI() {
        view.backgroundColor = .systemBackground
    }
    
    /// 绑定ViewModel - 子类需要重写此方法来绑定数据
    func bindViewModel() {
        // 默认实现为空，子类可以重写
    }
    
    /// 设置导航栏 - 子类可以重写此方法来定制导航栏
    func setupNavigationBar() {
        // 默认导航栏设置
        navigationController?.navigationBar.prefersLargeTitles = false
        navigationController?.navigationBar.tintColor = .systemBlue
    }
    
    // MARK: - 通用UI辅助方法
    
    /// 显示加载指示器
    func showLoadingIndicator() {
        let activityIndicator = UIActivityIndicatorView(style: .medium)
        activityIndicator.center = view.center
        activityIndicator.startAnimating()
        activityIndicator.tag = 999 // 标记用于移除
        view.addSubview(activityIndicator)
    }
    
    /// 隐藏加载指示器
    func hideLoadingIndicator() {
        view.subviews.first(where: { $0.tag == 999 })?.removeFromSuperview()
    }
    
    /// 显示错误提示
    /// - Parameters:
    ///   - title: 标题
    ///   - message: 错误信息
    func showErrorAlert(title: String = "错误", message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
} 
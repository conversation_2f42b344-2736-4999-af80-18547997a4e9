//
//  Coordinator.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import UIKit

/// 协调器基础协议 - 管理应用中的导航和视图控制器生命周期
protocol Coordinator: AnyObject {
    /// 子协调器数组，用于管理协调器的层级关系
    var childCoordinators: [Coordinator] { get set }
    
    /// 协调器的导航控制器
    var navigationController: UINavigationController { get set }
    
    /// 启动协调器
    func start()
    
    /// 添加子协调器
    /// - Parameter coordinator: 要添加的子协调器
    func addChildCoordinator(_ coordinator: Coordinator)
    
    /// 移除子协调器
    /// - Parameter coordinator: 要移除的子协调器
    func removeChildCoordinator(_ coordinator: Coordinator)
}

extension Coordinator {
    /// 默认添加子协调器实现
    func addChildCoordinator(_ coordinator: Coordinator) {
        childCoordinators.append(coordinator)
    }
    
    /// 默认移除子协调器实现
    func removeChildCoordinator(_ coordinator: Coordinator) {
        childCoordinators.removeAll { $0 === coordinator }
    }
}

/// Tab模块协议 - 定义Tab页面的基础功能
protocol TabModuleProtocol {
    /// Tab栏项目配置
    var tabBarItem: UITabBarItem { get }
    
    /// 根视图控制器
    var rootViewController: UIViewController { get }
    
    /// 处理深链接
    /// - Parameter url: 深链接URL
    /// - Returns: 是否成功处理
    func handleDeepLink(_ url: URL) -> Bool
} 
//
//  BaseViewModel.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import Foundation

/// 基础ViewModel - 提供MVVM架构的数据绑定基础
class BaseViewModel: ObservableObject {
    
    /// 加载状态枚举
    enum LoadingState {
        case idle       // 空闲状态
        case loading    // 加载中
        case loaded     // 加载完成
        case error(String) // 加载出错
    }
    
    // MARK: - Published Properties
    
    /// 当前加载状态
    @Published var loadingState: LoadingState = .idle
    
    /// 是否正在加载
    var isLoading: Bool {
        switch loadingState {
        case .loading:
            return true
        default:
            return false
        }
    }
    
    // MARK: - 初始化
    init() {
        // 默认初始化
        setupBindings()
    }
    
    // MARK: - 抽象方法（子类重写）
    
    /// 设置数据绑定 - 子类可以重写此方法来设置特定的数据绑定
    func setupBindings() {
        // 默认实现为空，子类可以重写
    }
    
    /// 加载数据 - 子类需要重写此方法来实现具体的数据加载逻辑
    func loadData() {
        // 默认实现为空，子类需要重写
    }
    
    /// 刷新数据 - 子类可以重写此方法来实现数据刷新逻辑
    func refreshData() {
        loadData()
    }
    
    // MARK: - 状态管理辅助方法
    
    /// 设置加载状态为加载中
    func setLoading() {
        DispatchQueue.main.async {
            self.loadingState = .loading
        }
    }
    
    /// 设置加载状态为加载完成
    func setLoaded() {
        DispatchQueue.main.async {
            self.loadingState = .loaded
        }
    }
    
    /// 设置加载状态为错误
    /// - Parameter errorMessage: 错误信息
    func setError(_ errorMessage: String) {
        DispatchQueue.main.async {
            self.loadingState = .error(errorMessage)
        }
    }
} 
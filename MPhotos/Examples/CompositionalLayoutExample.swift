import UIKit

/// CompositionalLayout 使用示例
/// 展示如何在 MPhotos 项目中集成和使用响应式网格布局
class CompositionalLayoutExampleViewController: UIViewController {
    
    // MARK: - UI 组件
    
    private var collectionView: UICollectionView!
    private var debugLabel: UILabel!
    private var spacingSlider: UISlider!
    private var spacingLabel: UILabel!
    
    // MARK: - 数据
    
    private var photos: [String] = []
    private var currentSpacing: CGFloat = 2.0
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupData()
        setupLayout()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        updateDebugInfo()
    }
    
    // MARK: - UI 设置
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "响应式网格布局示例"
        
        // 创建调试标签
        debugLabel = UILabel()
        debugLabel.numberOfLines = 0
        debugLabel.font = UIFont.monospacedSystemFont(ofSize: 12, weight: .regular)
        debugLabel.textColor = .secondaryLabel
        debugLabel.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(debugLabel)
        
        // 创建间距控制
        spacingLabel = UILabel()
        spacingLabel.text = "间距: 2.0pt"
        spacingLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        spacingLabel.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(spacingLabel)
        
        spacingSlider = UISlider()
        spacingSlider.minimumValue = 0
        spacingSlider.maximumValue = 10
        spacingSlider.value = 2.0
        spacingSlider.addTarget(self, action: #selector(spacingChanged(_:)), for: .valueChanged)
        spacingSlider.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(spacingSlider)
        
        // 创建 CollectionView（使用响应式布局）
        let layout = CompositionalLayoutManager.shared.createResponsiveGridLayout(customSpacing: currentSpacing)
        collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .systemBackground
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(ExamplePhotoCell.self, forCellWithReuseIdentifier: "PhotoCell")
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(collectionView)
        
        // 设置约束
        NSLayoutConstraint.activate([
            // 调试标签
            debugLabel.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 8),
            debugLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16),
            debugLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16),
            
            // 间距标签
            spacingLabel.topAnchor.constraint(equalTo: debugLabel.bottomAnchor, constant: 16),
            spacingLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16),
            
            // 间距滑块
            spacingSlider.centerYAnchor.constraint(equalTo: spacingLabel.centerYAnchor),
            spacingSlider.leadingAnchor.constraint(equalTo: spacingLabel.trailingAnchor, constant: 16),
            spacingSlider.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16),
            spacingSlider.widthAnchor.constraint(greaterThanOrEqualToConstant: 200),
            
            // CollectionView
            collectionView.topAnchor.constraint(equalTo: spacingSlider.bottomAnchor, constant: 16),
            collectionView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    private func setupData() {
        // 生成示例数据
        photos = (1...100).map { "Photo \($0)" }
    }
    
    private func setupLayout() {
        updateLayout()
    }
    
    // MARK: - 布局更新
    
    @objc private func spacingChanged(_ sender: UISlider) {
        currentSpacing = CGFloat(sender.value)
        spacingLabel.text = "间距: \(String(format: "%.1f", currentSpacing))pt"
        updateLayout()
    }
    
    private func updateLayout() {
        let newLayout = CompositionalLayoutManager.shared.createResponsiveGridLayout(customSpacing: currentSpacing)
        collectionView.setCollectionViewLayout(newLayout, animated: true)
        
        // 延迟更新调试信息，等待布局完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.updateDebugInfo()
        }
    }
    
    private func updateDebugInfo() {
        guard collectionView.bounds.width > 0 else { return }
        
        let layoutInfo = CompositionalLayoutManager.shared.getLayoutInfo(
            for: collectionView.bounds.width,
            customSpacing: currentSpacing
        )
        
        let validation = CompositionalLayoutManager.shared.validateLayoutConfiguration(
            containerWidth: collectionView.bounds.width,
            customSpacing: currentSpacing
        )
        
        var debugText = layoutInfo
        
        if !validation.isValid || !validation.suggestions.isEmpty {
            debugText += "\n\n⚠️ 布局建议:"
            validation.suggestions.forEach { suggestion in
                debugText += "\n• \(suggestion)"
            }
        }
        
        debugLabel.text = debugText
    }
}

// MARK: - UICollectionViewDataSource

extension CompositionalLayoutExampleViewController: UICollectionViewDataSource {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return photos.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "PhotoCell", for: indexPath) as! ExamplePhotoCell
        cell.configure(with: photos[indexPath.item], index: indexPath.item)
        return cell
    }
}

// MARK: - UICollectionViewDelegate

extension CompositionalLayoutExampleViewController: UICollectionViewDelegate {
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: true)
        
        let alert = UIAlertController(
            title: "照片选中",
            message: "选中了 \(photos[indexPath.item])",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - 示例 Cell

class ExamplePhotoCell: UICollectionViewCell {
    
    private let imageView = UIImageView()
    private let titleLabel = UILabel()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        // 设置背景
        backgroundColor = .systemGray5
        layer.cornerRadius = 8
        clipsToBounds = true
        
        // 设置图片视图
        imageView.contentMode = .scaleAspectFill
        imageView.backgroundColor = .systemGray4
        imageView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(imageView)
        
        // 设置标题标签
        titleLabel.textAlignment = .center
        titleLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        titleLabel.textColor = .label
        titleLabel.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.8)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(titleLabel)
        
        // 设置约束
        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            titleLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            titleLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            titleLabel.heightAnchor.constraint(equalToConstant: 24)
        ])
    }
    
    func configure(with title: String, index: Int) {
        titleLabel.text = title
        
        // 生成不同颜色的背景，模拟照片
        let colors: [UIColor] = [.systemBlue, .systemGreen, .systemOrange, .systemPurple, .systemRed, .systemYellow]
        imageView.backgroundColor = colors[index % colors.count]
    }
}

// MARK: - 使用说明

/*
 
 ## 如何使用这个示例:
 
 1. **创建视图控制器**:
    ```swift
    let exampleVC = CompositionalLayoutExampleViewController()
    navigationController?.pushViewController(exampleVC, animated: true)
    ```
 
 2. **观察响应式行为**:
    - 旋转设备查看列数变化
    - 调整间距滑块查看效果
    - 查看调试信息了解布局详情
 
 3. **集成到你的项目**:
    - 复制 CompositionalLayoutManager 的使用方式
    - 替换现有的 UICollectionViewFlowLayout
    - 移除复杂的尺寸计算逻辑
 
 ## 核心代码模式:
 
 ```swift
 // 1. 创建布局
 let layout = CompositionalLayoutManager.shared.createResponsiveGridLayout(customSpacing: 2.0)
 
 // 2. 应用到 CollectionView
 collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
 
 // 3. 动态更新布局
 let newLayout = CompositionalLayoutManager.shared.createResponsiveGridLayout(customSpacing: newSpacing)
 collectionView.setCollectionViewLayout(newLayout, animated: true)
 ```
 
 */

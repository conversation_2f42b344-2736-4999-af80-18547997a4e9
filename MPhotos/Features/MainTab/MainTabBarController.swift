//
//  MainTabBarController.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import UIKit

/// 主TabBar控制器 - 管理应用的四个主要功能模块
class MainTabBarController: UITabBarController {
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupTabBarController()
        setupTabBarAppearance()
    }
    
    // MARK: - 设置方法
    
    /// 设置TabBar控制器
    private func setupTabBarController() {
        // 创建各个Tab的视图控制器
        let photoLibraryVC = createPhotoLibraryTab()
        let cleanerVC = createCleanerTab()
        let albumsVC = createAlbumsTab()
        let toolsVC = createToolsTab()
        
        // 设置视图控制器数组
        viewControllers = [photoLibraryVC, cleanerVC, albumsVC, toolsVC]
        
        // 设置默认选中的Tab（图库）
        selectedIndex = 0
        
        print("✅ MainTabBarController 初始化完成，包含 \(viewControllers?.count ?? 0) 个Tab")
    }
    
    /// 设置TabBar外观
    private func setupTabBarAppearance() {
        // 设置TabBar背景
        tabBar.backgroundColor = .systemBackground
        tabBar.barTintColor = .systemBackground
        
        // 设置TabBar项目颜色
        tabBar.tintColor = .systemBlue
        tabBar.unselectedItemTintColor = .systemGray
        
        // 设置TabBar样式
        if #available(iOS 15.0, *) {
            let appearance = UITabBarAppearance()
            appearance.configureWithDefaultBackground()
            appearance.backgroundColor = .systemBackground
            
            tabBar.standardAppearance = appearance
            tabBar.scrollEdgeAppearance = appearance
        }
        
        print("✅ TabBar 外观设置完成")
    }
    
    // MARK: - Tab创建方法
    
    /// 创建图库Tab
    /// - Returns: 图库模块的导航控制器
    private func createPhotoLibraryTab() -> UINavigationController {
        // 创建真正的图库视图控制器
        let photoLibraryVC = PhotoLibraryViewController()
        photoLibraryVC.title = "图库"
        
        // 创建导航控制器
        let navigationController = UINavigationController(rootViewController: photoLibraryVC)
        
        // 设置Tab项目
        navigationController.tabBarItem = UITabBarItem(
            title: "图库",
            image: UIImage(systemName: "photo.on.rectangle"),
            selectedImage: UIImage(systemName: "photo.on.rectangle.fill")
        )
        
        return navigationController
    }
    
    /// 创建清理Tab
    /// - Returns: 清理模块的导航控制器
    private func createCleanerTab() -> UINavigationController {
        let cleanerVC = CleanerPlaceholderViewController()
        cleanerVC.title = "为你推荐"
        
        let navigationController = UINavigationController(rootViewController: cleanerVC)
        navigationController.tabBarItem = UITabBarItem(
            title: "为你推荐",
            image: UIImage(systemName: "sparkles"),
            selectedImage: UIImage(systemName: "sparkles")
        )
        
        return navigationController
    }
    
    /// 创建相簿Tab
    /// - Returns: 相簿模块的导航控制器
    private func createAlbumsTab() -> UINavigationController {
        let albumsVC = AlbumsPlaceholderViewController()
        albumsVC.title = "相簿"
        
        let navigationController = UINavigationController(rootViewController: albumsVC)
        navigationController.tabBarItem = UITabBarItem(
            title: "相簿",
            image: UIImage(systemName: "folder"),
            selectedImage: UIImage(systemName: "folder.fill")
        )
        
        return navigationController
    }
    
    /// 创建功能Tab
    /// - Returns: 功能模块的导航控制器
    private func createToolsTab() -> UINavigationController {
        let toolsVC = ToolsPlaceholderViewController()
        toolsVC.title = "搜索"
        
        let navigationController = UINavigationController(rootViewController: toolsVC)
        navigationController.tabBarItem = UITabBarItem(
            title: "搜索",
            image: UIImage(systemName: "magnifyingglass"),
            selectedImage: UIImage(systemName: "magnifyingglass")
        )
        
        return navigationController
    }
}

// MARK: - 占位视图控制器

/// 图库占位视图控制器 - 临时占位，后续会被真正的PhotoLibraryViewController替换
class PhotoLibraryPlaceholderViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .systemBackground
        
        let label = UILabel()
        label.text = "图库功能开发中..."
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        label.textColor = .label
        label.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(label)
        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            label.centerYAnchor.constraint(equalTo: view.centerYAnchor)
        ])
    }
}

/// 清理占位视图控制器
class CleanerPlaceholderViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .systemBackground
        
        let label = UILabel()
        label.text = "为你推荐功能开发中..."
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 18, weight: .medium)  
        label.textColor = .label
        label.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(label)
        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            label.centerYAnchor.constraint(equalTo: view.centerYAnchor)
        ])
    }
}

/// 相簿占位视图控制器
class AlbumsPlaceholderViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .systemBackground
        
        let label = UILabel()
        label.text = "相簿功能开发中..."
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        label.textColor = .label
        label.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(label)
        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            label.centerYAnchor.constraint(equalTo: view.centerYAnchor)
        ])
    }
}

/// 功能占位视图控制器
class ToolsPlaceholderViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .systemBackground
        
        let label = UILabel()
        label.text = "搜索功能开发中..."
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        label.textColor = .label
        label.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(label)
        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            label.centerYAnchor.constraint(equalTo: view.centerYAnchor)
        ])
    }
} 
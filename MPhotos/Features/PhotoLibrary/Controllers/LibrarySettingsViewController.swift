//
//  LibrarySettingsViewController.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import UIKit

/// 图库设置代理协议
protocol LibrarySettingsDelegate: AnyObject {
    /// 设置发生变更时的回调
    /// - Parameter settings: 新的设置
    func settingsDidChange(_ settings: LibrarySettings)
}

/// 图库设置视图控制器 - 管理图库的各种设置选项
class LibrarySettingsViewController: BaseViewController {
    
    // MARK: - 属性
    
    /// 设置代理
    weak var delegate: LibrarySettingsDelegate?
    
    /// 表格视图
    private var tableView: UITableView!
    
    /// 设置服务
    private let settingsService = SettingsService.shared
    
    /// 当前设置
    private var currentSettings: LibrarySettings
    
    // MARK: - 设置项枚举
    
    private enum SettingSection: Int, CaseIterable {
        case display = 0    // 显示设置
        case albums = 1     // 相册管理
        
        var title: String {
            switch self {
            case .display:
                return "显示设置"
            case .albums:
                return "相册管理"
            }
        }
        
        var footer: String? {
            switch self {
            case .display:
                return "调整图库的显示方式和布局"
            case .albums:
                return "选择要在图库中显示的相册"
            }
        }
    }
    
    private enum DisplaySettingRow: Int, CaseIterable {
        case layout = 0      // 布局
        case sortOrder = 1   // 排序
        case dateGrouping = 2 // 日期分组

        var title: String {
            switch self {
            case .layout:
                return "布局"
            case .sortOrder:
                return "排序"
            case .dateGrouping:
                return "按拍摄日期整理"
            }
        }

        var subtitle: String? {
            switch self {
            case .layout:
                return "选择照片网格的列数"
            case .sortOrder:
                return "选择照片的排序方式"
            case .dateGrouping:
                return "按拍摄日期对照片进行分组显示"
            }
        }
    }
    
    // MARK: - 初始化
    
    init() {
        self.currentSettings = SettingsService.shared.librarySettings
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        self.currentSettings = SettingsService.shared.librarySettings
        super.init(coder: coder)
    }
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        currentSettings = settingsService.librarySettings
    }
    
    // MARK: - UI设置
    
    override func setupUI() {
        super.setupUI()
        setupTableView()
    }
    
    override func setupNavigationBar() {
        super.setupNavigationBar()
        
        title = "图库设置"
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            title: "取消",
            style: .plain,
            target: self,
            action: #selector(cancelButtonTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            title: "完成",
            style: .done,
            target: self,
            action: #selector(doneButtonTapped)
        )
    }
    
    /// 设置表格视图
    private func setupTableView() {
        tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.translatesAutoresizingMaskIntoConstraints = false
        
        // 注册Cell
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "SettingCell")
        tableView.register(SwitchTableViewCell.self, forCellReuseIdentifier: "SwitchCell")
        
        view.addSubview(tableView)
        
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    // MARK: - 按钮事件
    
    @objc private func cancelButtonTapped() {
        dismiss(animated: true)
        print("❌ 取消设置更改")
    }
    
    @objc private func doneButtonTapped() {
        // 保存设置
        settingsService.librarySettings = currentSettings
        
        // 通知代理
        delegate?.settingsDidChange(currentSettings)
        
        dismiss(animated: true)
        print("✅ 设置已保存并应用")
    }
    
    // MARK: - 设置更新方法
    
    /// 更新布局设置
    /// - Parameter layoutType: 新的布局类型
    private func updateLayout(_ layoutType: LibrarySettings.LayoutType) {
        currentSettings.layoutType = layoutType
        tableView.reloadRows(at: [IndexPath(row: DisplaySettingRow.layout.rawValue, section: SettingSection.display.rawValue)], with: .none)
        print("🎛️ 布局设置更新为: \(layoutType.displayName)")
    }
    
    /// 更新排序设置
    /// - Parameter sortOrder: 新的排序方式
    private func updateSortOrder(_ sortOrder: LibrarySettings.SortOrder) {
        currentSettings.sortOrder = sortOrder

        // 持久化设置到SettingsService（这会自动触发通知）
        settingsService.updateSortOrder(sortOrder)

        // 🔧 修复：移除重复的代理通知，因为SettingsService已经会通知所有代理
        // delegate?.settingsDidChange(currentSettings)

        // 更新UI显示
        tableView.reloadRows(at: [IndexPath(row: DisplaySettingRow.sortOrder.rawValue, section: SettingSection.display.rawValue)], with: .none)
        print("📊 排序设置更新为: \(sortOrder.displayName)")
    }
    

}

// MARK: - UITableViewDataSource

extension LibrarySettingsViewController: UITableViewDataSource {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return SettingSection.allCases.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let settingSection = SettingSection(rawValue: section) else { return 0 }
        
        switch settingSection {
        case .display:
            return DisplaySettingRow.allCases.count
        case .albums:
            return 1 // 暂时只有一个管理相册的选项
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let settingSection = SettingSection(rawValue: indexPath.section) else {
            return UITableViewCell()
        }
        
        switch settingSection {
        case .display:
            return configureDisplayCell(for: indexPath)
        case .albums:
            return configureAlbumCell(for: indexPath)
        }
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return SettingSection(rawValue: section)?.title
    }
    
    func tableView(_ tableView: UITableView, titleForFooterInSection section: Int) -> String? {
        return SettingSection(rawValue: section)?.footer
    }
    
    // MARK: - Cell配置方法
    
    /// 配置显示设置Cell
    private func configureDisplayCell(for indexPath: IndexPath) -> UITableViewCell {
        guard let row = DisplaySettingRow(rawValue: indexPath.row) else {
            return UITableViewCell()
        }
        
        switch row {
        case .layout, .sortOrder:
            let cell = tableView.dequeueReusableCell(withIdentifier: "SettingCell", for: indexPath)
            cell.textLabel?.text = row.title
            cell.accessoryType = .disclosureIndicator

            if row == .layout {
                cell.detailTextLabel?.text = currentSettings.layoutType.displayName
            } else if row == .sortOrder {
                cell.detailTextLabel?.text = currentSettings.sortOrder.displayName
            }

            return cell

        case .dateGrouping:
            let cell = tableView.dequeueReusableCell(withIdentifier: "SwitchCell", for: indexPath) as! SwitchTableViewCell
            cell.configure(
                title: row.title,
                isOn: currentSettings.groupByDate
            ) { [weak self] isOn in
                self?.currentSettings.groupByDate = isOn
                self?.settingsService.updateDateGrouping(isOn)
                print("📅 日期分组切换为: \(isOn ? "开启" : "关闭")")
            }
            return cell
        }
    }
    
    /// 配置相册管理Cell
    private func configureAlbumCell(for indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "SettingCell", for: indexPath)
        cell.textLabel?.text = "显示相册"
        cell.accessoryType = .disclosureIndicator
        cell.detailTextLabel?.text = "\(currentSettings.selectedAlbumIdentifiers.count) 个相册"
        return cell
    }
}

// MARK: - UITableViewDelegate

extension LibrarySettingsViewController: UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        guard let settingSection = SettingSection(rawValue: indexPath.section) else { return }
        
        switch settingSection {
        case .display:
            handleDisplaySettingSelection(for: indexPath)
        case .albums:
            handleAlbumSettingSelection(for: indexPath)
        }
    }
    
    /// 处理显示设置选择
    private func handleDisplaySettingSelection(for indexPath: IndexPath) {
        guard let row = DisplaySettingRow(rawValue: indexPath.row) else { return }
        
        switch row {
        case .layout:
            showLayoutSelectionAlert()
        case .sortOrder:
            showSortOrderSelectionAlert()
        case .dateGrouping:
            // 开关类型，由SwitchCell处理
            break
        }
    }
    
    /// 处理相册设置选择
    private func handleAlbumSettingSelection(for indexPath: IndexPath) {
        // TODO: 显示相册选择页面
        print("📂 显示相册选择页面")
    }
    
    /// 显示布局选择提示
    private func showLayoutSelectionAlert() {
        let alert = UIAlertController(title: "布局", message: "选择照片网格的列数", preferredStyle: .actionSheet)
        
        for layoutType in LibrarySettings.LayoutType.allCases {
            let action = UIAlertAction(title: layoutType.displayName, style: .default) { [weak self] _ in
                self?.updateLayout(layoutType)
            }
            if layoutType == currentSettings.layoutType {
                action.setValue(true, forKey: "checked")
            }
            alert.addAction(action)
        }
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        // iPad适配
        if let popover = alert.popoverPresentationController {
            popover.sourceView = tableView
            popover.sourceRect = tableView.rectForRow(at: IndexPath(row: DisplaySettingRow.layout.rawValue, section: SettingSection.display.rawValue))
        }
        
        present(alert, animated: true)
    }
    
    /// 显示排序选择提示
    private func showSortOrderSelectionAlert() {
        let alert = UIAlertController(title: "排序", message: "选择照片的排序方式", preferredStyle: .actionSheet)
        
        for sortOrder in LibrarySettings.SortOrder.allCases {
            let action = UIAlertAction(title: sortOrder.displayName, style: .default) { [weak self] _ in
                self?.updateSortOrder(sortOrder)
            }
            if sortOrder == currentSettings.sortOrder {
                action.setValue(true, forKey: "checked")
            }
            alert.addAction(action)
        }
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        // iPad适配
        if let popover = alert.popoverPresentationController {
            popover.sourceView = tableView
            popover.sourceRect = tableView.rectForRow(at: IndexPath(row: DisplaySettingRow.sortOrder.rawValue, section: SettingSection.display.rawValue))
        }
        
        present(alert, animated: true)
    }


}

// MARK: - 开关Cell

/// 带开关的表格视图Cell
private class SwitchTableViewCell: UITableViewCell {

    private let switchControl = UISwitch()
    private var switchHandler: ((Bool) -> Void)?

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupSwitch()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupSwitch()
    }

    private func setupSwitch() {
        switchControl.addTarget(self, action: #selector(switchValueChanged), for: .valueChanged)
        accessoryView = switchControl
        selectionStyle = .none
    }

    @objc private func switchValueChanged() {
        switchHandler?(switchControl.isOn)
    }

    func configure(title: String, isOn: Bool, handler: @escaping (Bool) -> Void) {
        textLabel?.text = title
        switchControl.isOn = isOn
        switchHandler = handler
    }
}


import UIKit
import Photos

// MARK: - 📱 CollectionView优化实施指南
/*
 核心优化策略：
 
 1. 滚动性能优化 ⚡️
    - 使用prefetchDataSource预加载数据
    - 实现智能的预热区域管理
    - 区分滚动中和静止状态的加载策略
 
 2. Cell复用优化 ♻️
    - 使用representedAssetIdentifier防止图片错位
    - prepareForReuse中彻底清理状态
    - 及时取消不需要的图片请求
 
 3. 内存管理优化 💾
    - 监听内存警告并及时响应
    - 实现请求的生命周期管理
    - 避免同时存在过多请求
 
 4. 用户体验优化 ✨
    - 渐进式加载提供即时反馈
    - 加载指示器显示加载状态
    - 滚动停止后自动提升图片质量
 */

class OptimizedPhotoCollectionViewController: UIViewController {
    // MARK: - Properties
    @IBOutlet weak var collectionView: UICollectionView!
    
    private let cacheManager = ImprovedCacheManager.shared
    private var assets: [PHAsset] = []
    private var thumbnailSize: CGSize = .zero
    
    // 滚动优化相关
    private var previousPreheatRect = CGRect.zero
    private let preheatRectMultiplier: CGFloat = 2.0  // 预加载视口2倍范围
    private var isScrolling = false
    private var scrollEndTimer: Timer?
    
    // MARK: - View Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupCollectionView()
        updateItemSize()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateCachedAssets()
    }
    
    // MARK: - Setup
    private func setupCollectionView() {
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.prefetchDataSource = self

        // 注册 Cell
        collectionView.register(PhotoCell.self, forCellWithReuseIdentifier: "PhotoCell")

        // 优化滚动性能
        collectionView.isPrefetchingEnabled = true
        collectionView.decelerationRate = .fast
    }
    
    private func updateItemSize() {
        let viewWidth = view.bounds.width
        let desiredItemsPerRow: CGFloat = 3
        let padding: CGFloat = 2
        
        let itemWidth = (viewWidth - (desiredItemsPerRow + 1) * padding) / desiredItemsPerRow
        let itemSize = CGSize(width: itemWidth, height: itemWidth)
        
        let scale = UIScreen.main.scale
        thumbnailSize = CGSize(width: itemWidth * scale, height: itemWidth * scale)
        
        if let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
            layout.itemSize = itemSize
            layout.minimumInteritemSpacing = padding
            layout.minimumLineSpacing = padding
        }
    }
}

// MARK: - UICollectionViewDataSource
/*
 🎯 Cell配置最佳实践：
 
 1. 防止图片错位
    - 保存asset标识符到cell
    - 图片加载完成时验证标识符
    - 不匹配则丢弃结果
 
 2. 性能优化要点
    - 复用前彻底清理cell状态
    - 使用异步加载避免阻塞主线程
    - 合理使用加载指示器
 
 3. 调试技巧
    - 给cell添加不同背景色查看复用情况
    - 打印日志追踪加载请求
    - 使用Time Profiler检查主线程阻塞
 */
extension OptimizedPhotoCollectionViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return assets.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "PhotoCell", for: indexPath) as! PhotoCell
        let asset = assets[indexPath.item]
        
        // 重置cell状态，避免复用问题
        cell.prepareForReuse()
        cell.representedAssetIdentifier = asset.localIdentifier
        
        // 使用改进的渐进式加载
        cacheManager.loadThumbnailProgressive(for: asset, targetSize: thumbnailSize) { [weak cell] result in
            DispatchQueue.main.async {
                // 验证cell没有被复用
                guard cell?.representedAssetIdentifier == asset.localIdentifier else { return }
                
                switch result {
                case .success(let image):
                    cell?.imageView.image = image
                    
                    // 如果是低质量图片，添加加载动画
                    if image.size.width < self.thumbnailSize.width * 0.8 {
                        cell?.showLoadingIndicator()
                    } else {
                        cell?.hideLoadingIndicator()
                    }
                    
                case .failure:
                    cell?.imageView.image = UIImage(systemName: "photo")
                }
            }
        }
        
        return cell
    }
}

// MARK: - UICollectionViewDataSourcePrefetching
/*
 📲 预加载机制详解：
 
 工作原理：
 - 系统预测用户滚动方向
 - 提前调用prefetchItemsAt
 - 在后台准备即将显示的内容
 
 优化建议：
 1. 预加载数量控制
    - 不要一次预加载太多（建议10-20个）
    - 根据滚动速度动态调整
    - 快速滚动时可以跳过预加载
 
 2. 取消机制
    - 及时取消不需要的预加载
    - 避免浪费系统资源
    - 使用请求ID跟踪每个请求
 
 3. 与缓存配合
    - 预加载的图片自动进入缓存
    - 显示时直接从缓存读取
    - 大幅提升滚动流畅度
 */
extension OptimizedPhotoCollectionViewController: UICollectionViewDataSourcePrefetching {
    func collectionView(_ collectionView: UICollectionView, prefetchItemsAt indexPaths: [IndexPath]) {
        let assets = indexPaths.map { self.assets[$0.item] }
        cacheManager.startCachingImages(for: assets, targetSize: thumbnailSize)
    }
    
    func collectionView(_ collectionView: UICollectionView, cancelPrefetchingForItemsAt indexPaths: [IndexPath]) {
        let assets = indexPaths.map { self.assets[$0.item] }
        cacheManager.stopCachingImages(for: assets, targetSize: thumbnailSize)
    }
}

// MARK: - Scroll View Delegate
/*
 🎢 滚动优化策略：
 
 核心思路：滚动时追求流畅，停止后追求质量
 
 1. 滚动中 (isScrolling = true)
    - 只加载低质量图片
    - 减少预加载范围
    - 推迟非必要的更新
 
 2. 滚动停止 (isScrolling = false)
    - 更新可见图片为高质量
    - 恢复正常预加载范围
    - 执行延迟的更新任务
 
 3. 性能监控
    - FPS保持60帧
    - 主线程CPU使用率 < 80%
    - 响应延迟 < 16ms
 
 实施要点：
 - 使用Timer延迟判断滚动结束，避免频繁切换
 - 区分拖拽结束和减速结束两种情况
 - 快速滚动时可以进一步降低图片质量
 */
extension OptimizedPhotoCollectionViewController: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        // 滚动时更新缓存
        updateCachedAssets()
    }
    
    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        isScrolling = true
        scrollEndTimer?.invalidate()
    }
    
    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if !decelerate {
            scrollingDidEnd()
        }
    }
    
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        scrollingDidEnd()
    }
    
    private func scrollingDidEnd() {
        // 延迟标记滚动结束，避免频繁切换
        scrollEndTimer?.invalidate()
        scrollEndTimer = Timer.scheduledTimer(withTimeInterval: 0.3, repeats: false) { [weak self] _ in
            self?.isScrolling = false
            self?.updateVisibleCellsQuality()
        }
    }
    
    // 滚动结束后更新可见cell的图片质量
    private func updateVisibleCellsQuality() {
        let visibleCells = collectionView.visibleCells.compactMap { $0 as? PhotoCell }
        let visibleAssets = collectionView.indexPathsForVisibleItems.map { assets[$0.item] }
        
        // 确保可见的图片都是高质量的
        for (index, cell) in visibleCells.enumerated() {
            guard index < visibleAssets.count else { continue }
            let asset = visibleAssets[index]
            
            // 重新请求高质量图片
            cacheManager.loadThumbnailProgressive(for: asset, targetSize: thumbnailSize) { [weak cell] result in
                DispatchQueue.main.async {
                    guard cell?.representedAssetIdentifier == asset.localIdentifier else { return }
                    
                    if case .success(let image) = result {
                        cell?.imageView.image = image
                        cell?.hideLoadingIndicator()
                    }
                }
            }
        }
    }
}

// MARK: - UICollectionViewDelegate
extension OptimizedPhotoCollectionViewController: UICollectionViewDelegate {
    // 可以在这里添加其他 delegate 方法
}

// MARK: - Asset Caching
/*
 🔄 预热区域算法说明：

 概念解释：
 - 可见区域：当前屏幕显示的内容
 - 预热区域：可见区域上下各扩展2倍高度
 - 更新阈值：滚动超过1/3屏幕高度才更新

 算法优化：
 1. 差分计算
    - 只处理新增和移除的区域
    - 避免重复计算相同区域
    - 大幅减少不必要的缓存操作

 2. 智能阈值
    - 滚动距离超过1/3屏幕才更新
    - 避免微小滚动触发频繁更新
    - 平衡响应性和性能

 3. 区域计算优化
    - 使用数学计算代替遍历
    - 批量处理索引范围
    - 减少单个资源的处理开销
 */
extension OptimizedPhotoCollectionViewController {
    private func updateCachedAssets() {
        guard viewIfLoaded?.window != nil else { return }

        // 计算预热区域
        let visibleRect = CGRect(origin: collectionView.contentOffset, size: collectionView.bounds.size)
        let preheatRect = visibleRect.insetBy(
            dx: 0,
            dy: -visibleRect.height * preheatRectMultiplier
        )

        // 只在预热区域变化显著时更新
        let delta = abs(preheatRect.midY - previousPreheatRect.midY)
        guard delta > collectionView.bounds.height / 3 else { return }

        // 计算需要预加载的资产
        let (addedRects, removedRects) = differencesBetweenRects(previousPreheatRect, preheatRect)

        let addedAssets = addedRects
            .flatMap { rect in indicesForElements(in: rect) }
            .map { assets[$0] }

        let removedAssets = removedRects
            .flatMap { rect in indicesForElements(in: rect) }
            .map { assets[$0] }

        // 更新缓存
        cacheManager.startCachingImages(for: addedAssets, targetSize: thumbnailSize)
        cacheManager.stopCachingImages(for: removedAssets, targetSize: thumbnailSize)

        // 更新可见资产
        let visibleAssets = collectionView.indexPathsForVisibleItems.map { assets[$0.item] }
        cacheManager.updateVisibleAssets(visible: visibleAssets, targetSize: thumbnailSize)

        previousPreheatRect = preheatRect
    }

    private func indicesForElements(in rect: CGRect) -> [Int] {
        guard let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout else {
            return []
        }

        let itemWidth = layout.itemSize.width + layout.minimumInteritemSpacing
        let itemHeight = layout.itemSize.height + layout.minimumLineSpacing
        let itemsPerRow = Int(collectionView.bounds.width / itemWidth)

        let minRow = Int(rect.minY / itemHeight)
        let maxRow = Int(rect.maxY / itemHeight)

        var indices: [Int] = []
        for row in minRow...maxRow {
            for col in 0..<itemsPerRow {
                let index = row * itemsPerRow + col
                if index < assets.count {
                    indices.append(index)
                }
            }
        }

        return indices
    }

    private func differencesBetweenRects(_ old: CGRect, _ new: CGRect) -> (added: [CGRect], removed: [CGRect]) {
        if old.intersects(new) {
            var added = [CGRect]()
            if new.maxY > old.maxY {
                added.append(CGRect(x: new.origin.x, y: old.maxY, width: new.width, height: new.maxY - old.maxY))
            }
            if old.minY > new.minY {
                added.append(CGRect(x: new.origin.x, y: new.minY, width: new.width, height: old.minY - new.minY))
            }

            var removed = [CGRect]()
            if new.maxY < old.maxY {
                removed.append(CGRect(x: new.origin.x, y: new.maxY, width: new.width, height: old.maxY - new.maxY))
            }
            if old.minY < new.minY {
                removed.append(CGRect(x: new.origin.x, y: old.minY, width: new.width, height: new.minY - old.minY))
            }

            return (added, removed)
        } else {
            return ([new], [old])
        }
    }
}

// MARK: - PhotoCell
class PhotoCell: UICollectionViewCell {
    let imageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = .systemGray6
        imageView.translatesAutoresizingMaskIntoConstraints = false
        return imageView
    }()

    private var loadingIndicator: UIActivityIndicatorView?

    var representedAssetIdentifier: String?

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        contentView.addSubview(imageView)

        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor)
        ])
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        imageView.image = nil
        representedAssetIdentifier = nil
        hideLoadingIndicator()
    }

    func showLoadingIndicator() {
        if loadingIndicator == nil {
            loadingIndicator = UIActivityIndicatorView(style: .medium)
            loadingIndicator?.translatesAutoresizingMaskIntoConstraints = false
            contentView.addSubview(loadingIndicator!)

            NSLayoutConstraint.activate([
                loadingIndicator!.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
                loadingIndicator!.centerYAnchor.constraint(equalTo: contentView.centerYAnchor)
            ])
        }

        loadingIndicator?.startAnimating()
    }

    func hideLoadingIndicator() {
        loadingIndicator?.stopAnimating()
    }
}

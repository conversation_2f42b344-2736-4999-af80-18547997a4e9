//
//  PhotoDetailViewController.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/6/14.
//

import UIKit
import Photos
import AVFoundation
import AVKit

/// 照片详情预览控制器 - 支持图片和视频全屏预览
class PhotoDetailViewController: UIViewController {
    
    // MARK: - 属性
    
    /// 当前显示的照片模型
    private var currentPhoto: PhotoModel
    
    /// 所有照片数据源
    private var photos: [PhotoModel]
    
    /// 当前照片索引
    private var currentIndex: Int
    
    /// 滚动视图容器
    private let scrollView = UIScrollView()
    
    /// 图片视图
    private let imageView = UIImageView()
    
    /// 视频播放器容器
    private var playerViewController: AVPlayerViewController?
    
    /// 视频播放器
    private var player: AVPlayer?
    
    /// 加载指示器
    private let loadingIndicator = UIActivityIndicatorView(style: .large)
    
    /// 导航栏容器
    private let navigationBar = UIView()
    
    /// 工具栏容器
    private let toolbar = UIView()
    
    /// 收藏按钮引用（用于更新状态）
    private var favoriteButton: UIButton?
    
    /// 标题标签引用（用于更新标题）
    private var titleLabel: UILabel?
    
    /// 顶部渐变层
    private var topGradientLayer: CAGradientLayer?
    
    /// 底部渐变层
    private var bottomGradientLayer: CAGradientLayer?
    
    /// 是否显示UI控件
    private var isUIVisible = true
    
    /// 单击手势
    private var singleTapGesture: UITapGestureRecognizer!
    
    /// 双击手势
    private var doubleTapGesture: UITapGestureRecognizer!
    
    /// 拖拽手势
    private var panGesture: UIPanGestureRecognizer!
    
    /// 初始frame（用于转场动画）
    private var initialFrame: CGRect?
    
    // MARK: - 初始化
    
    /// 初始化照片详情控制器
    /// - Parameters:
    ///   - photos: 照片数组
    ///   - currentIndex: 当前显示的照片索引
    ///   - initialFrame: 初始frame（用于转场动画）
    init(photos: [PhotoModel], currentIndex: Int, initialFrame: CGRect? = nil) {
        self.photos = photos
        self.currentIndex = currentIndex
        self.currentPhoto = photos[currentIndex]
        self.initialFrame = initialFrame
        
        super.init(nibName: nil, bundle: nil)
        
        // 设置模态展示样式
        modalPresentationStyle = .fullScreen
        modalTransitionStyle = .crossDissolve
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        setupGestures()
        setupConstraints()
        loadCurrentPhoto()
        
        print("📸 照片详情页加载完成，当前索引: \(currentIndex)")
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // 隐藏状态栏
        setNeedsStatusBarAppearanceUpdate()
    }
    
    override var prefersStatusBarHidden: Bool {
        return true
    }
    
    override var preferredStatusBarUpdateAnimation: UIStatusBarAnimation {
        return .fade
    }
    
    // MARK: - UI设置
    
    /// 设置用户界面
    private func setupUI() {
        view.backgroundColor = .black
        
        // 配置滚动视图
        scrollView.delegate = self
        scrollView.minimumZoomScale = 1.0
        scrollView.maximumZoomScale = 3.0
        scrollView.showsVerticalScrollIndicator = false
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.contentInsetAdjustmentBehavior = .never
        
        // 配置图片视图
        imageView.contentMode = .scaleAspectFit
        imageView.isUserInteractionEnabled = true
        
        // 配置加载指示器
        loadingIndicator.color = .white
        loadingIndicator.hidesWhenStopped = true
        
        // 添加子视图
        view.addSubview(scrollView)
        scrollView.addSubview(imageView)
        view.addSubview(loadingIndicator)
        
        // 配置导航栏和工具栏
        setupNavigationBar()
        setupToolbar()
    }
    
    /// 设置导航栏
    private func setupNavigationBar() {
        // 创建导航栏容器
        navigationBar.backgroundColor = .clear
        navigationBar.frame = CGRect(x: 0, y: 0, width: view.bounds.width, height: 100)
        view.addSubview(navigationBar)
        
        // 添加顶部渐变背景 - 原生Photos效果
        let topGradient = CAGradientLayer()
        topGradient.frame = navigationBar.bounds
        topGradient.colors = [
            UIColor.black.withAlphaComponent(0.6).cgColor,
            UIColor.black.withAlphaComponent(0.3).cgColor,
            UIColor.clear.cgColor
        ]
        topGradient.locations = [0.0, 0.7, 1.0]
        navigationBar.layer.insertSublayer(topGradient, at: 0)
        self.topGradientLayer = topGradient
        
        // 创建关闭按钮 - 完全按照iOS Photos样式
        let closeButton = UIButton(type: .system)
        let closeConfig = UIImage.SymbolConfiguration(pointSize: 18, weight: .medium)
        closeButton.setImage(UIImage(systemName: "xmark", withConfiguration: closeConfig), for: .normal)
        closeButton.tintColor = .white
        closeButton.frame = CGRect(x: 16, y: 50, width: 44, height: 44)
        closeButton.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        
        // 创建标题标签 - 原生Photos样式
        let titleLbl = UILabel()
        titleLbl.text = "\(currentIndex + 1) / \(photos.count)"
        titleLbl.textColor = .white
        titleLbl.font = UIFont.systemFont(ofSize: 17, weight: .semibold)
        titleLbl.textAlignment = .center
        titleLbl.frame = CGRect(x: 80, y: 50, width: view.bounds.width - 160, height: 44)
        
        navigationBar.addSubview(closeButton)
        navigationBar.addSubview(titleLbl)
        
        // 保存引用
        self.titleLabel = titleLbl
    }
    
    /// 设置工具栏
    private func setupToolbar() {
        // 创建工具栏容器 - 原生Photos样式
        toolbar.backgroundColor = .clear
        toolbar.frame = CGRect(x: 0, y: view.bounds.height - 100, width: view.bounds.width, height: 100)
        view.addSubview(toolbar)
        
        // 添加底部渐变背景 - 原生Photos效果
        let bottomGradient = CAGradientLayer()
        bottomGradient.frame = toolbar.bounds
        bottomGradient.colors = [
            UIColor.clear.cgColor,
            UIColor.black.withAlphaComponent(0.3).cgColor,
            UIColor.black.withAlphaComponent(0.6).cgColor
        ]
        bottomGradient.locations = [0.0, 0.3, 1.0]
        toolbar.layer.insertSublayer(bottomGradient, at: 0)
        self.bottomGradientLayer = bottomGradient
        
        // 按钮间距计算 - 完全按照原生Photos布局
        let buttonWidth: CGFloat = 44
        let totalButtons = 4
        let totalButtonWidth = CGFloat(totalButtons) * buttonWidth
        let availableSpace = view.bounds.width - 32 // 左右各16的边距
        let spacing = (availableSpace - totalButtonWidth) / CGFloat(totalButtons - 1)
        
        var currentX: CGFloat = 16
        
        // 分享按钮
        let shareBtn = createToolbarButton(image: "square.and.arrow.up", action: #selector(shareButtonTapped))
        shareBtn.frame = CGRect(x: currentX, y: 28, width: buttonWidth, height: buttonWidth)
        currentX += buttonWidth + spacing
        
        // 收藏按钮
        let favoriteBtn = createToolbarButton(image: "heart", action: #selector(favoriteButtonTapped))
        favoriteBtn.frame = CGRect(x: currentX, y: 28, width: buttonWidth, height: buttonWidth)
        self.favoriteButton = favoriteBtn
        currentX += buttonWidth + spacing
        
        // 信息按钮
        let infoBtn = createToolbarButton(image: "info.circle", action: #selector(infoButtonTapped))
        infoBtn.frame = CGRect(x: currentX, y: 28, width: buttonWidth, height: buttonWidth)
        currentX += buttonWidth + spacing
        
        // 删除按钮
        let deleteBtn = createToolbarButton(image: "trash", action: #selector(deleteButtonTapped))
        deleteBtn.frame = CGRect(x: currentX, y: 28, width: buttonWidth, height: buttonWidth)
        
        toolbar.addSubview(shareBtn)
        toolbar.addSubview(favoriteBtn)
        toolbar.addSubview(infoBtn)
        toolbar.addSubview(deleteBtn)
        
        updateFavoriteButton()
    }
    
    /// 创建工具栏按钮 - 原生Photos样式
    private func createToolbarButton(image: String, action: Selector) -> UIButton {
        let button = UIButton(type: .system)
        let config = UIImage.SymbolConfiguration(pointSize: 22, weight: .medium)
        button.setImage(UIImage(systemName: image, withConfiguration: config), for: .normal)
        button.tintColor = .white
        button.addTarget(self, action: action, for: .touchUpInside)
        return button
    }
    
    /// 设置手势识别
    private func setupGestures() {
        // 单击手势 - 切换UI显示/隐藏
        singleTapGesture = UITapGestureRecognizer(target: self, action: #selector(handleSingleTap(_:)))
        singleTapGesture.numberOfTapsRequired = 1
        view.addGestureRecognizer(singleTapGesture)
        
        // 双击手势 - 缩放
        doubleTapGesture = UITapGestureRecognizer(target: self, action: #selector(handleDoubleTap(_:)))
        doubleTapGesture.numberOfTapsRequired = 2
        view.addGestureRecognizer(doubleTapGesture)
        
        // 拖拽手势 - 关闭预览
        panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        view.addGestureRecognizer(panGesture)
        
        // 设置手势优先级
        singleTapGesture.require(toFail: doubleTapGesture)
        
        // 左右滑动手势 - 切换照片
        let swipeLeft = UISwipeGestureRecognizer(target: self, action: #selector(swipeLeft))
        swipeLeft.direction = .left
        view.addGestureRecognizer(swipeLeft)
        
        let swipeRight = UISwipeGestureRecognizer(target: self, action: #selector(swipeRight))
        swipeRight.direction = .right
        view.addGestureRecognizer(swipeRight)
    }
    
    /// 设置约束
    private func setupConstraints() {
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        imageView.translatesAutoresizingMaskIntoConstraints = false
        loadingIndicator.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // 滚动视图 - 全屏显示
            scrollView.topAnchor.constraint(equalTo: view.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            // 图片视图 - 填充滚动视图
            imageView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            imageView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            imageView.heightAnchor.constraint(equalTo: scrollView.heightAnchor),
            
            // 加载指示器 - 居中显示
            loadingIndicator.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            loadingIndicator.centerYAnchor.constraint(equalTo: view.centerYAnchor)
        ])
    }
    
    // MARK: - 照片加载
    
    /// 加载当前照片
    private func loadCurrentPhoto() {
        currentPhoto = photos[currentIndex]
        
        // 更新标题
        titleLabel?.text = "\(currentIndex + 1) / \(photos.count)"
        
        // 重置缩放
        scrollView.zoomScale = 1.0
        
        // 清理之前的内容
        cleanupPreviousContent()
        
        if currentPhoto.mediaType == .video {
            loadVideo()
        } else {
            loadImage()
        }
        
        updateFavoriteButton()
        
        print("📸 加载照片: \(currentPhoto.id), 类型: \(currentPhoto.mediaType)")
    }
    
    /// 清理之前的内容
    private func cleanupPreviousContent() {
        // 停止视频播放
        player?.pause()
        player = nil
        
        // 移除视频播放器
        playerViewController?.view.removeFromSuperview()
        playerViewController?.removeFromParent()
        playerViewController = nil
        
        // 清空图片
        imageView.image = nil
        imageView.isHidden = false
    }
    
    /// 加载图片
    private func loadImage() {
        loadingIndicator.startAnimating()

        let targetSize = CGSize(
            width: view.bounds.width * UIScreen.main.scale,
            height: view.bounds.height * UIScreen.main.scale
        )

        PhotoLibraryService.shared.fetchThumbnail(for: currentPhoto, size: targetSize) { [weak self] result in
            DispatchQueue.main.async {
                self?.loadingIndicator.stopAnimating()

                switch result {
                case .success(let imageData):
                    if let image = imageData as? UIImage {
                        self?.imageView.image = image
                        self?.updateImageViewFrame()
                    }
                case .failure(let error):
                    print("❌ 加载图片失败: \(error)")
                    // TODO: 显示错误状态
                }
            }
        }
    }


    
    /// 加载视频
    private func loadVideo() {
        loadingIndicator.startAnimating()
        imageView.isHidden = true
        
        let options = PHVideoRequestOptions()
        options.isNetworkAccessAllowed = true
        options.deliveryMode = .highQualityFormat
        
        PHImageManager.default().requestAVAsset(forVideo: currentPhoto.asset, options: options) { [weak self] asset, _, _ in
            DispatchQueue.main.async {
                self?.loadingIndicator.stopAnimating()
                
                guard let asset = asset else {
                    print("❌ 加载视频失败")
                    return
                }
                
                self?.setupVideoPlayer(with: asset)
            }
        }
    }
    
    /// 设置视频播放器
    private func setupVideoPlayer(with asset: AVAsset) {
        player = AVPlayer(playerItem: AVPlayerItem(asset: asset))
        playerViewController = AVPlayerViewController()
        playerViewController?.player = player
        playerViewController?.showsPlaybackControls = true
        playerViewController?.videoGravity = .resizeAspect
        
        guard let playerVC = playerViewController else { return }
        
        addChild(playerVC)
        view.insertSubview(playerVC.view, belowSubview: navigationBar)
        playerVC.didMove(toParent: self)
        
        // 设置播放器约束
        playerVC.view.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            playerVC.view.topAnchor.constraint(equalTo: view.topAnchor),
            playerVC.view.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            playerVC.view.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            playerVC.view.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
        
        print("🎬 视频播放器设置完成")
    }
    
    /// 更新图片视图frame
    private func updateImageViewFrame() {
        guard let image = imageView.image else { return }
        
        let imageSize = image.size
        let viewSize = scrollView.bounds.size
        
        let scaleX = viewSize.width / imageSize.width
        let scaleY = viewSize.height / imageSize.height
        let scale = min(scaleX, scaleY)
        
        let scaledSize = CGSize(
            width: imageSize.width * scale,
            height: imageSize.height * scale
        )
        
        imageView.frame = CGRect(
            x: (viewSize.width - scaledSize.width) / 2,
            y: (viewSize.height - scaledSize.height) / 2,
            width: scaledSize.width,
            height: scaledSize.height
        )
        
        scrollView.contentSize = scaledSize
    }
    
    // MARK: - 手势处理
    
    /// 处理单击手势 - 切换UI显示/隐藏
    @objc private func handleSingleTap(_ gesture: UITapGestureRecognizer) {
        toggleUIVisibility()
    }

    /// 处理双击手势 - 缩放功能
    @objc private func handleDoubleTap(_ gesture: UITapGestureRecognizer) {
        if scrollView.zoomScale == scrollView.minimumZoomScale {
            // 放大到双击位置
            let location = gesture.location(in: imageView)
            let zoomRect = zoomRectForScale(scrollView.maximumZoomScale, center: location)
            scrollView.zoom(to: zoomRect, animated: true)
        } else {
            // 缩小到最小尺寸
            scrollView.setZoomScale(scrollView.minimumZoomScale, animated: true)
        }
    }
    
    /// 处理拖拽手势 - 关闭预览
    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: view)
        let velocity = gesture.velocity(in: view)
        
        switch gesture.state {
        case .began:
            // 开始拖拽
            break
        case .changed:
            // 拖拽中 - 只有在垂直方向拖拽时才响应
            if abs(translation.y) > abs(translation.x) {
                let progress = abs(translation.y) / view.bounds.height
                let alpha = 1.0 - min(progress * 2, 1.0)
                view.backgroundColor = UIColor.black.withAlphaComponent(alpha)
                
                // 移动视图
                view.transform = CGAffineTransform(translationX: 0, y: translation.y)
            }
        case .ended, .cancelled:
            // 拖拽结束
            let shouldDismiss = abs(translation.y) > 100 || abs(velocity.y) > 500
            
            if shouldDismiss {
                dismissWithAnimation()
            } else {
                // 恢复原状
                UIView.animate(withDuration: 0.3) {
                    self.view.backgroundColor = .black
                    self.view.transform = .identity
                }
            }
        default:
            break
        }
    }
    
    /// 左滑手势 - 下一张照片
    @objc private func swipeLeft() {
        showNextPhoto()
    }
    
    /// 右滑手势 - 上一张照片
    @objc private func swipeRight() {
        showPreviousPhoto()
    }
    
    // MARK: - 按钮事件
    
    /// 关闭按钮点击
    @objc private func closeButtonTapped() {
        dismissWithAnimation()
    }
    
    /// 分享按钮点击
    @objc private func shareButtonTapped() {
        shareCurrentPhoto()
    }
    
    /// 收藏按钮点击
    @objc private func favoriteButtonTapped() {
        toggleFavorite()
    }
    
    /// 删除按钮点击
    @objc private func deleteButtonTapped() {
        showDeleteConfirmation()
    }
    
    /// 信息按钮点击
    @objc private func infoButtonTapped() {
        showPhotoInfo()
    }
    
    // MARK: - 功能实现
    
    /// 切换UI显示/隐藏
    private func toggleUIVisibility() {
        isUIVisible.toggle()
        
        UIView.animate(withDuration: 0.3) {
            self.navigationBar.alpha = self.isUIVisible ? 1.0 : 0.0
            self.toolbar.alpha = self.isUIVisible ? 1.0 : 0.0
        }
    }
    
    /// 显示下一张照片
    private func showNextPhoto() {
        guard currentIndex < photos.count - 1 else { return }
        currentIndex += 1
        loadCurrentPhoto()
    }
    
    /// 显示上一张照片
    private func showPreviousPhoto() {
        guard currentIndex > 0 else { return }
        currentIndex -= 1
        loadCurrentPhoto()
    }
    
    /// 分享当前照片
    private func shareCurrentPhoto() {
        let activityVC = UIActivityViewController(activityItems: [currentPhoto.asset], applicationActivities: nil)
        // 在iPad上设置弹出位置
        if let popover = activityVC.popoverPresentationController {
            popover.sourceView = view
            popover.sourceRect = CGRect(x: view.bounds.midX, y: view.bounds.midY, width: 0, height: 0)
            popover.permittedArrowDirections = []
        }
        present(activityVC, animated: true)
    }

    /// 分析照片压缩比例（调试功能）
    private func analyzePhotoCompressionRatio() {
        guard let displayedImage = imageView.image else {
            print("🔍 压缩分析：无法获取显示的图片")
            return
        }

        let originalSize = currentPhoto.pixelSize
        let displayedSize = displayedImage.size
        let targetSize = CGSize(
            width: view.bounds.width * UIScreen.main.scale,
            height: view.bounds.height * UIScreen.main.scale
        )

        // 计算压缩比例
        let originalAspectRatio = originalSize.width / originalSize.height
        let displayedAspectRatio = displayedSize.width / displayedSize.height

        // 判断是否被压缩变形
        let aspectRatioDifference = abs(originalAspectRatio - displayedAspectRatio)
        let isCompressed = aspectRatioDifference > 0.01 // 1%的容差

        // 计算应该的裁剪尺寸（aspectFill模式）
        let targetAspectRatio = targetSize.width / targetSize.height
        var expectedSize: CGSize

        if originalAspectRatio > targetAspectRatio {
            // 原图更宽，应该裁剪宽度
            expectedSize = CGSize(
                width: originalSize.height * targetAspectRatio,
                height: originalSize.height
            )
        } else {
            // 原图更高，应该裁剪高度
            expectedSize = CGSize(
                width: originalSize.width,
                height: originalSize.width / targetAspectRatio
            )
        }

        print("""
        🔍 照片压缩分析报告
        ==================
        照片ID: \(currentPhoto.id)
        原始尺寸: \(Int(originalSize.width))x\(Int(originalSize.height))
        显示尺寸: \(Int(displayedSize.width))x\(Int(displayedSize.height))
        目标尺寸: \(Int(targetSize.width))x\(Int(targetSize.height))
        期望尺寸: \(Int(expectedSize.width))x\(Int(expectedSize.height))

        宽高比分析:
        - 原始宽高比: \(String(format: "%.3f", originalAspectRatio))
        - 显示宽高比: \(String(format: "%.3f", displayedAspectRatio))
        - 目标宽高比: \(String(format: "%.3f", targetAspectRatio))
        - 差异: \(String(format: "%.3f", aspectRatioDifference))

        压缩状态: \(isCompressed ? "❌ 被压缩变形" : "✅ 正常裁剪")

        可能原因:
        \(isCompressed ? "- PHImageManager使用了.fast模式导致直接缩放\n- 图片格式特殊(HEIC/Live Photo)处理异常\n- 缓存层级处理不一致" : "- 正常的aspectFill裁剪\n- PHImageManager正确处理了宽高比")
        ==================
        """)
    }
    
    /// 切换收藏状态
    private func toggleFavorite() {
        // TODO: 实现收藏功能
        currentPhoto.isFavorite.toggle()
        updateFavoriteButton()
        
        print("❤️ 收藏状态切换: \(currentPhoto.isFavorite)")
    }
    
    /// 更新收藏按钮状态
    private func updateFavoriteButton() {
        let imageName = currentPhoto.isFavorite ? "heart.fill" : "heart"
        favoriteButton?.setImage(UIImage(systemName: imageName), for: .normal)
        favoriteButton?.tintColor = currentPhoto.isFavorite ? .systemRed : .white
    }
    
    /// 显示删除确认对话框
    private func showDeleteConfirmation() {
        let alert = UIAlertController(
            title: "删除照片",
            message: "确定要删除这张照片吗？此操作无法撤销。",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "删除", style: .destructive) { _ in
            self.deleteCurrentPhoto()
        })
        
        present(alert, animated: true)
    }
    
    /// 删除当前照片
    private func deleteCurrentPhoto() {
        // TODO: 实现删除功能
        print("🗑️ 删除照片: \(currentPhoto.id)")
        
        // 从数组中移除
        photos.remove(at: currentIndex)
        
        if photos.isEmpty {
            // 没有照片了，关闭预览
            dismissWithAnimation()
        } else {
            // 调整索引并加载新照片
            if currentIndex >= photos.count {
                currentIndex = photos.count - 1
            }
            loadCurrentPhoto()
        }
    }
    
    /// 显示照片信息
    private func showPhotoInfo() {
        let alert = UIAlertController(title: "照片信息", message: nil, preferredStyle: .alert)
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .short
        
        var message = ""
        message += "创建时间: \(dateFormatter.string(from: currentPhoto.creationDate ?? Date()))\n"
        message += "媒体类型: \(currentPhoto.mediaType == .video ? "视频" : "图片")\n"
        message += "文件ID: \(currentPhoto.id)\n"
        
        alert.message = message
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        
        present(alert, animated: true)
    }
    
    /// 带动画关闭预览
    private func dismissWithAnimation() {
        UIView.animate(withDuration: 0.3, animations: {
            self.view.alpha = 0
            self.view.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        }) { _ in
            self.dismiss(animated: false)
        }
    }
    
    /// 计算缩放矩形
    private func zoomRectForScale(_ scale: CGFloat, center: CGPoint) -> CGRect {
        let size = CGSize(
            width: scrollView.bounds.width / scale,
            height: scrollView.bounds.height / scale
        )
        
        let origin = CGPoint(
            x: center.x - size.width / 2,
            y: center.y - size.height / 2
        )
        
        return CGRect(origin: origin, size: size)
    }
}

// MARK: - UIScrollViewDelegate

extension PhotoDetailViewController: UIScrollViewDelegate {
    
    func viewForZooming(in scrollView: UIScrollView) -> UIView? {
        return imageView
    }
    
    func scrollViewDidZoom(_ scrollView: UIScrollView) {
        // 缩放时保持图片居中
        let offsetX = max((scrollView.bounds.width - scrollView.contentSize.width) * 0.5, 0)
        let offsetY = max((scrollView.bounds.height - scrollView.contentSize.height) * 0.5, 0)
        
        imageView.center = CGPoint(
            x: scrollView.contentSize.width * 0.5 + offsetX,
            y: scrollView.contentSize.height * 0.5 + offsetY
        )
    }
} 
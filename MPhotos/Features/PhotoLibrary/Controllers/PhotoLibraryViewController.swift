//
//  PhotoLibraryViewController.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import UIKit
import Photos

/// 图库主视图控制器 - 负责展示照片网格和处理用户交互
class PhotoLibraryViewController: BaseViewController {

    // MARK: - 依赖
    private let mediaManager: MediaManaging = MediaManager()
    private let photoLibraryService = SimplifiedPhotoLibraryService.shared

    // MARK: - UI组件
    
    /// 照片集合视图
    private var collectionView: UICollectionView!
    
    /// 集合视图布局
    private var collectionViewLayout: UICollectionViewFlowLayout!
    
    /// 设置按钮
    private var settingsButton: UIBarButtonItem!
    
    /// 选择按钮
    private var selectButton: UIBarButtonItem!
    
    /// 搜索按钮
    private var searchButton: UIBarButtonItem!
    
    // MARK: - 属性
    
    /// 视图模型
    private var viewModel: PhotoLibraryViewModel!
    
    /// 照片数组
    private var photos: [PhotoModel] = []
    
    /// 缩略图尺寸
    private var thumbnailSize: CGSize = CGSize(width: 200, height: 200)
    
    /// 是否正在选择模式
    private var isSelectionMode: Bool = false
    
    /// 用户是否正在滚动
    private var isUserScrolling: Bool = false
    
    /// 是否正在更新UI（防重入）
    private var isUpdatingUI: Bool = false
    
    /// 是否是首次加载（用于判断是否需要位置保持）
    private var isFirstLoad: Bool = true
    
    /// 是否已经进行过用户交互（滚动等）
    private var hasUserInteracted: Bool = false

    /// 应用是否已完全加载完成（用于防止初期手势被打断）
    private var isAppFullyLoaded: Bool = false

    /// 是否需要在数据加载完成后滚动到合适位置（用于排序变更）
    private var needsScrollToAppropriatePosition: Bool = false

    /// 滚动停止检测定时器
    private var scrollStopTimer: Timer?

    /// 手势布局管理器
    private var gestureLayoutManager: GestureLayoutManager!
    
    /// 布局过渡管理器
    private var layoutTransition: LayoutTransition!
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupViewModel()
        requestPhotoLibraryPermission()
        setupMemoryWarningObserver()

        #if DEBUG
        // 启动缓存监控
        CacheMonitor.shared.startMonitoring()
        #endif
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // 启动性能监控
        PerformanceMonitor.shared.startFPSMonitoring()
    }

    /// 设置内存警告监听
    private func setupMemoryWarningObserver() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }

    /// 处理内存警告
    @objc private func handleMemoryWarning() {
        print("⚠️ 收到内存警告，清理缓存")
        photoLibraryService.clearMemoryCache()

        // 触发垃圾回收
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.collectionView.reloadData()
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateThumbnailSize()
    }
    
    // MARK: - 设置方法
    
    override func setupUI() {
        super.setupUI()
        setupNavigationBar()
        setupCollectionView()
        setupConstraints()
    }
    
    override func bindViewModel() {
        super.bindViewModel()
        // ViewModel绑定会在setupViewModel中处理
    }
    
    /// 设置视图模型
    private func setupViewModel() {
        viewModel = PhotoLibraryViewModel()
        
        // 绑定数据变化
        viewModel.onPhotosUpdated = { [weak self] photos in
            DispatchQueue.main.async {
                self?.updatePhotosWithAnimation(newPhotos: photos)
                self?.hideLoadingIndicator()
                // print("📸 照片数据更新完成，共 \(photos.count) 张照片")
            }
        }
        
        // 绑定错误处理
        viewModel.onError = { [weak self] error in
            DispatchQueue.main.async {
                self?.hideLoadingIndicator()
                self?.showErrorAlert(message: error.localizedDescription)
                print("❌ 照片加载出错: \(error.localizedDescription)")
            }
        }
        
        // 绑定设置变更
        viewModel.onSettingsChanged = { [weak self] settings in
            DispatchQueue.main.async {
                self?.handleSettingsChange(settings)
            }
        }
    }
    
    /// 设置导航栏
    override func setupNavigationBar() {
        super.setupNavigationBar()
        
        title = "图库"
        navigationController?.navigationBar.prefersLargeTitles = true
        
        // 创建导航栏按钮
        searchButton = UIBarButtonItem(
            image: UIImage(systemName: "magnifyingglass"),
            style: .plain,
            target: self,
            action: #selector(searchButtonTapped)
        )
        
        settingsButton = UIBarButtonItem(
            image: UIImage(systemName: "gearshape"),
            style: .plain,
            target: self,
            action: #selector(settingsButtonTapped)
        )
        
        selectButton = UIBarButtonItem(
            title: "选择",
            style: .plain,
            target: self,
            action: #selector(selectButtonTapped)
        )
        
        // 设置导航栏按钮
        navigationItem.rightBarButtonItems = [settingsButton, searchButton]
        navigationItem.leftBarButtonItem = selectButton
        
        print("✅ 导航栏设置完成")
    }
    
    /// 设置集合视图
    private func setupCollectionView() {
        // 创建布局
        collectionViewLayout = UICollectionViewFlowLayout()
        collectionViewLayout.minimumInteritemSpacing = 2
        collectionViewLayout.minimumLineSpacing = 2
        collectionViewLayout.sectionInset = UIEdgeInsets(top: 2, left: 2, bottom: 2, right: 2)
        
        // 创建集合视图
        collectionView = UICollectionView(frame: .zero, collectionViewLayout: collectionViewLayout)
        collectionView.backgroundColor = .systemBackground
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.prefetchDataSource = self
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        
        // 注册Cell
        collectionView.register(PhotoGridCell.self, forCellWithReuseIdentifier: PhotoGridCell.identifier)
        
        // 注册Section Header（用于日期分组）
        collectionView.register(DateSectionHeaderView.self, 
                              forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader, 
                              withReuseIdentifier: DateSectionHeaderView.identifier)
        
        view.addSubview(collectionView)
        
        // 初始化手势布局管理器
        gestureLayoutManager = GestureLayoutManager(collectionView: collectionView)
        gestureLayoutManager.delegate = self
        
        // 初始化布局过渡管理器
        layoutTransition = LayoutTransition()
        layoutTransition.delegate = self

        // 🎯 渐进式加载：不再需要视口质量管理器

        print("✅ 集合视图设置完成")
        print("✅ 手势布局管理器初始化完成")
        print("✅ 视口质量管理器初始化完成")
        print("✅ 布局过渡管理器初始化完成")
        
        // 🔧 添加长按手势查看日志
        let longPressGesture = UILongPressGestureRecognizer(target: self, action: #selector(handleLongPress(_:)))
        longPressGesture.minimumPressDuration = 2.0
        longPressGesture.numberOfTouchesRequired = 1
        collectionView.addGestureRecognizer(longPressGesture)
    }
    
    /// 设置约束
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    // MARK: - 权限处理
    
    /// 请求照片库访问权限
    private func requestPhotoLibraryPermission() {
        let status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        
        print("📷 当前照片权限状态: \(status.rawValue)")
        
        switch status {
        case .authorized, .limited:
            // 已授权，立即加载照片
            print("✅ 照片权限已授权，开始加载照片")
            loadPhotos()
            
        case .denied, .restricted:
            // 被拒绝，显示设置提示
            print("❌ 照片权限被拒绝")
            showPermissionDeniedAlert()
            
        case .notDetermined:
            // 未确定，请求权限
            print("🔄 请求照片权限...")
            PHPhotoLibrary.requestAuthorization(for: .readWrite) { [weak self] newStatus in
                print("📷 权限请求结果: \(newStatus.rawValue)")
                DispatchQueue.main.async {
                    if newStatus == .authorized || newStatus == .limited {
                        print("✅ 权限授权成功，开始加载照片")
                        self?.loadPhotos()
                    } else {
                        print("❌ 权限授权失败")
                        self?.showPermissionDeniedAlert()
                    }
                }
            }
            
        @unknown default:
            print("⚠️ 未知权限状态")
            showPermissionDeniedAlert()
        }
    }
    
    /// 显示权限被拒绝的提示
    private func showPermissionDeniedAlert() {
        let alert = UIAlertController(
            title: "需要照片访问权限",
            message: "请在设置中允许MPhotos访问您的照片库，以便显示和管理您的照片。",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "去设置", style: .default) { _ in
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsUrl)
            }
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(alert, animated: true)
    }
    
    // MARK: - 数据加载
    
    /// 加载照片数据
    private func loadPhotos() {
        showLoadingIndicator()
        
        // 设置照片更新回调
        viewModel.onPhotosUpdated = { [weak self] photos in
            guard let self = self else { return }
            
            // 🔧 修复：线程安全检查和防护
            DispatchQueue.main.async { [weak self] in
                // 安全检查：确保self仍然有效
                guard let self = self else { 
                    return 
                }
                
                // 检查photos是否为空
                guard !photos.isEmpty else {
                    self.hideLoadingIndicator()
                    return
                }
                
                // 线程安全地检查是否首次加载
                let wasEmpty = self.photos.count == 0
                
                // 更新照片数据
                self.updatePhotosWithAnimation(newPhotos: photos)
                self.hideLoadingIndicator()

                // 🎯 方案3：智能初始位置 - 在UI更新后设置正确的contentOffset
                if wasEmpty || self.needsScrollToAppropriatePosition {
                    // 在数据更新后设置正确的初始位置
                    DispatchQueue.main.async {
                        self.setInitialContentOffsetAfterReload()

                        if wasEmpty {
                            print("📍 首次加载，使用智能初始位置")
                        } else {
                            print("📍 排序变更，使用智能初始位置")
                        }
                    }

                    // 重置标志
                    self.needsScrollToAppropriatePosition = false
                }

                // 只在首次加载时启用手势和调试
                if wasEmpty {
                    // 🔧 立即启用手势识别器，确保启动后就能使用缩放动画
                    self.gestureLayoutManager.enableGesture()
                    self.isAppFullyLoaded = true
                    print("✅ 手势识别器已立即启用，缩放动画功能就绪")
                    print("✅ 应用完全加载完成，手势功能已就绪")

                    // 🧪 调试：打印缩放映射配置和状态
                    self.gestureLayoutManager.printScaleMappingConfiguration()
                    self.gestureLayoutManager.testScaleMappingLogic()
                    self.gestureLayoutManager.checkGestureRecognizerStatus()

                    // 🚀 渐进式加载：无需预生成缓存
                }
                
                // print("✅ 照片UI更新完成，共 \(photos.count) 张照片")
            }
        }
        
        viewModel.loadPhotos()
        // print("🔄 开始加载照片数据...")
    }
    
    /// 使用动画更新照片数据（避免闪烁）
    private func updatePhotosWithAnimation(newPhotos: [PhotoModel]) {
        // 🔧 关键修复：手势期间完全禁止UI更新，避免重置布局
        if viewModel.isGestureInProgress {
            print("🤏 手势进行中，完全禁止照片UI更新，避免干扰布局变更")
            return
        }
        
        // 防重入检查
        if isUpdatingUI {
            print("⚠️ UI正在更新中，跳过本次更新")
            return
        }
        
        let oldCount = photos.count
        let newCount = newPhotos.count
        
        // 如果是首次加载或者照片数量减少，直接重新加载
        if oldCount == 0 || newCount < oldCount {
            isUpdatingUI = true
            photos = newPhotos
            collectionView.reloadData()
            isUpdatingUI = false
            return
        }
        
        // 如果照片数量没有变化，检查是否需要处理排序变更
        if newCount == oldCount {
            photos = newPhotos

            // 🔧 修复：检查是否是排序变更，如果是则需要处理滚动位置
            if needsScrollToAppropriatePosition {
                DispatchQueue.main.async { [weak self] in
                    self?.setInitialContentOffsetAfterReload()
                    self?.needsScrollToAppropriatePosition = false
                    print("📍 排序变更，数量相同，调整滚动位置")
                }
            }
            return
        }
        
        // 如果照片数量增加，使用增量更新
        if newCount > oldCount {
            // 如果用户正在滚动，使用全量更新避免复杂的同步问题
            if isUserScrolling {
                print("👆 用户正在滚动，使用全量更新")
                isUpdatingUI = true
                photos = newPhotos
                collectionView.reloadData()
                isUpdatingUI = false
                return
            }
            
            // 安全检查：确保索引范围有效
            guard oldCount < newCount else {
                print("⚠️ 索引范围无效，使用全量更新")
                isUpdatingUI = true
                photos = newPhotos
                collectionView.reloadData()
                isUpdatingUI = false
                return
            }
            
            // 计算新增的索引路径
            let newIndexPaths = (oldCount..<newCount).map { IndexPath(item: $0, section: 0) }
            
            // 使用批量更新避免闪烁
            isUpdatingUI = true
            collectionView.performBatchUpdates({
                // 在批量更新块内更新数据源，确保同步
                self.photos = newPhotos
                self.collectionView.insertItems(at: newIndexPaths)
            }, completion: { _ in
                self.isUpdatingUI = false
                // print("📱 增量更新UI完成：新增 \(newCount - oldCount) 张照片")
            })
        }
    }
    
    /// 🎯 方案3：在UI更新后设置正确的初始contentOffset（智能初始位置）
    private func setInitialContentOffsetAfterReload() {
        guard !photos.isEmpty else { return }

        let currentSettings = viewModel.currentSettings

        // 强制布局更新以获取正确的contentSize（基于新数据）
        collectionView.layoutIfNeeded()

        switch currentSettings.sortOrder {
        case .newestFirst:
            // 最新在上：设置为顶部
            collectionView.contentOffset = CGPoint(x: 0, y: 0)
            print("📍 智能初始位置：最新在上模式，设置初始位置为顶部")

        case .oldestFirst:
            // 最新在下：计算底部位置，考虑安全区域
            let contentHeight = collectionView.contentSize.height
            let visibleHeight = collectionView.bounds.height
            let safeAreaBottom = view.safeAreaInsets.bottom
            let adjustedVisibleHeight = visibleHeight - safeAreaBottom
            let bottomOffset = max(0, contentHeight - adjustedVisibleHeight)

            collectionView.contentOffset = CGPoint(x: 0, y: bottomOffset)
            print("📍 智能初始位置：最新在下模式，设置初始位置为底部（offset: \(bottomOffset)，安全区域: \(safeAreaBottom)）")
        }
    }

    /// 根据排序方式滚动到合适的位置（保留作为备用方案）
    private func scrollToAppropriatePosition() {
        guard !photos.isEmpty else { return }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            guard let self = self else { return }

            let currentSettings = self.viewModel.currentSettings

            switch currentSettings.sortOrder {
            case .newestFirst:
                // 最新在上：滚动到顶部
                let topIndexPath = IndexPath(item: 0, section: 0)
                self.collectionView.scrollToItem(at: topIndexPath, at: .top, animated: false)
                print("📍 最新在上模式，滚动到顶部")

            case .oldestFirst:
                // 最新在下：滚动到底部
                let lastIndexPath = IndexPath(item: self.photos.count - 1, section: 0)
                self.collectionView.scrollToItem(at: lastIndexPath, at: .bottom, animated: false)
                print("📍 最新在下模式，滚动到底部")
            }
        }
    }
    
    /// 刷新照片数据
    private func refreshPhotos() {
        viewModel.refreshPhotos()
        // print("🔄 刷新照片数据...")
    }
    
    // MARK: - 设置处理
    
    /// 处理设置变更
    /// - Parameter settings: 新的设置
    private func handleSettingsChange(_ settings: LibrarySettings) {
        print("⚙️ 处理设置变更: \(settings.layoutType.displayName), 排序: \(settings.sortOrder.displayName)")

        // 🔧 关键修复：检查当前照片的实际排序状态来判断是否需要重新加载
        let needsDataReload = shouldReloadDataForNewSettings(settings)

        if needsDataReload {
            print("📊 数据筛选/排序变化，重新加载数据")

            // 标记需要滚动到合适位置
            needsScrollToAppropriatePosition = true
            print("📍 排序变更，标记需要滚动到合适位置")

            refreshPhotos()
        } else {
            // 🔧 修复：只有在非首次加载且用户有过交互时才保持滚动位置
            if isFirstLoad || !hasUserInteracted {
                print("🎨 首次布局或无用户交互，直接更新布局")
                updateLayoutForSettings(settings)
                collectionView.reloadData()
                
                // 首次加载后标记为非首次
                if isFirstLoad {
                    isFirstLoad = false
                }
            } else {
                print("🎨 仅布局变化，通过LayoutTransition保持滚动位置")
                
                // 获取当前布局类型
                let currentLayout = viewModel.currentSettings.layoutType
                let newLayout = settings.layoutType
                
                // 如果布局类型发生变化，通过LayoutTransition处理
                if currentLayout != newLayout {
                    // 保存当前滚动位置
                    let savedPosition = saveCurrentScrollPosition()
                    
                    // 开始布局过渡（设置变更触发的过渡使用动画）
                    layoutTransition.beginTransition(
                        from: currentLayout,
                        to: newLayout,
                        type: .settings,
                        savedPosition: savedPosition,
                        animated: true
                    )
                } else {
                    // 如果布局类型没有变化，直接更新其他设置
                    updateLayoutForSettings(settings)
                }
            }
        }
        
        print("⚙️ 设置变更处理完成")
    }

    /// 判断新设置是否需要重新加载数据
    /// - Parameter newSettings: 新的设置
    /// - Returns: 是否需要重新加载数据
    private func shouldReloadDataForNewSettings(_ newSettings: LibrarySettings) -> Bool {
        // 如果没有照片数据，肯定需要加载
        if photos.isEmpty {
            return true
        }

        // 检查当前照片的排序是否与新设置匹配
        let currentPhotosAreNewestFirst = isPhotosOrderedNewestFirst()
        let newSettingsWantNewestFirst = (newSettings.sortOrder == .newestFirst)

        // 如果排序方式不匹配，需要重新加载
        if currentPhotosAreNewestFirst != newSettingsWantNewestFirst {
            print("📊 排序方式变更：当前照片排序=\(currentPhotosAreNewestFirst ? "最新在前" : "最旧在前")，新设置=\(newSettingsWantNewestFirst ? "最新在前" : "最旧在前")")
            return true
        }

        return false
    }

    /// 检查当前照片数组是否按最新在前排序
    /// - Returns: true表示最新在前，false表示最旧在前
    private func isPhotosOrderedNewestFirst() -> Bool {
        guard photos.count >= 2 else { return true }

        // 比较前两张照片的创建时间
        let firstPhotoDate = photos[0].creationDate ?? Date.distantPast
        let secondPhotoDate = photos[1].creationDate ?? Date.distantPast

        return firstPhotoDate >= secondPhotoDate
    }

    /// 显示照片详情页
    /// - Parameter index: 照片索引
    private func showPhotoDetail(at index: Int) {
        // 获取点击的cell的frame，用于转场动画
        let indexPath = IndexPath(item: index, section: 0)
        let cell = collectionView.cellForItem(at: indexPath)
        let initialFrame = cell?.frame
        
        // 创建照片详情控制器
        let detailVC = PhotoDetailViewController(
            photos: photos,
            currentIndex: index,
            initialFrame: initialFrame
        )
        
        // 全屏模态展示
        present(detailVC, animated: true)
        
        print("📸 展示照片详情页，索引: \(index)")
    }
    
    /// 根据设置更新布局
    /// - Parameter settings: 图库设置
    private func updateLayoutForSettings(_ settings: LibrarySettings) {
        updateThumbnailSize()
        collectionView.collectionViewLayout.invalidateLayout()
    }
    
    // MARK: - 位置保存和恢复
    
    /// 获取屏幕中心区域的照片索引
    /// - Returns: 中心区域的照片索引数组，按距离中心的远近排序
    private func getCenterAreaPhotos() -> [IndexPath] {
        let visibleRect = collectionView.bounds
        let centerPoint = CGPoint(x: visibleRect.midX, y: visibleRect.midY)

        // 定义中心区域：屏幕中心40%的区域
        let centerAreaSize = CGSize(
            width: visibleRect.width * 0.4,
            height: visibleRect.height * 0.4
        )
        let centerAreaRect = CGRect(
            x: centerPoint.x - centerAreaSize.width / 2,
            y: centerPoint.y - centerAreaSize.height / 2,
            width: centerAreaSize.width,
            height: centerAreaSize.height
        )

        // 获取与中心区域相交的所有照片
        var centerPhotos: [(IndexPath, CGFloat)] = []

        for indexPath in collectionView.indexPathsForVisibleItems {
            guard let cellFrame = collectionView.layoutAttributesForItem(at: indexPath)?.frame else { continue }

            // 检查cell是否与中心区域相交
            if centerAreaRect.intersects(cellFrame) {
                // 计算cell中心到屏幕中心的距离
                let cellCenter = CGPoint(x: cellFrame.midX, y: cellFrame.midY)
                let distance = sqrt(pow(cellCenter.x - centerPoint.x, 2) + pow(cellCenter.y - centerPoint.y, 2))
                centerPhotos.append((indexPath, distance))
            }
        }

        // 按距离排序，最近的在前面
        centerPhotos.sort { $0.1 < $1.1 }

        let result = centerPhotos.map { $0.0 }
        print("📍 获取中心区域照片: \(result.count)张，最中心的是索引 \(result.first?.item ?? -1)")

        return result
    }

    /// 保存当前滚动位置
    /// - Returns: 当前滚动位置信息，如果保存失败返回nil
    private func saveCurrentScrollPosition() -> ScrollPosition? {
        // 安全检查：确保照片数组不为空
        guard !photos.isEmpty else {
            print("⚠️ 保存位置失败：照片数组为空")
            return nil
        }
        
        // 安全检查：确保CollectionView已经布局
        guard collectionView.bounds.width > 0 && collectionView.bounds.height > 0 else {
            print("⚠️ 保存位置失败：CollectionView尺寸无效")
            return nil
        }
        
        // 安全检查：确保用户有过交互，不保存初始位置
        guard hasUserInteracted else {
            print("⚠️ 保存位置失败：用户尚未进行任何交互")
            return nil
        }
        
        // 安全检查：确保当前不在顶部（避免保存无意义的顶部位置）
        let currentOffset = collectionView.contentOffset
        if currentOffset.y <= 0 {
            print("⚠️ 保存位置失败：当前在顶部位置")
            return nil
        }
        
        let visibleRect = collectionView.bounds
        let visibleCenter = CGPoint(x: visibleRect.midX, y: visibleRect.midY)
        
        // 找到最接近可见区域中心的照片
        let visibleIndexPaths = collectionView.indexPathsForVisibleItems
        guard !visibleIndexPaths.isEmpty else {
            print("⚠️ 保存位置失败：没有可见的照片")
            return nil
        }
        
        // 找到最接近中心的IndexPath
        var closestIndexPath: IndexPath?
        var closestDistance: CGFloat = CGFloat.greatestFiniteMagnitude
        
        for indexPath in visibleIndexPaths {
            if let cellFrame = collectionView.layoutAttributesForItem(at: indexPath)?.frame {
                let cellCenter = CGPoint(x: cellFrame.midX, y: cellFrame.midY)
                let distance = sqrt(pow(cellCenter.x - visibleCenter.x, 2) + pow(cellCenter.y - visibleCenter.y, 2))
                
                if distance < closestDistance {
                    closestDistance = distance
                    closestIndexPath = indexPath
                }
            }
        }
        
        guard let centerIndexPath = closestIndexPath else {
            print("⚠️ 保存位置失败：无法找到中心照片")
            return nil
        }
        
        // 计算中心照片的偏移量
        let centerCellFrame = collectionView.layoutAttributesForItem(at: centerIndexPath)?.frame ?? .zero
        let centerOffset = CGPoint(
            x: visibleCenter.x - centerCellFrame.midX,
            y: visibleCenter.y - centerCellFrame.midY
        )
        
        // 创建ScrollPosition实例
        let scrollPosition = ScrollPosition(
            centerIndexPath: centerIndexPath,
            centerOffset: centerOffset,
            contentOffset: collectionView.contentOffset,
            visibleRect: visibleRect,
            layoutType: viewModel.currentSettings.layoutType
        )
        
        print("💾 保存滚动位置成功")
        print("   - 中心照片索引: \(centerIndexPath.item)")
        print("   - 中心偏移: \(centerOffset)")
        print("   - 内容偏移: \(collectionView.contentOffset)")
        print("   - 布局类型: \(viewModel.currentSettings.layoutType.displayName)")
        
        return scrollPosition
    }



    /// 🎯 新增：为特定目标照片保存滚动位置
    /// - Parameter targetIndexPath: 目标照片的索引路径
    /// - Returns: 保存的滚动位置信息
    private func saveScrollPositionForTarget(_ targetIndexPath: IndexPath) -> ScrollPosition? {
        guard !photos.isEmpty && targetIndexPath.item < photos.count else {
            print("⚠️ 保存目标位置失败：索引无效")
            return nil
        }

        let visibleRect = collectionView.bounds
        let visibleCenter = CGPoint(x: visibleRect.midX, y: visibleRect.midY)

        // 使用目标照片作为中心照片
        let centerIndexPath = targetIndexPath

        // 计算目标照片的偏移量
        let centerCellFrame = collectionView.layoutAttributesForItem(at: centerIndexPath)?.frame ?? .zero
        let centerOffset = CGPoint(
            x: visibleCenter.x - centerCellFrame.midX,
            y: visibleCenter.y - centerCellFrame.midY
        )

        // 创建ScrollPosition实例
        let scrollPosition = ScrollPosition(
            centerIndexPath: centerIndexPath,
            centerOffset: centerOffset,
            contentOffset: collectionView.contentOffset,
            visibleRect: visibleRect,
            layoutType: viewModel.currentSettings.layoutType
        )

        print("🎯 保存目标照片位置成功")
        print("   - 目标照片索引: \(centerIndexPath.item)")
        print("   - 目标偏移: \(centerOffset)")
        print("   - 内容偏移: \(collectionView.contentOffset)")
        print("   - 布局类型: \(viewModel.currentSettings.layoutType.displayName)")

        return scrollPosition
    }

    /// 恢复滚动位置
    /// - Parameters:
    ///   - scrollPosition: 要恢复的滚动位置
    ///   - animated: 是否使用动画
    private func restoreScrollPosition(_ scrollPosition: ScrollPosition, animated: Bool = false) {
        // 验证ScrollPosition是否有效
        guard scrollPosition.isValid else {
            print("⚠️ 恢复位置失败：ScrollPosition无效")
            restoreToFallbackPosition(scrollPosition, animated: animated)
            return
        }
        
        // 安全检查：确保照片数组不为空
        guard !photos.isEmpty else {
            print("⚠️ 恢复位置失败：照片数组为空")
            return
        }
        
        // 强制布局更新
        collectionView.layoutIfNeeded()
        
        // 使用智能算法计算最佳目标位置
        let targetLayout = viewModel.currentSettings.layoutType
        let targetOffset = scrollPosition.getOptimalContentOffset(
            for: targetLayout,
            in: collectionView,
            photos: photos
        )
        
        // 执行滚动
        print("🎯 恢复滚动位置")
        print("   - 目标偏移: \(targetOffset)")
        print("   - 使用动画: \(animated)")
        
        collectionView.setContentOffset(targetOffset, animated: animated)
        
        // 如果使用动画，等待动画完成后验证位置
        if animated {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
                self?.validateRestoredPosition(scrollPosition)
            }
        } else {
            validateRestoredPosition(scrollPosition)
        }
    }
    
    /// 使用备选方案恢复滚动位置
    /// - Parameters:
    ///   - scrollPosition: 滚动位置信息
    ///   - animated: 是否使用动画
    private func restoreToFallbackPosition(_ scrollPosition: ScrollPosition, animated: Bool = false) {
        // 方案1：使用相对位置比例
        let fallbackOffset = scrollPosition.getFallbackContentOffset(in: collectionView)
        
        print("🔄 使用备选方案恢复位置")
        print("   - 备选偏移: \(fallbackOffset)")
        
        collectionView.setContentOffset(fallbackOffset, animated: animated)
        
        // 方案2：如果备选方案也失败，尝试滚动到对应的索引
        if fallbackOffset == .zero && scrollPosition.centerIndexPath.item < photos.count {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                self?.scrollToIndexIfPossible(scrollPosition.centerIndexPath, animated: animated)
            }
        }
    }
    
    /// 尝试滚动到指定索引
    /// - Parameters:
    ///   - indexPath: 目标索引路径
    ///   - animated: 是否使用动画
    private func scrollToIndexIfPossible(_ indexPath: IndexPath, animated: Bool = false) {
        guard indexPath.item >= 0 && indexPath.item < photos.count else {
            print("⚠️ 无法滚动到索引 \(indexPath.item)：索引越界")
            return
        }
        
        // 使用scrollToItem方法
        collectionView.scrollToItem(at: indexPath, at: .centeredVertically, animated: animated)
        print("📍 滚动到索引 \(indexPath.item)")
    }
    
    /// 验证恢复的位置是否准确
    /// - Parameter originalPosition: 原始位置信息
    private func validateRestoredPosition(_ originalPosition: ScrollPosition) {
        let currentVisibleIndexPaths = collectionView.indexPathsForVisibleItems
        let targetIndex = originalPosition.centerIndexPath.item
        
        // 使用新的精度评估系统
        let assessment = originalPosition.getAccuracyAssessment(in: collectionView, photos: photos)
        
        // 检查目标照片是否在可见区域内
        let isTargetVisible = currentVisibleIndexPaths.contains { $0.item == targetIndex }
        
        print("📊 位置恢复评估报告:")
        print("   - 目标照片索引: \(targetIndex)")
        print("   - 目标照片可见: \(isTargetVisible ? "✅" : "❌")")
        print("   - 当前可见索引: \(currentVisibleIndexPaths.map { $0.item })")
        print("   - 精度评分: \(String(format: "%.1f", assessment.score))/100")
        print("   - 评估结果: \(assessment.description)")
        print("   - 当前内容偏移: \(collectionView.contentOffset)")
        
        // 记录详细的位置信息用于调试
        if let cellFrame = collectionView.layoutAttributesForItem(at: originalPosition.centerIndexPath)?.frame {
            let collectionCenter = CGPoint(x: collectionView.bounds.midX, y: collectionView.bounds.midY)
            let cellCenter = CGPoint(x: cellFrame.midX, y: cellFrame.midY)
            let distance = sqrt(pow(cellCenter.x - collectionCenter.x, 2) + pow(cellCenter.y - collectionCenter.y, 2))
            
            print("   - 目标照片位置: \(cellFrame)")
            print("   - 屏幕中心点: \(collectionCenter)")
            print("   - 照片中心点: \(cellCenter)")
            print("   - 中心距离: \(String(format: "%.1f", distance))px")
        }
        
        // 如果精度低于70分，记录为待优化问题
        if assessment.score < 70.0 {
            print("⚠️ 位置恢复精度待优化，得分低于70分")
            recordPositionIssueForOptimization(originalPosition, assessment: assessment)
        }
    }
    
    /// 记录位置问题以供优化
    /// - Parameters:
    ///   - position: 位置信息
    ///   - assessment: 评估结果
    private func recordPositionIssueForOptimization(_ position: ScrollPosition, assessment: (score: Double, description: String)) {
        let issue = """
        📝 位置恢复问题记录:
        - 时间: \(Date())
        - 原始布局: \(position.layoutType.displayName)
        - 目标布局: \(viewModel.currentSettings.layoutType.displayName)
        - 目标索引: \(position.centerIndexPath.item)
        - 评估得分: \(assessment.score)
        - 问题描述: \(assessment.description)
        - 原始偏移: \(position.centerOffset)
        - 当前偏移: \(collectionView.contentOffset)
        """
        
        print(issue)
        // 这里可以进一步将问题信息存储到文件或服务器用于后续分析
    }
    
    /// 执行布局变更并保持滚动位置
    /// - Parameter newSettings: 新的设置
    private func performLayoutChangeWithPositionPreservation(newSettings: LibrarySettings) {
        // 第一步：保存当前滚动位置
        let savedPosition = saveCurrentScrollPosition()
        
        // 第二步：更新布局设置
        updateLayoutForSettings(newSettings)
        
        // 第三步：刷新UI（使用performBatchUpdates避免突兀的重新加载）
        collectionView.performBatchUpdates({
            // 在批量更新中让布局失效
            collectionView.collectionViewLayout.invalidateLayout()
        }, completion: { [weak self] _ in
            // 第四步：恢复滚动位置
            if let position = savedPosition {
                self?.restoreScrollPosition(position, animated: false)
            }
        })
        
        print("🔄 布局变更完成，位置保持处理完毕")
    }
    
    /// 更新缩略图尺寸
    private func updateThumbnailSize() {
        let screenWidth = UIScreen.main.bounds.width
        let settings = viewModel.currentSettings
        let columnsPerRow = settings.layoutType.columnsPerRow
        let spacing = settings.gridSpacing

        let totalSpacing = spacing * CGFloat(columnsPerRow + 1)
        let itemWidth = (screenWidth - totalSpacing) / CGFloat(columnsPerRow)

        thumbnailSize = CGSize(width: itemWidth * UIScreen.main.scale,
                             height: itemWidth * UIScreen.main.scale)

        print("📏 缩略图尺寸更新为: \(thumbnailSize)")

        // 布局切换时清理内存缓存
        photoLibraryService.clearMemoryCache()
        print("🧹 布局切换：清理内存缓存")
    }
    
    // MARK: - 按钮事件
    
    @objc private func searchButtonTapped() {
        print("🔍 搜索按钮点击")
        // TODO: 实现搜索功能
    }
    
    @objc private func settingsButtonTapped() {
        print("⚙️ 设置按钮点击")
        let settingsVC = LibrarySettingsViewController()
        settingsVC.delegate = self
        let navController = UINavigationController(rootViewController: settingsVC)
        present(navController, animated: true)
    }
    
    @objc private func selectButtonTapped() {
        isSelectionMode.toggle()
        updateSelectionMode()
        print("✅ 选择模式切换: \(isSelectionMode)")
    }
    
    /// 更新选择模式UI
    private func updateSelectionMode() {
        if isSelectionMode {
            selectButton.title = "取消"
            // TODO: 显示批量操作工具栏
        } else {
            selectButton.title = "选择"
            // TODO: 隐藏批量操作工具栏
            // 清除所有选中状态
            photos.indices.forEach { photos[$0].isSelected = false }
            collectionView.reloadData()
        }
    }
    
    /// 🔧 处理长按手势显示日志信息
    @objc private func handleLongPress(_ gesture: UILongPressGestureRecognizer) {
        if gesture.state == .began {
            showGestureLogInfo()
        }
    }
    
    /// 🔧 显示手势日志信息
    private func showGestureLogInfo() {
        let alert = UIAlertController(title: "手势调试信息", message: nil, preferredStyle: .alert)

        // 🔧 新增：显示手势状态和配置信息
        let gestureStatus = gestureLayoutManager.isGestureInProgress ? "进行中" : "空闲"
        let appLoadStatus = isAppFullyLoaded ? "已完成" : "加载中"
        let currentLayout = viewModel.currentSettings.layoutType.displayName

        alert.message = """
        手势状态: \(gestureStatus)
        应用加载: \(appLoadStatus)
        当前布局: \(currentLayout)

        测试提示：
        • 双指缩放可切换布局
        • 缩小手势 → 更多列
        • 放大手势 → 更少列
        """

        // 添加测试手势按钮
        let testAction = UIAlertAction(title: "测试手势映射", style: .default) { [weak self] _ in
            self?.gestureLayoutManager.testScaleMappingLogic()
        }
        alert.addAction(testAction)

        // 添加检查状态按钮
        let statusAction = UIAlertAction(title: "检查手势状态", style: .default) { [weak self] _ in
            self?.gestureLayoutManager.checkGestureRecognizerStatus()
        }
        alert.addAction(statusAction)

        // 获取日志文件路径
        if let logPath = GestureLogger.shared.getLogFilePath() {
            // 添加复制路径按钮
            let copyAction = UIAlertAction(title: "复制日志路径", style: .default) { _ in
                UIPasteboard.general.string = logPath
                print("📋 日志文件路径已复制到剪贴板")
            }
            alert.addAction(copyAction)

            // 添加清空日志按钮
            let clearAction = UIAlertAction(title: "清空日志", style: .destructive) { _ in
                GestureLogger.shared.clearLog()
                print("🗑️ 手势日志已清空")
            }
            alert.addAction(clearAction)
        }

        // 添加取消按钮
        let cancelAction = UIAlertAction(title: "取消", style: .cancel)
        alert.addAction(cancelAction)

        present(alert, animated: true)
    }
}

// MARK: - UICollectionViewDataSource

extension PhotoLibraryViewController: UICollectionViewDataSource {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return photos.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: PhotoGridCell.identifier, for: indexPath) as! PhotoGridCell

        let photo = photos[indexPath.item]

        // 配置Cell
        cell.configure(with: photo, targetSize: thumbnailSize, isSelectionMode: isSelectionMode)

        // 使用MediaManager加载缩略图
        mediaManager.loadThumbnail(for: photo.asset, targetSize: thumbnailSize) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let image):
                    cell.setImage(image)
                case .failure(let error):
                    print("❌ 缩略图加载失败: \(error)")
                    cell.setPlaceholderImage()
                }
            }
        }

        return cell
    }
}

// MARK: - UIScrollViewDelegate

extension PhotoLibraryViewController: UIScrollViewDelegate {

    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        // 🎯 渐进式加载：滚动时无需特殊处理

        // 重置定时器
        scrollStopTimer?.invalidate()
        scrollStopTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { [weak self] _ in
            self?.handleScrollStopped()
        }
    }

    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        isUserScrolling = true
        hasUserInteracted = true // 标记用户有过交互

        // 🎯 渐进式加载：滚动开始时无需特殊处理

        print("👆 用户开始滚动")
    }

    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if !decelerate {
            isUserScrolling = false
            handleScrollStopped()

            // 🎯 渐进式加载：滚动结束时无需特殊处理

            print("👆 用户停止滚动")
        }
    }

    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        isUserScrolling = false
        handleScrollStopped()

        // 🎯 渐进式加载：滚动结束时无需特殊处理

        print("👆 滚动减速结束")
    }

    /// 处理滚动停止
    private func handleScrollStopped() {
        scrollStopTimer?.invalidate()
        scrollStopTimer = nil

        // 检测可见区域的照片压缩问题
        checkVisiblePhotosCompression()
    }

    /// 检测可见区域照片的压缩问题
    private func checkVisiblePhotosCompression() {
        let visibleIndexPaths = collectionView.indexPathsForVisibleItems

        for indexPath in visibleIndexPaths {
            guard indexPath.item < photos.count,
                  let cell = collectionView.cellForItem(at: indexPath) as? PhotoGridCell else { continue }

            let photo = photos[indexPath.item]
            checkAndFixPhotoCompression(photo: photo, cell: cell)
        }
    }


}

// MARK: - UICollectionViewDelegate

extension PhotoLibraryViewController: UICollectionViewDelegate {
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: true)
        
        if isSelectionMode {
            // 选择模式：切换选中状态
            photos[indexPath.item].isSelected.toggle()
            collectionView.reloadItems(at: [indexPath])
        } else {
            // 正常模式：全屏查看照片
            let photo = photos[indexPath.item]
            showPhotoDetail(at: indexPath.item)
            print("📸 点击查看照片: \(photo.id)")
        }
    }
    
    // MARK: - 滑动预加载优化
    
    func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        // 预加载即将显示的图片周围的图片
        preloadImagesAroundIndex(indexPath.item)
    }
    
    func collectionView(_ collectionView: UICollectionView, didEndDisplaying cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        // 停止预加载已经离开屏幕的图片
        stopPreloadingImagesAroundIndex(indexPath.item)
    }

    /// 预加载指定索引周围的图片
    private func preloadImagesAroundIndex(_ index: Int) {
        // 安全检查：确保照片数组不为空
        guard !photos.isEmpty else {
            print("⚠️ 照片数组为空，跳过预加载")
            return
        }
        
        // 安全检查：确保索引有效
        guard index >= 0 && index < photos.count else {
            print("⚠️ 预加载索引越界: \(index), 总数: \(photos.count)")
            return
        }
        
        let settings = viewModel.currentSettings
        let columnsPerRow = settings.layoutType.columnsPerRow
        
        // 🎯 原生级别预加载：当前视图 + 1屏
        let screenHeight = collectionView.bounds.height
        let itemHeight = thumbnailSize.height + settings.gridSpacing
        let rowsPerScreen = Int(ceil(screenHeight / itemHeight))
        let itemsPerScreen = rowsPerScreen * columnsPerRow
        
        // 预加载范围：当前视图 + 上下各1屏
        let preloadRange = itemsPerScreen
        
        let startIndex = max(0, index - preloadRange)
        let endIndex = min(photos.count - 1, index + preloadRange)
        
        // 安全检查：确保范围有效
        guard startIndex <= endIndex else {
            print("⚠️ 预加载范围无效: \(startIndex)...\(endIndex)")
            return
        }
        
        let photosToPreload = Array(photos[startIndex...endIndex])
        
        // 使用简化的预加载功能
        let assetsToPreload = photosToPreload.map { $0.asset }
        photoLibraryService.preloadPhotos(in: startIndex..<endIndex+1, targetSize: thumbnailSize)
        
        // print("📦 预加载范围: \(startIndex)...\(endIndex), 共 \(photosToPreload.count) 张")
    }
    
    /// 停止预加载指定索引周围的图片
    private func stopPreloadingImagesAroundIndex(_ index: Int) {
        // 安全检查：确保照片数组不为空
        guard !photos.isEmpty else {
            return
        }
        
        // 安全检查：确保索引有效
        guard index >= 0 && index < photos.count else {
            return
        }
        
        let settings = viewModel.currentSettings
        let columnsPerRow = settings.layoutType.columnsPerRow
        let stopRange = columnsPerRow * 2 // 停止范围适中
        
        let startIndex = max(0, index - stopRange)
        let endIndex = min(photos.count - 1, index + stopRange)
        
        // 安全检查：确保范围有效
        guard startIndex <= endIndex else {
            return
        }
        
        let photosToStop = Array(photos[startIndex...endIndex])
        
        // 简化的停止预加载（在新架构中不需要显式停止）
        // 新的MediaManager会自动管理请求
    }
}

// MARK: - UICollectionViewDelegateFlowLayout

extension PhotoLibraryViewController: UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let settings = viewModel.currentSettings
        let screenWidth = collectionView.bounds.width
        let spacing = settings.gridSpacing
        let columnsPerRow = settings.layoutType.columnsPerRow
        
        // 获取FlowLayout的sectionInset
        guard let flowLayout = collectionViewLayout as? UICollectionViewFlowLayout else {
            // 如果转换失败，使用默认的sectionInset
            let totalSpacing = spacing * CGFloat(columnsPerRow - 1)
            let itemWidth = (screenWidth - totalSpacing) / CGFloat(columnsPerRow)
            return CGSize(width: itemWidth, height: itemWidth)
        }
        
        let sectionInset = flowLayout.sectionInset
        let totalSpacing = spacing * CGFloat(columnsPerRow - 1) + sectionInset.left + sectionInset.right
        let itemWidth = (screenWidth - totalSpacing) / CGFloat(columnsPerRow)
        
        return CGSize(width: itemWidth, height: itemWidth)
    }
}

// MARK: - UICollectionViewDataSourcePrefetching

extension PhotoLibraryViewController: UICollectionViewDataSourcePrefetching {
    
    func collectionView(_ collectionView: UICollectionView, prefetchItemsAt indexPaths: [IndexPath]) {
        // 安全检查：确保照片数组不为空
        guard !photos.isEmpty else { return }
        
        let photosToCache: [PhotoModel] = indexPaths.compactMap { indexPath in
            guard indexPath.item >= 0 && indexPath.item < photos.count else {
                print("⚠️ 预取索引越界: \(indexPath.item), 总数: \(photos.count)")
                return nil
            }
            return photos[indexPath.item]
        }
        
        if !photosToCache.isEmpty {
            let assetsToCache = photosToCache.map { $0.asset }
            mediaManager.preloadImages(for: assetsToCache, targetSize: thumbnailSize)
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, cancelPrefetchingForItemsAt indexPaths: [IndexPath]) {
        // 安全检查：确保照片数组不为空
        guard !photos.isEmpty else { return }
        
        let photosToStopCaching: [PhotoModel] = indexPaths.compactMap { indexPath in
            guard indexPath.item >= 0 && indexPath.item < photos.count else {
                return nil
            }
            return photos[indexPath.item]
        }
        
        if !photosToStopCaching.isEmpty {
            // 在新架构中，MediaManager会自动管理请求取消
            for photo in photosToStopCaching {
                mediaManager.cancelRequest(for: photo.asset)
            }
        }
    }
}

// MARK: - LibrarySettingsDelegate

extension PhotoLibraryViewController: LibrarySettingsDelegate {
    
    func settingsDidChange(_ settings: LibrarySettings) {
        handleSettingsChange(settings)
    }
}

// MARK: - GestureLayoutManagerDelegate

extension PhotoLibraryViewController: GestureLayoutManagerDelegate {
    
    func gestureManagerDidBeginGesture(_ manager: GestureLayoutManager) {
        let timestamp = CFAbsoluteTimeGetCurrent()
        let timestampString = String(format: "%.3f", timestamp)

        print("🤏 [⏰\(timestampString)] 控制器收到手势开始回调")

        // 🔧 新增：检查应用是否完全加载，防止初期干扰
        if !isAppFullyLoaded {
            print("⚠️ 应用尚未完全加载，手势可能不稳定")
        }

        // 设置ViewModel的手势状态 - 记录时间
        let viewModelStartTime = CFAbsoluteTimeGetCurrent()
        viewModel.setGestureActive(true)
        let viewModelDuration = CFAbsoluteTimeGetCurrent() - viewModelStartTime

        print("🤏 [⏰\(String(format: "%.3f", CFAbsoluteTimeGetCurrent()))] ViewModel手势状态设置完成，耗时: \(String(format: "%.3f", viewModelDuration * 1000))ms")

        // 🔧 新增：暂停所有可能干扰手势的后台任务
        isUpdatingUI = true  // 防止UI更新干扰

        // 在手势开始时可以进行一些准备工作
        // 例如：禁用其他手势，显示缩放指示器等
    }
    
    func gestureManager(_ manager: GestureLayoutManager, didUpdateToLayout layoutType: LibrarySettings.LayoutType, scale: CGFloat, targetIndexPath: IndexPath?) {
        let timestamp = CFAbsoluteTimeGetCurrent()
        let timestampString = String(format: "%.3f", timestamp)
        
        let targetInfo = targetIndexPath != nil ? "目标照片: \(targetIndexPath!.item)" : "无目标照片"
        print("🤏 [⏰\(timestampString)] 控制器收到布局更新回调 - 布局: \(layoutType.displayName), 缩放: \(String(format: "%.2f", scale)), \(targetInfo)")
        
        // 获取当前布局类型
        let currentLayout = viewModel.currentSettings.layoutType
        
        // 如果布局类型发生变化，直接更新UI
        if currentLayout != layoutType {
            let updateStartTime = CFAbsoluteTimeGetCurrent()
            print("🤏 [⏰\(String(format: "%.3f", updateStartTime))] 检测到布局变化: \(currentLayout.displayName) → \(layoutType.displayName)")

            // 🎯 简单直接：保存手势选中的照片索引
            var targetPhotoIndex: Int?

            if let targetIndexPath = targetIndexPath {
                // 有手势目标照片，直接保存索引
                targetPhotoIndex = targetIndexPath.item
                print("🎯 保存手势目标照片索引: \(targetPhotoIndex!)")
            } else {
                // 没有手势目标，使用中心区域照片
                let centerPhotos = getCenterAreaPhotos()
                if let centerPhoto = centerPhotos.first {
                    targetPhotoIndex = centerPhoto.item
                    print("🎯 保存中心区域照片索引: \(targetPhotoIndex!)")
                }
            }
            
            // 🔧 分离设置更新和动画更新，避免performWithoutAnimation阻止动画

            // 1. 立即静默更新设置（无动画）
            UIView.performWithoutAnimation {
                let settingsUpdateStart = CFAbsoluteTimeGetCurrent()
                updateLayoutTypeQuietly(layoutType)
                let settingsUpdateDuration = CFAbsoluteTimeGetCurrent() - settingsUpdateStart
                print("🤏 [⏰\(String(format: "%.3f", CFAbsoluteTimeGetCurrent()))] 设置更新完成，耗时: \(String(format: "%.3f", settingsUpdateDuration * 1000))ms")

                // 3. 静默更新ViewModel状态（保持同步，但不阻塞手势响应）
                let viewModelUpdateStart = CFAbsoluteTimeGetCurrent()
                viewModel.updateLayoutTypeQuietly(layoutType)
                let viewModelUpdateDuration = CFAbsoluteTimeGetCurrent() - viewModelUpdateStart
                print("🤏 [⏰\(String(format: "%.3f", CFAbsoluteTimeGetCurrent()))] ViewModel状态更新完成，耗时: \(String(format: "%.3f", viewModelUpdateDuration * 1000))ms")

                // 🎯 简单直接：滚动到目标照片位置
                if let photoIndex = targetPhotoIndex, photoIndex < self.photos.count {
                    let targetIndexPath = IndexPath(item: photoIndex, section: 0)

                    // 延迟一点执行，确保布局已经更新
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        self.collectionView.scrollToItem(at: targetIndexPath, at: .centeredVertically, animated: false)
                        print("🎯 滚动到目标照片: 索引 \(photoIndex)")
                    }
                }
            }

            // 2. 使用动画更新CollectionView布局（在performWithoutAnimation外部，确保动画能正常执行）
            let layoutUpdateStart = CFAbsoluteTimeGetCurrent()
            updateCollectionViewLayoutWithAnimation(for: layoutType)
            let layoutUpdateDuration = CFAbsoluteTimeGetCurrent() - layoutUpdateStart
            print("🤏 [⏰\(String(format: "%.3f", CFAbsoluteTimeGetCurrent()))] CollectionView动画布局更新开始，耗时: \(String(format: "%.3f", layoutUpdateDuration * 1000))ms")
            
            let totalUpdateDuration = CFAbsoluteTimeGetCurrent() - updateStartTime
            print("✅ [⏰\(String(format: "%.3f", CFAbsoluteTimeGetCurrent()))] 手势布局更新完成: \(layoutType.displayName), 总耗时: \(String(format: "%.3f", totalUpdateDuration * 1000))ms")
        } else {
            print("🚫 [⏰\(timestampString)] 布局未变化，跳过更新: \(layoutType.displayName)")
        }
    }
    
    func gestureManager(_ manager: GestureLayoutManager, didEndWithLayout finalLayout: LibrarySettings.LayoutType) {
        let timestamp = CFAbsoluteTimeGetCurrent()
        let timestampString = String(format: "%.3f", timestamp)
        
        print("🤏 [⏰\(timestampString)] 控制器收到手势结束回调 - 最终布局: \(finalLayout.displayName)")
        
        // 获取当前实际的布局类型
        let currentLayout = viewModel.currentSettings.layoutType
        print("🤏 [⏰\(timestampString)] 手势结束时当前布局: \(currentLayout.displayName)")
        
        // 设置ViewModel的手势状态 - 记录时间
        let viewModelStartTime = CFAbsoluteTimeGetCurrent()
        viewModel.setGestureActive(false)
        let viewModelDuration = CFAbsoluteTimeGetCurrent() - viewModelStartTime
        print("🤏 [⏰\(String(format: "%.3f", CFAbsoluteTimeGetCurrent()))] ViewModel手势状态重置完成，耗时: \(String(format: "%.3f", viewModelDuration * 1000))ms")

        // 🔧 新增：重新启用UI更新
        isUpdatingUI = false
        
        // 🔧 关键修复：确保布局不会被重置
        if currentLayout != finalLayout {
            let fixStartTime = CFAbsoluteTimeGetCurrent()
            print("⚠️ [⏰\(String(format: "%.3f", fixStartTime))] 警告：手势结束后布局不一致！当前: \(currentLayout.displayName), 期望: \(finalLayout.displayName)")
            
            // 强制更新为最终布局
            viewModel.updateLayoutTypeQuietly(finalLayout)
            updateCollectionViewLayoutImmediately(for: finalLayout)
            
            let fixDuration = CFAbsoluteTimeGetCurrent() - fixStartTime
            print("🔧 [⏰\(String(format: "%.3f", CFAbsoluteTimeGetCurrent()))] 布局强制修正完成，耗时: \(String(format: "%.3f", fixDuration * 1000))ms")
        }
        
        let finalLayout = viewModel.currentSettings.layoutType
        print("🤏 [⏰\(String(format: "%.3f", CFAbsoluteTimeGetCurrent()))] 手势结束处理完成 - 布局确认: \(finalLayout.displayName)")
        
        // 手势结束后可以进行一些清理工作
        // 例如：重新启用其他手势，隐藏缩放指示器等
    }
    
    func getCurrentLayoutType(for manager: GestureLayoutManager) -> LibrarySettings.LayoutType {
        return viewModel.currentSettings.layoutType
    }

    func gestureManager(_ manager: GestureLayoutManager, didUpdateRealtimeScale scale: CGFloat) {
        // 这个方法现在不再需要，因为我们使用UIView.animate来处理布局切换动画
        // 动画效果将在updateCollectionViewLayoutWithAnimation方法中实现
    }
}

// MARK: - LayoutTransitionDelegate

extension PhotoLibraryViewController: LayoutTransitionDelegate {
    
    func layoutTransition(_ transition: LayoutTransition, willBeginTransition transitionInfo: LayoutTransitionInfo) {
        print("🔄 即将开始布局过渡: \(transitionInfo.transitionDirection)")
        
        // 可以在这里准备过渡前的状态
        // 例如：禁用用户交互、显示加载指示器等
    }
    
    func layoutTransition(_ transition: LayoutTransition, performTransition transitionInfo: LayoutTransitionInfo, completion: @escaping (Bool) -> Void) {
        print("🔄 执行布局过渡: \(transitionInfo.transitionDirection)")
        
        // 执行实际的布局切换
        performLayoutTransition(
            from: transitionInfo.fromLayout,
            to: transitionInfo.toLayout,
            savedPosition: transitionInfo.savedScrollPosition,
            animated: transitionInfo.animated,
            completion: completion
        )
    }
    
    func layoutTransition(_ transition: LayoutTransition, didCompleteTransition transitionInfo: LayoutTransitionInfo) {
        print("✅ 布局过渡完成: \(transitionInfo.transitionDirection)")

        // 可以在这里处理过渡完成后的逻辑
        // 例如：重新启用用户交互、隐藏加载指示器等

        // 🔧 渐进式加载：布局切换完成后无需特殊处理
    }
    
    func layoutTransition(_ transition: LayoutTransition, didCancelTransition transitionInfo: LayoutTransitionInfo) {
        print("❌ 布局过渡取消: \(transitionInfo.transitionDirection)")
        
        // 处理过渡取消的情况
        // 例如：恢复到原始状态、重新启用用户交互等
    }
}

// MARK: - 布局过渡实现

extension PhotoLibraryViewController {
    
    /// 执行布局过渡的核心方法
    /// - Parameters:
    ///   - fromLayout: 源布局类型
    ///   - toLayout: 目标布局类型
    ///   - savedPosition: 保存的滚动位置
    ///   - animated: 是否使用动画
    ///   - completion: 完成回调
    private func performLayoutTransition(
        from fromLayout: LibrarySettings.LayoutType,
        to toLayout: LibrarySettings.LayoutType,
        savedPosition: ScrollPosition?,
        animated: Bool,
        completion: @escaping (Bool) -> Void
    ) {
        // 获取当前过渡类型
        let currentTransitionType = layoutTransition.getCurrentTransitionType()
        
        if currentTransitionType == .gestureTriggered {
            // 手势触发的过渡：直接更新CollectionView布局，不通过ViewModel
            print("🤏 手势触发过渡：直接更新CollectionView布局")
            
            // 直接更新SettingsService中的布局类型（不触发通知）
            updateLayoutTypeQuietly(toLayout)
            
            // 直接更新CollectionView布局
            updateCollectionViewLayout(for: toLayout)
            
        } else {
            // 程序触发的过渡：通过ViewModel更新
            print("🔄 程序触发过渡：通过ViewModel更新")
            
            // 更新ViewModel中的布局类型
            viewModel.updateLayoutType(toLayout)
            
            // 更新CollectionView布局
            updateLayoutForSettings(viewModel.currentSettings)
        }
        
        // 执行布局更新
        if animated {
            // 使用流畅的动画进行布局切换
            performAnimatedLayoutTransition(
                from: fromLayout,
                to: toLayout,
                savedPosition: savedPosition,
                completion: completion
            )
        } else {
            // 直接进行布局切换（用于手势实时更新）
            performImmediateLayoutTransition(
                savedPosition: savedPosition,
                completion: completion
            )
        }
    }
    
    /// 静默更新布局类型（不触发通知）
    /// - Parameter layoutType: 新的布局类型
    private func updateLayoutTypeQuietly(_ layoutType: LibrarySettings.LayoutType) {
        let settingsService = SettingsService.shared
        settingsService.updateLayoutTypeQuietly(layoutType)
    }
    
    /// 直接更新CollectionView布局
    /// - Parameter layoutType: 布局类型
    private func updateCollectionViewLayout(for layoutType: LibrarySettings.LayoutType) {
        let itemSize = calculateItemSize(for: layoutType)
        collectionViewLayout.itemSize = itemSize

        // 🔧 修复：使用updateThumbnailSize方法，确保缓存清理被触发
        updateThumbnailSize()

        // 🔧 关键修复：立即强制刷新布局
        collectionViewLayout.invalidateLayout()
        
        // 🔧 强制立即更新布局，确保手势期间实时刷新
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 使用performBatchUpdates确保布局立即更新
            self.collectionView.performBatchUpdates({
                // 强制重新计算布局
                self.collectionView.collectionViewLayout.invalidateLayout()
            }, completion: { _ in
                print("📐 CollectionView布局更新完成 - 类型: \(layoutType.displayName), 尺寸: \(itemSize)")
            })
        }
        
        print("📐 正在更新CollectionView布局 - 类型: \(layoutType.displayName), 尺寸: \(itemSize)")
    }

    /// 使用动画更新CollectionView布局（手势专用）
    /// - Parameter layoutType: 布局类型
    private func updateCollectionViewLayoutWithAnimation(for layoutType: LibrarySettings.LayoutType) {
        let startTime = CFAbsoluteTimeGetCurrent()
        let startTimestamp = String(format: "%.3f", startTime)

        print("🎬 [⏰\(startTimestamp)] 开始动画更新CollectionView布局 - 类型: \(layoutType.displayName)")

        let itemSize = calculateItemSize(for: layoutType)
        let oldItemSize = collectionViewLayout.itemSize

        // 🔧 修复：使用updateThumbnailSize方法，确保缓存清理被触发
        updateThumbnailSize()

        // 使用UIView.animate实现平滑的布局切换动画
        UIView.animate(
            withDuration: 0.3,  // 动画持续时间
            delay: 0,
            options: [.curveEaseInOut, .allowUserInteraction, .beginFromCurrentState],
            animations: {
                // 更新布局参数
                self.collectionViewLayout.itemSize = itemSize

                // 执行布局更新
                self.collectionView.performBatchUpdates({
                    self.collectionView.collectionViewLayout.invalidateLayout()
                }, completion: nil)

                // 强制立即应用布局变化
                self.collectionView.layoutIfNeeded()
            },
            completion: { finished in
                let endTime = CFAbsoluteTimeGetCurrent()
                let duration = endTime - startTime
                let endTimestamp = String(format: "%.3f", endTime)

                print("🎬 [⏰\(endTimestamp)] 动画布局更新完成 - 类型: \(layoutType.displayName), 尺寸: \(oldItemSize) → \(itemSize), 耗时: \(String(format: "%.3f", duration * 1000))ms, 成功: \(finished)")
            }
        )
    }
    
    /// 立即更新CollectionView布局（手势专用 - 同步执行）
    /// - Parameter layoutType: 布局类型
    private func updateCollectionViewLayoutImmediately(for layoutType: LibrarySettings.LayoutType) {
        let startTime = CFAbsoluteTimeGetCurrent()
        let startTimestamp = String(format: "%.3f", startTime)
        
        print("🚀 [⏰\(startTimestamp)] 开始立即更新CollectionView布局 - 类型: \(layoutType.displayName)")
        
        let itemSize = calculateItemSize(for: layoutType)
        collectionViewLayout.itemSize = itemSize

        // 🔧 修复：使用updateThumbnailSize方法，确保缓存清理被触发
        updateThumbnailSize()
        
        let invalidateStart = CFAbsoluteTimeGetCurrent()
        // 🚀 超级优化：完全同步的布局更新，避免任何异步延迟
        collectionViewLayout.invalidateLayout()
        let invalidateDuration = CFAbsoluteTimeGetCurrent() - invalidateStart
        print("🚀 [⏰\(String(format: "%.3f", CFAbsoluteTimeGetCurrent()))] invalidateLayout完成，耗时: \(String(format: "%.3f", invalidateDuration * 1000))ms")
        
        let layoutStart = CFAbsoluteTimeGetCurrent()
        // 直接调用layoutIfNeeded强制立即重新布局
        collectionView.layoutIfNeeded()
        let layoutDuration = CFAbsoluteTimeGetCurrent() - layoutStart
        print("🚀 [⏰\(String(format: "%.3f", CFAbsoluteTimeGetCurrent()))] layoutIfNeeded完成，耗时: \(String(format: "%.3f", layoutDuration * 1000))ms")
        
        let totalDuration = CFAbsoluteTimeGetCurrent() - startTime
        print("🚀 [⏰\(String(format: "%.3f", CFAbsoluteTimeGetCurrent()))] 立即更新CollectionView布局完成 - 类型: \(layoutType.displayName), 尺寸: \(itemSize), 总耗时: \(String(format: "%.3f", totalDuration * 1000))ms")
    }
    
    /// 计算指定布局类型的CollectionView item大小
    /// - Parameter layoutType: 布局类型
    /// - Returns: item的CGSize
    private func calculateItemSize(for layoutType: LibrarySettings.LayoutType) -> CGSize {
        let screenWidth = UIScreen.main.bounds.width
        let columnsPerRow = layoutType.columnsPerRow
        let spacing: CGFloat = 2 // 使用固定间距，与collectionViewLayout的设置一致
        
        let totalSpacing = spacing * CGFloat(columnsPerRow + 1)
        let itemWidth = (screenWidth - totalSpacing) / CGFloat(columnsPerRow)
        
        // 返回正方形的item size，用于CollectionView布局
        return CGSize(width: itemWidth, height: itemWidth)
    }
    
    /// 执行动画布局过渡
    /// - Parameters:
    ///   - fromLayout: 源布局类型
    ///   - toLayout: 目标布局类型
    ///   - savedPosition: 保存的滚动位置
    ///   - completion: 完成回调
    private func performAnimatedLayoutTransition(
        from fromLayout: LibrarySettings.LayoutType,
        to toLayout: LibrarySettings.LayoutType,
        savedPosition: ScrollPosition?,
        completion: @escaping (Bool) -> Void
    ) {
        // 计算优化的动画参数
        let animationDuration: TimeInterval = 0.25  // 缩短动画时间，提供更即时的反馈
        let damping: CGFloat = 0.75  // 降低阻尼，增加一点弹性感
        let initialVelocity: CGFloat = 0.2  // 增加初始速度，让动画更有活力
        
        // 根据布局变化类型选择合适的动画
        let animationOptions: UIView.AnimationOptions = [
            .curveEaseInOut,
            .allowUserInteraction,
            .beginFromCurrentState
        ]
        
        // 添加触觉反馈
        let feedbackGenerator = UIImpactFeedbackGenerator(style: .medium)
        feedbackGenerator.impactOccurred()
        
        print("🎬 开始动画布局过渡: \(fromLayout.displayName) → \(toLayout.displayName)")
        print("   - 动画时长: \(animationDuration)s")
        print("   - 阻尼系数: \(damping)")
        
        // 使用弹簧动画
        UIView.animate(
            withDuration: animationDuration,
            delay: 0,
            usingSpringWithDamping: damping,
            initialSpringVelocity: initialVelocity,
            options: animationOptions,
            animations: {
                // 执行布局更新
                self.collectionView.performBatchUpdates({
                    self.collectionView.collectionViewLayout.invalidateLayout()
                }, completion: nil)
            },
            completion: { finished in
                print("🎬 动画布局过渡完成: \(finished ? "成功" : "中断")")
                
                // 恢复滚动位置
                self.restoreScrollPositionAfterTransition(
                    savedPosition: savedPosition,
                    animated: false
                )
                
                // 完成回调
                completion(finished)
            }
        )
    }
    
    /// 执行即时布局过渡（用于手势实时更新）
    /// - Parameters:
    ///   - savedPosition: 保存的滚动位置
    ///   - completion: 完成回调
    private func performImmediateLayoutTransition(
        savedPosition: ScrollPosition?,
        completion: @escaping (Bool) -> Void
    ) {
        // 直接进行布局切换
        collectionView.performBatchUpdates({
            collectionView.collectionViewLayout.invalidateLayout()
        }, completion: { finished in
            // 恢复滚动位置
            self.restoreScrollPositionAfterTransition(
                savedPosition: savedPosition,
                animated: false
            )
            
            completion(finished)
        })
    }
    
    /// 在过渡后恢复滚动位置
    /// - Parameters:
    ///   - savedPosition: 保存的滚动位置
    ///   - animated: 是否使用动画
    private func restoreScrollPositionAfterTransition(savedPosition: ScrollPosition?, animated: Bool) {
        guard let savedPosition = savedPosition else {
            print("⚠️ 没有保存的滚动位置，跳过恢复")
            return
        }
        
        // 等待布局更新完成后再恢复位置
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 使用现有的位置恢复方法
            self.restoreScrollPosition(savedPosition, animated: animated)
        }
    }
}



// MARK: - 工具扩展

extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}
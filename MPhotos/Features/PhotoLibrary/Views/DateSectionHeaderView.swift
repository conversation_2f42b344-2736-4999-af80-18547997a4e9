//
//  DateSectionHeaderView.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import UIKit

/// 日期分组头部视图 - 用于显示日期分组的标题
class DateSectionHeaderView: UICollectionReusableView {
    
    // MARK: - 标识符
    static let identifier = "DateSectionHeaderView"
    
    // MARK: - UI组件
    
    /// 日期标题标签
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        label.textColor = .label
        label.textAlignment = .left
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    /// 照片数量标签
    private let countLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = .secondaryLabel
        label.textAlignment = .right
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    /// 分隔线
    private let separatorView: UIView = {
        let view = UIView()
        view.backgroundColor = .separator
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupConstraints()
    }
    
    // MARK: - 生命周期
    
    override func prepareForReuse() {
        super.prepareForReuse()
        titleLabel.text = nil
        countLabel.text = nil
    }
    
    // MARK: - UI设置
    
    /// 设置UI组件
    private func setupUI() {
        backgroundColor = .systemBackground
        
        // 添加子视图
        addSubview(titleLabel)
        addSubview(countLabel)
        addSubview(separatorView)
    }
    
    /// 设置约束
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 标题标签
            titleLabel.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 16),
            titleLabel.centerYAnchor.constraint(equalTo: centerYAnchor),
            titleLabel.trailingAnchor.constraint(lessThanOrEqualTo: countLabel.leadingAnchor, constant: -8),
            
            // 数量标签
            countLabel.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -16),
            countLabel.centerYAnchor.constraint(equalTo: centerYAnchor),
            countLabel.widthAnchor.constraint(greaterThanOrEqualToConstant: 50),
            
            // 分隔线
            separatorView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 16),
            separatorView.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -16),
            separatorView.bottomAnchor.constraint(equalTo: bottomAnchor),
            separatorView.heightAnchor.constraint(equalToConstant: 0.5)
        ])
    }
    
    // MARK: - 配置方法
    
    /// 配置头部视图
    /// - Parameters:
    ///   - date: 日期
    ///   - photoCount: 照片数量
    func configure(with date: Date, photoCount: Int) {
        titleLabel.text = formatDate(date)
        countLabel.text = "\(photoCount)"
        
        print("📅 配置日期头部: \(formatDate(date)), 照片数量: \(photoCount)")
    }
    
    /// 配置头部视图（使用字符串）
    /// - Parameters:
    ///   - title: 标题
    ///   - photoCount: 照片数量
    func configure(with title: String, photoCount: Int) {
        titleLabel.text = title
        countLabel.text = "\(photoCount)"
        
        print("📅 配置自定义头部: \(title), 照片数量: \(photoCount)")
    }
    
    // MARK: - 辅助方法
    
    /// 格式化日期显示
    /// - Parameter date: 日期
    /// - Returns: 格式化后的日期字符串
    private func formatDate(_ date: Date) -> String {
        let calendar = Calendar.current
        let now = Date()
        
        // 判断是否为今天
        if calendar.isDateInToday(date) {
            return "今天"
        }
        
        // 判断是否为昨天
        if calendar.isDateInYesterday(date) {
            return "昨天"
        }
        
        // 判断是否为本周
        if calendar.isDate(date, equalTo: now, toGranularity: .weekOfYear) {
            let weekdayFormatter = DateFormatter()
            weekdayFormatter.locale = Locale(identifier: "zh_CN")
            weekdayFormatter.dateFormat = "EEEE"
            return weekdayFormatter.string(from: date)
        }
        
        // 判断是否为今年
        if calendar.isDate(date, equalTo: now, toGranularity: .year) {
            let formatter = DateFormatter()
            formatter.locale = Locale(identifier: "zh_CN")
            formatter.dateFormat = "M月d日"
            return formatter.string(from: date)
        }
        
        // 其他年份
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "yyyy年M月d日"
        return formatter.string(from: date)
    }
} 
//
//  ScrollPosition.swift
//  MPhotos
//
//  Created by MPhotos Team on 2024/12/13.
//

import Foundation
import UIKit

/// 滚动位置信息结构体 - 用于保存和恢复CollectionView的滚动位置
/// 
/// 这个结构体包含了恢复滚动位置所需的所有信息：
/// - centerIndexPath: 中心可见照片的索引路径
/// - centerOffset: 中心照片相对于可见区域中心的偏移量
/// - contentOffset: 当前内容偏移量（作为备选方案）
/// - visibleRect: 当前可见区域的矩形
/// - layoutType: 保存位置时的布局类型
struct ScrollPosition {
    /// 中心可见照片的索引路径
    let centerIndexPath: IndexPath
    
    /// 中心照片相对于可见区域中心的偏移量
    let centerOffset: CGPoint
    
    /// 当前内容偏移量（作为备选恢复方案）
    let contentOffset: CGPoint
    
    /// 当前可见区域的矩形
    let visibleRect: CGRect
    
    /// 保存位置时的布局类型
    let layoutType: LibrarySettings.LayoutType
    
    /// 保存时间戳（用于调试和验证）
    let timestamp: Date
    
    // MARK: - 初始化方法
    
    /// 创建ScrollPosition实例
    /// - Parameters:
    ///   - centerIndexPath: 中心可见照片的索引路径
    ///   - centerOffset: 中心照片的偏移量
    ///   - contentOffset: 当前内容偏移量
    ///   - visibleRect: 可见区域矩形
    ///   - layoutType: 当前布局类型
    init(centerIndexPath: IndexPath, 
         centerOffset: CGPoint, 
         contentOffset: CGPoint, 
         visibleRect: CGRect, 
         layoutType: LibrarySettings.LayoutType) {
        self.centerIndexPath = centerIndexPath
        self.centerOffset = centerOffset
        self.contentOffset = contentOffset
        self.visibleRect = visibleRect
        self.layoutType = layoutType
        self.timestamp = Date()
    }
    
    // MARK: - 计算方法
    
    /// 计算在新布局中的目标滚动位置
    /// - Parameters:
    ///   - newLayoutType: 新的布局类型
    ///   - collectionView: CollectionView实例
    ///   - photos: 照片数组
    /// - Returns: 新布局中的目标ContentOffset，如果计算失败返回nil
    func calculateTargetContentOffset(
        for newLayoutType: LibrarySettings.LayoutType, 
        in collectionView: UICollectionView, 
        photos: [PhotoModel]
    ) -> CGPoint? {
        
        // 安全检查：确保照片数组不为空
        guard !photos.isEmpty else {
            print("⚠️ ScrollPosition: 照片数组为空，无法计算目标位置")
            return nil
        }
        
        // 安全检查：确保索引有效
        guard centerIndexPath.item >= 0 && centerIndexPath.item < photos.count else {
            print("⚠️ ScrollPosition: 索引越界 \(centerIndexPath.item)，照片总数 \(photos.count)")
            return nil
        }
        
        // 强制布局更新，确保布局信息是最新的
        collectionView.layoutIfNeeded()
        
        // 获取目标照片在新布局中的frame
        let targetCellFrame = collectionView.layoutAttributesForItem(at: centerIndexPath)?.frame
        
        guard let cellFrame = targetCellFrame else {
            print("⚠️ ScrollPosition: 无法获取索引 \(centerIndexPath.item) 的cell frame")
            return nil
        }
        
        // 计算目标位置：让中心照片保持在可见区域的中心
        let collectionViewCenter = CGPoint(
            x: collectionView.bounds.width / 2,
            y: collectionView.bounds.height / 2
        )

        // 计算目标contentOffset
        let targetContentOffset = CGPoint(
            x: cellFrame.midX - collectionViewCenter.x - centerOffset.x,
            y: cellFrame.midY - collectionViewCenter.y - centerOffset.y
        )
        
        // 确保目标位置在有效范围内
        let clampedOffset = clampContentOffset(targetContentOffset, in: collectionView)
        
        print("📍 ScrollPosition: 计算目标位置")
        print("   - 中心照片索引: \(centerIndexPath.item)")
        print("   - 目标cell frame: \(cellFrame)")
        print("   - 目标cell中心: \(CGPoint(x: cellFrame.midX, y: cellFrame.midY))")
        print("   - 原始偏移: \(centerOffset)")
        print("   - 计算的目标offset: \(targetContentOffset)")
        print("   - CollectionView bounds: \(collectionView.bounds)")
        print("   - CollectionView contentSize: \(collectionView.contentSize)")
        print("   - 限制后的offset: \(clampedOffset)")
        
        return clampedOffset
    }
    
    /// 限制ContentOffset在有效范围内
    /// - Parameters:
    ///   - contentOffset: 要限制的ContentOffset
    ///   - collectionView: CollectionView实例
    /// - Returns: 限制后的ContentOffset
    private func clampContentOffset(_ contentOffset: CGPoint, in collectionView: UICollectionView) -> CGPoint {
        let contentSize = collectionView.contentSize
        let bounds = collectionView.bounds
        
        // 计算最大可滚动范围
        let maxOffsetX = max(0, contentSize.width - bounds.width)
        let maxOffsetY = max(0, contentSize.height - bounds.height)
        
        // 限制在有效范围内
        let clampedX = max(0, min(contentOffset.x, maxOffsetX))
        let clampedY = max(0, min(contentOffset.y, maxOffsetY))
        
        return CGPoint(x: clampedX, y: clampedY)
    }
    
    /// 获取备选的滚动位置（基于相对位置比例）
    /// - Parameters:
    ///   - collectionView: CollectionView实例
    /// - Returns: 备选的ContentOffset
    func getFallbackContentOffset(in collectionView: UICollectionView) -> CGPoint {
        let contentSize = collectionView.contentSize
        let _ = collectionView.bounds
        
        // 计算原始位置的相对比例
        let relativeX = contentSize.width > 0 ? contentOffset.x / contentSize.width : 0
        let relativeY = contentSize.height > 0 ? contentOffset.y / contentSize.height : 0
        
        // 根据新的内容大小计算目标位置
        let newContentSize = collectionView.contentSize
        let targetX = newContentSize.width * relativeX
        let targetY = newContentSize.height * relativeY
        
        let fallbackOffset = CGPoint(x: targetX, y: targetY)
        
        // 限制在有效范围内
        return clampContentOffset(fallbackOffset, in: collectionView)
    }
    
    /// 获取基于行列位置的滚动位置（第三种算法）
    /// - Parameters:
    ///   - newLayoutType: 新的布局类型
    ///   - collectionView: CollectionView实例
    ///   - photos: 照片数组
    /// - Returns: 基于行列位置的ContentOffset
    func getRowColumnBasedContentOffset(
        for newLayoutType: LibrarySettings.LayoutType,
        in collectionView: UICollectionView,
        photos: [PhotoModel]
    ) -> CGPoint? {
        
        // 安全检查
        guard !photos.isEmpty && centerIndexPath.item >= 0 && centerIndexPath.item < photos.count else {
            return nil
        }
        
        // 计算原始布局的行列位置
        let originalColumns = layoutType.columnsPerRow
        let originalRow = centerIndexPath.item / originalColumns
        let originalColumn = centerIndexPath.item % originalColumns
        
        // 计算在新布局中的大致位置
        let newColumns = newLayoutType.columnsPerRow
        let newIndex = originalRow * newColumns + min(originalColumn, newColumns - 1)
        
        // 确保新索引在有效范围内
        let clampedIndex = min(newIndex, photos.count - 1)
        let newIndexPath = IndexPath(item: clampedIndex, section: 0)
        
        // 获取新位置的frame
        guard let cellFrame = collectionView.layoutAttributesForItem(at: newIndexPath)?.frame else {
            return nil
        }
        
        // 计算目标位置，让该照片显示在可见区域中
        let targetY = cellFrame.midY - collectionView.bounds.height / 2
        let clampedY = max(0, min(targetY, collectionView.contentSize.height - collectionView.bounds.height))
        
        print("📐 ScrollPosition: 基于行列位置计算")
        print("   - 原始位置: 行\(originalRow), 列\(originalColumn) (索引\(centerIndexPath.item))")
        print("   - 新位置: 索引\(clampedIndex)")
        print("   - 目标Y偏移: \(clampedY)")
        
        return CGPoint(x: 0, y: clampedY)
    }
    
    /// 智能位置计算：尝试多种算法，返回最佳结果
    /// - Parameters:
    ///   - newLayoutType: 新的布局类型
    ///   - collectionView: CollectionView实例
    ///   - photos: 照片数组
    /// - Returns: 最佳的ContentOffset
    func getOptimalContentOffset(
        for newLayoutType: LibrarySettings.LayoutType,
        in collectionView: UICollectionView,
        photos: [PhotoModel]
    ) -> CGPoint {
        
        // 算法1：基于中心照片的精确位置计算
        if let primaryOffset = calculateTargetContentOffset(for: newLayoutType, in: collectionView, photos: photos) {
            print("🎯 ScrollPosition: 使用主要算法（中心照片定位）")
            return primaryOffset
        }
        
        // 算法2：基于行列位置的计算
        if let rowColumnOffset = getRowColumnBasedContentOffset(for: newLayoutType, in: collectionView, photos: photos) {
            print("📐 ScrollPosition: 使用行列算法")
            return rowColumnOffset
        }
        
        // 算法3：基于相对位置比例
        let fallbackOffset = getFallbackContentOffset(in: collectionView)
        print("📊 ScrollPosition: 使用比例算法")
        return fallbackOffset
    }
    
    // MARK: - 调试信息
    
    /// 获取详细的调试信息
    var debugDescription: String {
        return """
        ScrollPosition调试信息:
        - 中心照片索引: \(centerIndexPath.item)
        - 中心偏移: \(centerOffset)
        - 内容偏移: \(contentOffset)
        - 可见区域: \(visibleRect)
        - 布局类型: \(layoutType.displayName)
        - 保存时间: \(timestamp)
        """
    }
    
    /// 验证位置信息是否有效
    var isValid: Bool {
        // 检查索引是否有效
        guard centerIndexPath.item >= 0 else { 
            print("⚠️ ScrollPosition无效：索引为负数 \(centerIndexPath.item)")
            return false 
        }
        
        // 检查可见区域是否有效
        guard visibleRect.width > 0 && visibleRect.height > 0 else { 
            print("⚠️ ScrollPosition无效：可见区域尺寸无效 \(visibleRect)")
            return false 
        }
        
        // 检查偏移量是否在合理范围内
        guard abs(centerOffset.x) <= visibleRect.width && abs(centerOffset.y) <= visibleRect.height else { 
            print("⚠️ ScrollPosition无效：偏移量超出范围 offset:\(centerOffset) rect:\(visibleRect)")
            return false 
        }
        
        print("✅ ScrollPosition验证通过")
        return true
    }
    
    /// 获取位置精度评估
    /// - Parameters:
    ///   - collectionView: CollectionView实例
    ///   - photos: 照片数组
    /// - Returns: 精度评估结果
    func getAccuracyAssessment(in collectionView: UICollectionView, photos: [PhotoModel]) -> (score: Double, description: String) {
        guard !photos.isEmpty && centerIndexPath.item < photos.count else {
            return (0.0, "无法评估：数据无效")
        }
        
        let currentVisibleIndexPaths = collectionView.indexPathsForVisibleItems
        let targetIndex = centerIndexPath.item
        
        // 评估1：目标照片是否可见 (40分)
        let isTargetVisible = currentVisibleIndexPaths.contains { $0.item == targetIndex }
        var score = isTargetVisible ? 40.0 : 0.0
        
        // 评估2：目标照片的位置精度 (40分)
        if isTargetVisible, let cellFrame = collectionView.layoutAttributesForItem(at: centerIndexPath)?.frame {
            let collectionCenter = CGPoint(x: collectionView.bounds.midX, y: collectionView.bounds.midY)
            let cellCenter = CGPoint(x: cellFrame.midX, y: cellFrame.midY)
            let distance = sqrt(pow(cellCenter.x - collectionCenter.x, 2) + pow(cellCenter.y - collectionCenter.y, 2))
            
            // 距离越小，精度越高
            let maxDistance = max(collectionView.bounds.width, collectionView.bounds.height)
            let accuracy = max(0, 1 - (distance / maxDistance))
            score += accuracy * 40.0
        }
        
        // 评估3：周围照片的覆盖率 (20分)
        let visibleRange = currentVisibleIndexPaths.map { $0.item }
        let expectedRange = max(0, targetIndex - 5)...min(photos.count - 1, targetIndex + 5)
        let overlap = Set(visibleRange).intersection(Set(expectedRange))
        let coverageRatio = Double(overlap.count) / Double(expectedRange.count)
        score += coverageRatio * 20.0
        
        // 生成描述
        let description: String
        switch score {
        case 90...100:
            description = "优秀：位置恢复非常精确"
        case 70..<90:
            description = "良好：位置恢复基本准确"
        case 50..<70:
            description = "一般：位置恢复有偏差"
        case 20..<50:
            description = "较差：位置恢复偏差较大"
        default:
            description = "失败：位置恢复失败"
        }
        
        return (score, description)
    }
}

// MARK: - 扩展方法

extension ScrollPosition {
    /// 创建一个表示顶部位置的ScrollPosition
    /// - Parameters:
    ///   - layoutType: 布局类型
    ///   - collectionView: CollectionView实例
    /// - Returns: 顶部位置的ScrollPosition
    static func topPosition(for layoutType: LibrarySettings.LayoutType, in collectionView: UICollectionView) -> ScrollPosition {
        return ScrollPosition(
            centerIndexPath: IndexPath(item: 0, section: 0),
            centerOffset: CGPoint.zero,
            contentOffset: CGPoint.zero,
            visibleRect: collectionView.bounds,
            layoutType: layoutType
        )
    }
    
    /// 创建一个表示底部位置的ScrollPosition
    /// - Parameters:
    ///   - layoutType: 布局类型
    ///   - collectionView: CollectionView实例
    ///   - photosCount: 照片总数
    /// - Returns: 底部位置的ScrollPosition
    static func bottomPosition(for layoutType: LibrarySettings.LayoutType, in collectionView: UICollectionView, photosCount: Int) -> ScrollPosition {
        let lastIndex = max(0, photosCount - 1)
        let bottomOffset = CGPoint(x: 0, y: collectionView.contentSize.height - collectionView.bounds.height)
        
        return ScrollPosition(
            centerIndexPath: IndexPath(item: lastIndex, section: 0),
            centerOffset: CGPoint.zero,
            contentOffset: bottomOffset,
            visibleRect: collectionView.bounds,
            layoutType: layoutType
        )
    }
} 
//
//  LayoutTransition.swift
//  MPhotos
//
//  Created by MPhotos Team on 2024/12/13.
//

import Foundation
import UIKit

/// 布局过渡状态枚举
enum LayoutTransitionState {
    case idle           // 空闲状态
    case preparing      // 准备过渡
    case transitioning  // 过渡中
    case completing     // 完成过渡
    case cancelled      // 过渡取消
}

/// 布局过渡类型枚举
enum LayoutTransitionType {
    case gestureTriggered    // 手势触发的过渡
    case programmatic        // 程序触发的过渡
    case settings            // 设置变更触发的过渡
}

/// 布局过渡数据结构
struct LayoutTransitionInfo {
    /// 源布局类型
    let fromLayout: LibrarySettings.LayoutType
    
    /// 目标布局类型
    let toLayout: LibrarySettings.LayoutType
    
    /// 过渡类型
    let transitionType: LayoutTransitionType
    
    /// 过渡开始时间
    let startTime: Date
    
    /// 过渡触发的缩放值（仅手势触发时有效）
    let triggerScale: CGFloat?
    
    /// 保存的滚动位置
    let savedScrollPosition: ScrollPosition?
    
    /// 是否使用动画
    let animated: Bool
    
    /// 动画持续时间
    let animationDuration: TimeInterval
    
    init(fromLayout: LibrarySettings.LayoutType,
         toLayout: LibrarySettings.LayoutType,
         transitionType: LayoutTransitionType,
         triggerScale: CGFloat? = nil,
         savedScrollPosition: ScrollPosition? = nil,
         animated: Bool = true,
         animationDuration: TimeInterval = 0.3) {
        
        self.fromLayout = fromLayout
        self.toLayout = toLayout
        self.transitionType = transitionType
        self.startTime = Date()
        self.triggerScale = triggerScale
        self.savedScrollPosition = savedScrollPosition
        self.animated = animated
        self.animationDuration = animationDuration
    }
    
    /// 获取过渡方向
    var transitionDirection: String {
        let fromColumns = fromLayout.columnsPerRow
        let toColumns = toLayout.columnsPerRow
        
        if fromColumns < toColumns {
            return "放大 (\(fromColumns)→\(toColumns)列)"
        } else if fromColumns > toColumns {
            return "缩小 (\(fromColumns)→\(toColumns)列)"
        } else {
            return "无变化 (\(fromColumns)列)"
        }
    }
    
    /// 检查是否为无效过渡
    var isNoOpTransition: Bool {
        return fromLayout == toLayout
    }
}

/// 布局过渡管理器
class LayoutTransition {
    
    // MARK: - 属性
    
    /// 当前过渡状态
    private(set) var currentState: LayoutTransitionState = .idle
    
    /// 当前过渡信息
    private(set) var currentTransition: LayoutTransitionInfo?
    
    /// 过渡历史记录（最多保留10条）
    private var transitionHistory: [LayoutTransitionInfo] = []
    
    /// 过渡委托
    weak var delegate: LayoutTransitionDelegate?
    
    /// 是否启用详细日志
    var isVerboseLoggingEnabled: Bool = true
    
    // MARK: - 初始化
    
    init() {
        print("✅ LayoutTransition 初始化完成")
    }
    
    deinit {
        print("🗑️ LayoutTransition 已释放")
    }
    
    // MARK: - 公共方法
    
    /// 获取当前过渡类型
    /// - Returns: 当前过渡类型，如果没有活跃过渡则返回nil
    func getCurrentTransitionType() -> LayoutTransitionType? {
        return currentTransition?.transitionType
    }
    
    /// 开始布局过渡
    /// - Parameters:
    ///   - fromLayout: 源布局类型
    ///   - toLayout: 目标布局类型
    ///   - transitionType: 过渡类型
    ///   - triggerScale: 触发缩放值（可选）
    ///   - savedPosition: 保存的滚动位置（可选）
    ///   - animated: 是否使用动画
    ///   - completion: 完成回调
    /// - Returns: 是否成功开始过渡
    @discardableResult
    func beginTransition(
        from fromLayout: LibrarySettings.LayoutType,
        to toLayout: LibrarySettings.LayoutType,
        type transitionType: LayoutTransitionType,
        triggerScale: CGFloat? = nil,
        savedPosition: ScrollPosition? = nil,
        animated: Bool = true,
        completion: ((Bool) -> Void)? = nil
    ) -> Bool {
        
        // 检查当前状态
        guard currentState == .idle else {
            logWarning("过渡已在进行中，当前状态: \(currentState)")
            completion?(false)
            return false
        }
        
        // 创建过渡信息
        let transitionInfo = LayoutTransitionInfo(
            fromLayout: fromLayout,
            toLayout: toLayout,
            transitionType: transitionType,
            triggerScale: triggerScale,
            savedScrollPosition: savedPosition,
            animated: animated
        )
        
        // 检查是否为无效过渡
        if transitionInfo.isNoOpTransition {
            logInfo("跳过无效过渡：\(fromLayout.displayName) → \(toLayout.displayName)")
            completion?(true)
            return true
        }
        
        // 更新状态
        currentState = .preparing
        currentTransition = transitionInfo
        
        logInfo("🔄 开始布局过渡: \(transitionInfo.transitionDirection)")
        logInfo("   - 过渡类型: \(transitionType)")
        if let scale = triggerScale {
            logInfo("   - 触发缩放: \(String(format: "%.2f", scale))")
        }
        logInfo("   - 使用动画: \(animated)")
        
        // 通知委托开始过渡
        delegate?.layoutTransition(self, willBeginTransition: transitionInfo)
        
        // 更新状态为过渡中
        currentState = .transitioning
        
        // 通知委托执行过渡
        delegate?.layoutTransition(self, performTransition: transitionInfo) { [weak self] success in
            guard let self = self else { return }
            
            if success {
                self.completeTransition()
            } else {
                self.cancelTransition()
            }
            
            completion?(success)
        }
        
        return true
    }
    
    /// 完成当前过渡
    private func completeTransition() {
        guard let transition = currentTransition else { return }
        
        currentState = .completing
        
        // 添加到历史记录
        addToHistory(transition)
        
        // 计算过渡耗时
        let duration = Date().timeIntervalSince(transition.startTime)
        
        logInfo("✅ 布局过渡完成: \(transition.transitionDirection)")
        logInfo("   - 耗时: \(String(format: "%.3f", duration))秒")
        
        // 通知委托完成过渡
        delegate?.layoutTransition(self, didCompleteTransition: transition)
        
        // 重置状态
        currentState = .idle
        currentTransition = nil
    }
    
    /// 取消当前过渡
    private func cancelTransition() {
        guard let transition = currentTransition else { return }
        
        currentState = .cancelled
        
        logWarning("❌ 布局过渡取消: \(transition.transitionDirection)")
        
        // 通知委托取消过渡
        delegate?.layoutTransition(self, didCancelTransition: transition)
        
        // 重置状态
        currentState = .idle
        currentTransition = nil
    }
    
    /// 强制重置过渡状态
    func reset() {
        if currentState != .idle {
            logWarning("🔄 强制重置过渡状态，当前状态: \(currentState)")
        }
        
        currentState = .idle
        currentTransition = nil
    }
    
    // MARK: - 历史记录管理
    
    /// 添加过渡记录到历史
    private func addToHistory(_ transition: LayoutTransitionInfo) {
        transitionHistory.append(transition)
        
        // 限制历史记录数量
        if transitionHistory.count > 10 {
            transitionHistory.removeFirst()
        }
    }
    
    /// 获取过渡历史记录
    func getTransitionHistory() -> [LayoutTransitionInfo] {
        return transitionHistory
    }
    
    /// 获取过渡统计信息
    func getTransitionStatistics() -> String {
        let totalCount = transitionHistory.count
        let gestureCount = transitionHistory.filter { $0.transitionType == .gestureTriggered }.count
        let programmaticCount = transitionHistory.filter { $0.transitionType == .programmatic }.count
        let settingsCount = transitionHistory.filter { $0.transitionType == .settings }.count
        
        var stats = ["=== 布局过渡统计 ==="]
        stats.append("总过渡次数: \(totalCount)")
        stats.append("手势触发: \(gestureCount)")
        stats.append("程序触发: \(programmaticCount)")
        stats.append("设置触发: \(settingsCount)")
        stats.append("当前状态: \(currentState)")
        
        if let current = currentTransition {
            stats.append("当前过渡: \(current.transitionDirection)")
        }
        
        return stats.joined(separator: "\n")
    }
    
    // MARK: - 日志方法
    
    private func logInfo(_ message: String) {
        if isVerboseLoggingEnabled {
            print("🔄 [LayoutTransition] \(message)")
        }
    }
    
    private func logWarning(_ message: String) {
        print("⚠️ [LayoutTransition] \(message)")
    }
}

// MARK: - LayoutTransitionDelegate

/// 布局过渡委托协议
protocol LayoutTransitionDelegate: AnyObject {
    
    /// 即将开始布局过渡
    /// - Parameters:
    ///   - transition: 过渡管理器
    ///   - transitionInfo: 过渡信息
    func layoutTransition(_ transition: LayoutTransition, willBeginTransition transitionInfo: LayoutTransitionInfo)
    
    /// 执行布局过渡
    /// - Parameters:
    ///   - transition: 过渡管理器
    ///   - transitionInfo: 过渡信息
    ///   - completion: 完成回调
    func layoutTransition(_ transition: LayoutTransition, performTransition transitionInfo: LayoutTransitionInfo, completion: @escaping (Bool) -> Void)
    
    /// 布局过渡完成
    /// - Parameters:
    ///   - transition: 过渡管理器
    ///   - transitionInfo: 过渡信息
    func layoutTransition(_ transition: LayoutTransition, didCompleteTransition transitionInfo: LayoutTransitionInfo)
    
    /// 布局过渡取消
    /// - Parameters:
    ///   - transition: 过渡管理器
    ///   - transitionInfo: 过渡信息
    func layoutTransition(_ transition: LayoutTransition, didCancelTransition transitionInfo: LayoutTransitionInfo)
}

// MARK: - 扩展方法

extension LayoutTransition {
    
    /// 检查是否可以开始新的过渡
    var canBeginTransition: Bool {
        return currentState == .idle
    }
    
    /// 检查当前是否正在过渡中
    var isTransitioning: Bool {
        return currentState == .transitioning || currentState == .preparing
    }
    
    /// 获取当前过渡进度描述
    var currentTransitionDescription: String? {
        guard let transition = currentTransition else { return nil }
        
        let elapsed = Date().timeIntervalSince(transition.startTime)
        return "过渡中: \(transition.transitionDirection) (已用时 \(String(format: "%.1f", elapsed))s)"
    }
} 
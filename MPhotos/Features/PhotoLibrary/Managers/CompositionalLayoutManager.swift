import UIKit

/// 响应式网格布局管理器
/// 使用 UICollectionViewCompositionalLayout 实现完全模拟苹果原生 Photos App 的网格布局
class CompositionalLayoutManager {
    
    // MARK: - 单例
    static let shared = CompositionalLayoutManager()
    private init() {}
    
    // MARK: - 响应式断点配置
    
    /// 响应式布局断点配置
    /// 完全模拟原生 Photos App 的响应式行为
    private struct ResponsiveBreakpoints {
        static let small: CGFloat = 400      // < 400pt: 3列 (iPhone SE 竖屏)
        static let medium: CGFloat = 600     // 400-600pt: 5列 (普通 iPhone)
        static let large: CGFloat = 800      // 600-800pt: 7列 (横屏 iPhone/小 iPad)
        // > 800pt: 9列 (iPad 横屏等大屏设备)
    }
    
    /// 根据容器宽度确定列数
    /// - Parameter containerWidth: 容器宽度
    /// - Returns: 对应的列数
    private func determineColumnCount(for containerWidth: CGFloat) -> Int {
        switch containerWidth {
        case 0..<ResponsiveBreakpoints.small:
            return 3
        case ResponsiveBreakpoints.small..<ResponsiveBreakpoints.medium:
            return 5
        case ResponsiveBreakpoints.medium..<ResponsiveBreakpoints.large:
            return 7
        default:
            return 9
        }
    }
    
    // MARK: - 布局创建
    
    /// 创建响应式网格布局
    /// - Returns: 配置好的 UICollectionViewCompositionalLayout
    func createResponsiveGridLayout() -> UICollectionViewCompositionalLayout {
        let layout = UICollectionViewCompositionalLayout { [weak self] (sectionIndex, layoutEnvironment) -> NSCollectionLayoutSection? in
            return self?.createGridSection(layoutEnvironment: layoutEnvironment)
        }
        
        return layout
    }
    
    /// 创建网格区域布局
    /// - Parameter layoutEnvironment: 布局环境
    /// - Returns: 配置好的 NSCollectionLayoutSection
    private func createGridSection(layoutEnvironment: NSCollectionLayoutEnvironment) -> NSCollectionLayoutSection {
        // 获取容器的有效内容尺寸
        let containerWidth = layoutEnvironment.container.effectiveContentSize.width
        
        // 根据容器宽度确定列数
        let columnCount = determineColumnCount(for: containerWidth)
        
        // 计算间距百分比
        // 2pt 间距转换为相对于容器宽度的百分比
        let spacingPoints: CGFloat = 2.0
        let spacingFraction = spacingPoints / containerWidth
        
        // 创建 item
        let itemSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0 / CGFloat(columnCount)),
            heightDimension: .fractionalHeight(1.0)
        )
        
        let item = NSCollectionLayoutItem(layoutSize: itemSize)
        
        // 设置 item 间距（使用百分比）
        item.contentInsets = NSDirectionalEdgeInsets(
            top: spacingFraction,
            leading: spacingFraction / 2,
            bottom: 0,
            trailing: spacingFraction / 2
        )
        
        // 创建 group (一行)
        let groupSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0),
            heightDimension: .fractionalWidth(1.0 / CGFloat(columnCount)) // 正方形高度
        )
        
        let group = NSCollectionLayoutGroup.horizontal(
            layoutSize: groupSize,
            subitem: item,
            count: columnCount
        )
        
        // 创建 section
        let section = NSCollectionLayoutSection(group: group)
        
        // 设置 section 边距（使用百分比）
        section.contentInsets = NSDirectionalEdgeInsets(
            top: spacingFraction,
            leading: spacingFraction,
            bottom: spacingFraction,
            trailing: spacingFraction
        )
        
        return section
    }
    
    // MARK: - 高级布局配置

    /// 创建带自定义间距的响应式网格布局
    /// - Parameter customSpacing: 自定义间距（pt），如果为 nil 则使用默认 2pt
    /// - Returns: 配置好的 UICollectionViewCompositionalLayout
    func createResponsiveGridLayout(customSpacing: CGFloat? = nil) -> UICollectionViewCompositionalLayout {
        let layout = UICollectionViewCompositionalLayout { [weak self] (sectionIndex, layoutEnvironment) -> NSCollectionLayoutSection? in
            return self?.createGridSection(layoutEnvironment: layoutEnvironment, customSpacing: customSpacing)
        }

        return layout
    }

    /// 创建网格区域布局（支持自定义间距）
    /// - Parameters:
    ///   - layoutEnvironment: 布局环境
    ///   - customSpacing: 自定义间距
    /// - Returns: 配置好的 NSCollectionLayoutSection
    private func createGridSection(layoutEnvironment: NSCollectionLayoutEnvironment, customSpacing: CGFloat? = nil) -> NSCollectionLayoutSection {
        // 获取容器的有效内容尺寸
        let containerWidth = layoutEnvironment.container.effectiveContentSize.width

        // 根据容器宽度确定列数
        let columnCount = determineColumnCount(for: containerWidth)

        // 使用自定义间距或默认间距
        let spacingPoints = customSpacing ?? 2.0
        let spacingFraction = spacingPoints / containerWidth

        // 创建 item
        let itemSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0 / CGFloat(columnCount)),
            heightDimension: .fractionalHeight(1.0)
        )

        let item = NSCollectionLayoutItem(layoutSize: itemSize)

        // 设置 item 间距（使用百分比）
        // 左右间距各为总间距的一半，确保相邻 item 间距正确
        item.contentInsets = NSDirectionalEdgeInsets(
            top: spacingFraction,
            leading: spacingFraction / 2,
            bottom: 0,
            trailing: spacingFraction / 2
        )

        // 创建 group (一行)
        let groupSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0),
            heightDimension: .fractionalWidth(1.0 / CGFloat(columnCount)) // 正方形高度
        )

        let group = NSCollectionLayoutGroup.horizontal(
            layoutSize: groupSize,
            subitem: item,
            count: columnCount
        )

        // 创建 section
        let section = NSCollectionLayoutSection(group: group)

        // 设置 section 边距（使用百分比）
        section.contentInsets = NSDirectionalEdgeInsets(
            top: spacingFraction,
            leading: spacingFraction,
            bottom: spacingFraction,
            trailing: spacingFraction
        )

        return section
    }

    // MARK: - 调试和诊断

    /// 获取当前布局信息（用于调试）
    /// - Parameters:
    ///   - containerWidth: 容器宽度
    ///   - customSpacing: 自定义间距
    /// - Returns: 布局信息字符串
    func getLayoutInfo(for containerWidth: CGFloat, customSpacing: CGFloat? = nil) -> String {
        let columnCount = determineColumnCount(for: containerWidth)
        let spacingPoints = customSpacing ?? 2.0
        let spacingFraction = spacingPoints / containerWidth
        let itemWidthFraction = 1.0 / CGFloat(columnCount)
        let actualItemWidth = containerWidth * itemWidthFraction

        return """
        📐 响应式布局信息:
        - 容器宽度: \(String(format: "%.1f", containerWidth))pt
        - 列数: \(columnCount)
        - Item宽度比例: \(String(format: "%.4f", itemWidthFraction)) (\(String(format: "%.1f", itemWidthFraction * 100))%)
        - Item实际宽度: \(String(format: "%.1f", actualItemWidth))pt
        - 间距比例: \(String(format: "%.6f", spacingFraction)) (\(String(format: "%.3f", spacingFraction * 100))%)
        - 实际间距: \(String(format: "%.1f", spacingPoints))pt
        - 布局模式: 纯百分比响应式布局
        """
    }

    /// 验证布局配置的合理性
    /// - Parameters:
    ///   - containerWidth: 容器宽度
    ///   - customSpacing: 自定义间距
    /// - Returns: 验证结果和建议
    func validateLayoutConfiguration(containerWidth: CGFloat, customSpacing: CGFloat? = nil) -> (isValid: Bool, suggestions: [String]) {
        var suggestions: [String] = []
        var isValid = true

        let columnCount = determineColumnCount(for: containerWidth)
        let spacingPoints = customSpacing ?? 2.0
        let spacingFraction = spacingPoints / containerWidth
        let itemWidthFraction = 1.0 / CGFloat(columnCount)
        let actualItemWidth = containerWidth * itemWidthFraction

        // 检查 item 宽度是否过小
        if actualItemWidth < 30 {
            isValid = false
            suggestions.append("Item宽度过小(\(String(format: "%.1f", actualItemWidth))pt)，建议减少列数")
        }

        // 检查间距比例是否合理
        if spacingFraction > 0.05 {
            suggestions.append("间距比例较大(\(String(format: "%.2f", spacingFraction * 100))%)，可能影响显示效果")
        }

        // 检查容器宽度是否有效
        if containerWidth <= 0 {
            isValid = false
            suggestions.append("容器宽度无效，请确保 CollectionView 已正确布局")
        }

        return (isValid: isValid, suggestions: suggestions)
    }
}

// MARK: - 扩展：便捷方法

extension CompositionalLayoutManager {
    
    /// 为指定的 UICollectionView 应用响应式布局
    /// - Parameter collectionView: 目标 CollectionView
    func applyResponsiveLayout(to collectionView: UICollectionView) {
        let layout = createResponsiveGridLayout()
        collectionView.setCollectionViewLayout(layout, animated: false)
    }
    
    /// 获取指定宽度下的列数（供外部查询使用）
    /// - Parameter containerWidth: 容器宽度
    /// - Returns: 对应的列数
    func getColumnCount(for containerWidth: CGFloat) -> Int {
        return determineColumnCount(for: containerWidth)
    }
}

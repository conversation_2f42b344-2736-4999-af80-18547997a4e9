import Foundation
import UIKit

/// 手势日志记录器
/// 专门用于记录双指缩放手势的详细调试信息
class GestureLogger {
    static let shared = GestureLogger()
    
    private let logFileName = "gesture_debug.log"
    private let maxLogSize: Int = 5 * 1024 * 1024 // 5MB
    private var logFileURL: URL?
    private let logQueue = DispatchQueue(label: "gesture.logger", qos: .utility)
    
    private init() {
        setupLogFile()
    }
    
    /// 设置日志文件
    private func setupLogFile() {
        guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            print("❌ 无法获取Documents目录")
            return
        }
        
        logFileURL = documentsPath.appendingPathComponent(logFileName)
        
        // 创建日志文件（如果不存在）
        if let url = logFileURL, !FileManager.default.fileExists(atPath: url.path) {
            FileManager.default.createFile(atPath: url.path, contents: nil, attributes: nil)
            writeHeader()
        }
    }
    
    /// 写入日志头部信息
    private func writeHeader() {
        let header = """
        
        ==========================================
        MPhotos 手势调试日志
        启动时间: \(Date())
        ==========================================
        
        """
        writeToFile(header)
    }
    
    /// 记录手势事件
    /// - Parameters:
    ///   - event: 事件名称
    ///   - details: 详细信息
    ///   - timestamp: 时间戳（可选，默认当前时间）
    func logGesture(_ event: String, details: String = "", timestamp: CFAbsoluteTime? = nil) {
        let currentTime = timestamp ?? CFAbsoluteTimeGetCurrent()
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "HH:mm:ss.SSS"
        
        let logEntry = """
        [🤏 \(dateFormatter.string(from: Date(timeIntervalSinceReferenceDate: currentTime)))] \(event)
        \(details.isEmpty ? "" : "   详情: \(details)")
        
        """
        
        // 同时输出到控制台和文件
        print("🤏 \(event) \(details)")
        
        logQueue.async { [weak self] in
            self?.writeToFile(logEntry)
        }
    }
    
    /// 记录布局变化
    /// - Parameters:
    ///   - from: 原始布局
    ///   - to: 目标布局
    ///   - scale: 缩放值
    ///   - timestamp: 时间戳
    func logLayoutChange(from: String, to: String, scale: CGFloat, timestamp: CFAbsoluteTime) {
        let details = "布局变化: \(from) → \(to), 缩放值: \(String(format: "%.3f", scale))"
        logGesture("布局变化", details: details, timestamp: timestamp)
    }
    
    /// 记录手势性能
    /// - Parameters:
    ///   - operation: 操作名称
    ///   - duration: 持续时间（秒）
    ///   - timestamp: 时间戳
    func logPerformance(_ operation: String, duration: TimeInterval, timestamp: CFAbsoluteTime) {
        let details = "操作: \(operation), 耗时: \(String(format: "%.3f", duration * 1000))ms"
        logGesture("性能统计", details: details, timestamp: timestamp)
    }
    
    /// 记录手势状态
    /// - Parameters:
    ///   - state: 手势状态
    ///   - scale: 缩放值
    ///   - velocity: 速度
    ///   - timestamp: 时间戳
    func logGestureState(_ state: String, scale: CGFloat, velocity: CGFloat, timestamp: CFAbsoluteTime) {
        let details = "状态: \(state), 缩放: \(String(format: "%.3f", scale)), 速度: \(String(format: "%.3f", velocity))"
        logGesture("手势状态", details: details, timestamp: timestamp)
    }
    
    /// 记录错误信息
    /// - Parameters:
    ///   - error: 错误描述
    ///   - timestamp: 时间戳
    func logError(_ error: String, timestamp: CFAbsoluteTime? = nil) {
        let currentTime = timestamp ?? CFAbsoluteTimeGetCurrent()
        logGesture("❌ 错误", details: error, timestamp: currentTime)
    }
    
    /// 写入文件（线程安全版本）
    /// - Parameter content: 内容
    private func writeToFile(_ content: String) {
        guard let url = logFileURL else { return }

        // 🔧 修复：线程安全的文件操作
        do {
            // 检查文件是否存在，如果不存在则创建
            if !FileManager.default.fileExists(atPath: url.path) {
                FileManager.default.createFile(atPath: url.path, contents: nil, attributes: nil)
            }

            // 安全地检查文件大小
            let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
            if let fileSize = attributes[.size] as? Int, fileSize > maxLogSize {
                // 清空文件而不是删除重建，避免竞态条件
                try "".write(to: url, atomically: true, encoding: .utf8)
                writeHeader()
            }

            // 安全地写入内容
            if content.data(using: .utf8) != nil {
                // 使用原子写入避免文件损坏
                let existingContent = (try? String(contentsOf: url)) ?? ""
                let newContent = existingContent + content
                try newContent.write(to: url, atomically: true, encoding: .utf8)
            }

        } catch {
            // 文件操作失败时，静默处理，避免崩溃
            print("⚠️ 日志写入失败: \(error.localizedDescription)")
        }
    }
    
    /// 清空日志（线程安全版本）
    func clearLog() {
        guard let url = logFileURL else { return }

        logQueue.async { [weak self] in
            do {
                // 安全地清空文件
                try "".write(to: url, atomically: true, encoding: .utf8)

                // 在主线程重写头部
                DispatchQueue.main.async {
                    self?.writeHeader()
                    self?.logGesture("日志清空", details: "日志文件已清空")
                }
            } catch {
                print("⚠️ 日志清空失败: \(error.localizedDescription)")
            }
        }
    }
    
    /// 获取日志文件路径
    func getLogFilePath() -> String? {
        return logFileURL?.path
    }
    
    /// 获取日志内容
    func getLogContent() -> String? {
        guard let url = logFileURL else { return nil }
        return try? String(contentsOf: url)
    }
    
    /// 导出日志到相册（用于调试）
    func exportLogToPhotos() {
        guard let content = getLogContent() else {
            print("❌ 无法获取日志内容")
            return
        }
        
        // 创建临时文件
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("gesture_log_\(Date().timeIntervalSince1970).txt")
        
        do {
            try content.write(to: tempURL, atomically: true, encoding: .utf8)
            print("📋 日志已导出到: \(tempURL.path)")
        } catch {
            print("❌ 导出日志失败: \(error)")
        }
    }
}

// MARK: - 便捷日志方法
extension GestureLogger {
    /// 记录手势开始
    func logGestureBegan(scale: CGFloat, touches: Int) {
        logGesture("手势开始", details: "初始缩放: \(String(format: "%.3f", scale)), 触摸点数: \(touches)")
    }
    
    /// 记录手势变化
    func logGestureChanged(scale: CGFloat, deltaScale: CGFloat, velocity: CGFloat) {
        let details = "缩放: \(String(format: "%.3f", scale)), 增量: \(String(format: "%.3f", deltaScale)), 速度: \(String(format: "%.3f", velocity))"
        logGesture("手势变化", details: details)
    }
    
    /// 记录手势结束
    func logGestureEnded(finalScale: CGFloat, finalLayout: String) {
        logGesture("手势结束", details: "最终缩放: \(String(format: "%.3f", finalScale)), 最终布局: \(finalLayout)")
    }
    
    /// 记录布局更新
    func logLayoutUpdate(layoutType: String, duration: TimeInterval) {
        let details = "布局: \(layoutType), 更新耗时: \(String(format: "%.3f", duration * 1000))ms"
        logGesture("布局更新", details: details)
    }
    
    /// 记录触觉反馈
    func logHapticFeedback(type: String, layout: String) {
        logGesture("触觉反馈", details: "类型: \(type), 布局: \(layout)")
    }
} 

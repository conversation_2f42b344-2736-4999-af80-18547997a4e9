//
//  GestureLayoutManager.swift
//  MPhotos
//
//  Created by MPhotos Team on 2024/12/13.
//

import Foundation
import UIKit

/// 手势布局管理器协议 - 定义手势处理的回调接口
protocol GestureLayoutManagerDelegate: AnyObject {
    /// 手势开始时调用
    /// - Parameter manager: 手势管理器实例
    func gestureManagerDidBeginGesture(_ manager: GestureLayoutManager)
    
    /// 手势进行中调用，通知布局变更
    /// - Parameters:
    ///   - manager: 手势管理器实例
    ///   - layoutType: 新的布局类型
    ///   - scale: 当前缩放比例
    ///   - targetIndexPath: 手势位置对应的照片索引（用于定位缩放中心）
    func gestureManager(_ manager: GestureLayoutManager, didUpdateToLayout layoutType: LibrarySettings.LayoutType, scale: CGFloat, targetIndexPath: IndexPath?)
    
    /// 手势结束时调用
    /// - Parameters:
    ///   - manager: 手势管理器实例
    ///   - finalLayout: 最终确定的布局类型
    func gestureManager(_ manager: GestureLayoutManager, didEndWithLayout finalLayout: LibrarySettings.LayoutType)
    
    /// 获取当前布局类型
    /// - Parameter manager: 手势管理器实例
    /// - Returns: 当前布局类型
    func getCurrentLayoutType(for manager: GestureLayoutManager) -> LibrarySettings.LayoutType

    /// 实时缩放动画更新（保留以兼容现有代码）
    /// - Parameters:
    ///   - manager: 手势管理器实例
    ///   - scale: 当前动画缩放值
    func gestureManager(_ manager: GestureLayoutManager, didUpdateRealtimeScale scale: CGFloat)
}

/// 手势布局管理器 - 负责处理缩放手势和布局切换逻辑
///
/// 这个类管理：
/// - UIPinchGestureRecognizer的创建和配置
/// - 缩放比例到布局类型的映射
/// - 手势状态的跟踪和处理
/// - 布局切换的边界限制
class GestureLayoutManager: NSObject {
    
    // MARK: - 属性
    
    /// 代理对象，用于回调布局变更
    weak var delegate: GestureLayoutManagerDelegate?
    
    /// 缩放手势识别器
    private var pinchGestureRecognizer: UIPinchGestureRecognizer!
    
    /// 目标CollectionView
    private weak var targetCollectionView: UICollectionView?
    
    /// 手势开始时的布局类型
    private var gestureStartLayoutType: LibrarySettings.LayoutType = .fiveColumns
    
    /// 手势开始时的缩放比例
    private var gestureStartScale: CGFloat = 1.0
    
    /// 当前累积的缩放比例
    private var currentAccumulatedScale: CGFloat = 1.0
    
    /// 是否正在进行手势操作
    private(set) var isGestureActive: Bool = false
    
    /// 手势锁定状态 - 防止手势进行中被其他操作打断
    private var isGestureLocked: Bool = false

    /// 🔧 新增：当前手势是否已经触发过布局切换（防止一次手势多次切换）
    private var hasTriggeredLayoutSwitch: Bool = false

    /// 🔧 新增：手势开始时的位置（用于定位缩放中心）
    private var gestureStartLocation: CGPoint = .zero

    /// 🔧 新增：手势位置对应的照片索引（用于保持缩放中心）
    private var gestureTargetIndexPath: IndexPath?
    
    /// 检查手势是否正在进行中
    var isGestureInProgress: Bool {
        return isGestureActive || isGestureLocked
    }

    /// 获取当前手势位置（用于精确位置保持）
    /// - Returns: 手势在CollectionView中的位置，如果没有活动手势则返回nil
    func getCurrentGestureLocation() -> CGPoint? {
        guard isGestureActive, let _ = targetCollectionView else {
            return nil
        }
        return gestureStartLocation
    }
    
    /// 缩放阈值 - 避免微小的手势触发布局变更（降低阈值提高灵敏度）
    private let scaleThreshold: CGFloat = 0.03
    
    /// 触觉反馈生成器
    private let lightFeedbackGenerator = UIImpactFeedbackGenerator(style: .light)
    private let mediumFeedbackGenerator = UIImpactFeedbackGenerator(style: .medium)
    private let heavyFeedbackGenerator = UIImpactFeedbackGenerator(style: .heavy)
    
    /// 上一次触发反馈的布局类型
    private var lastFeedbackLayoutType: LibrarySettings.LayoutType?

    /// 实时缩放动画的显示链接器
    private var displayLink: CADisplayLink?

    /// 当前动画缩放值
    private var currentAnimationScale: CGFloat = 1.0

    /// 目标动画缩放值
    private var targetAnimationScale: CGFloat = 1.0

    /// 动画开始时间
    private var animationStartTime: CFTimeInterval = 0

    /// 动画持续时间 - 优化为更短的时间以提供更即时的反馈
    private let animationDuration: CFTimeInterval = 0.15

    // MARK: - 缩放映射配置
    
    /// 布局类型对应的缩放级别
    /// 注意：缩放逻辑遵循用户直觉
    /// - 捏合缩小（scale < 1.0）→ 显示更多照片（更多列）
    /// - 双指放大（scale > 1.0）→ 显示更少照片（更少列）
    private let layoutScaleLevels: [LibrarySettings.LayoutType: CGFloat] = [
        .threeColumns: 2.0,     // 3列 = 2.0（放大手势，显示更少照片）
        .fiveColumns: 1.0,      // 5列 = 1.0（基准值）
        .tenColumns: 0.5        // 10列 = 0.5（缩小手势，显示更多照片）
    ]
    
    /// 缩放比例到布局类型的映射范围
    /// 缩放值越小 → 列数越多 → 照片越小越多
    /// 缩放值越大 → 列数越少 → 照片越大越少
    /// 🔧 重新设计：基于累积缩放值的简单阈值映射
    private let scaleToLayoutMapping: [(range: ClosedRange<CGFloat>, layout: LibrarySettings.LayoutType)] = [
        (0.0...0.8, .tenColumns),      // 10列：累积缩放 < 0.8（缩小手势）
        (0.8...1.6, .fiveColumns),     // 5列：累积缩放 0.8 ~ 1.6（中性区间）
        (1.6...4.0, .threeColumns)     // 3列：累积缩放 > 1.6（放大手势）
    ]
    
    // MARK: - 初始化
    
    /// 初始化手势管理器
    /// - Parameter collectionView: 目标CollectionView
    init(collectionView: UICollectionView) {
        super.init()
        self.targetCollectionView = collectionView
        setupPinchGestureRecognizer()
        GestureLogger.shared.logGesture("GestureLayoutManager 初始化完成")
    }
    
    // MARK: - 公共方法
    
    /// 启用手势识别
    func enableGesture() {
        pinchGestureRecognizer.isEnabled = true
        GestureLogger.shared.logGesture("手势识别已启用")

        // 🔧 新增：启用后立即检查状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.checkGestureRecognizerStatus()
        }
    }
    
    /// 禁用手势识别
    func disableGesture() {
        pinchGestureRecognizer.isEnabled = false
        GestureLogger.shared.logGesture("手势识别已禁用")
    }

    // MARK: - 实时动画方法

    /// 开始实时缩放动画
    /// - Parameter targetScale: 目标缩放值
    private func startRealtimeAnimation(targetScale: CGFloat) {
        // 停止之前的动画
        stopRealtimeAnimation()

        // 设置动画参数
        targetAnimationScale = targetScale
        animationStartTime = CACurrentMediaTime()

        // 创建显示链接器
        displayLink = CADisplayLink(target: self, selector: #selector(updateRealtimeAnimation))
        displayLink?.add(to: .main, forMode: .common)

        GestureLogger.shared.logGesture("开始实时缩放动画", details: "目标缩放: \(String(format: "%.3f", targetScale))")
    }

    /// 停止实时缩放动画
    private func stopRealtimeAnimation() {
        displayLink?.invalidate()
        displayLink = nil
    }

    /// 更新实时动画
    @objc private func updateRealtimeAnimation() {
        let currentTime = CACurrentMediaTime()
        let elapsed = currentTime - animationStartTime
        let progress = min(elapsed / animationDuration, 1.0)

        // 使用优化的缓动函数计算当前缩放值
        let easedProgress = easeInOutCubic(progress)

        // 使用更平滑的插值方法
        let smoothingFactor: CGFloat = 0.3  // 平滑系数，值越小动画越平滑
        let targetDelta = targetAnimationScale - currentAnimationScale
        currentAnimationScale += targetDelta * smoothingFactor * easedProgress

        // 通知代理更新UI
        print("🎬 动画更新: 当前缩放=\(String(format: "%.3f", currentAnimationScale)), 进度=\(String(format: "%.2f", progress))")
        delegate?.gestureManager(self, didUpdateRealtimeScale: currentAnimationScale)

        // 动画完成时停止
        if progress >= 1.0 {
            stopRealtimeAnimation()
            currentAnimationScale = targetAnimationScale
        }
    }

    /// 缓动函数 - 优化的弹性缓出效果
    /// - Parameter t: 进度值 (0.0 - 1.0)
    /// - Returns: 缓动后的值
    private func easeInOutCubic(_ t: Double) -> CGFloat {
        // 使用更自然的缓动曲线，提供更好的手感
        if t < 0.5 {
            return CGFloat(2 * t * t)
        } else {
            let f = t - 1
            return CGFloat(1 - 2 * f * f)
        }
    }

    /// 弹性缓出函数 - 为缩放动画提供更自然的感觉
    /// - Parameter t: 进度值 (0.0 - 1.0)
    /// - Returns: 缓动后的值
    private func easeOutElastic(_ t: Double) -> CGFloat {
        if t == 0 { return 0 }
        if t == 1 { return 1 }

        let p = 0.3
        let s = p / 4
        return CGFloat(pow(2, -10 * t) * sin((t - s) * (2 * Double.pi) / p) + 1)
    }

    /// 🔧 新增：检查手势识别器状态
    func checkGestureRecognizerStatus() {
        let isEnabled = pinchGestureRecognizer.isEnabled
        let state = pinchGestureRecognizer.state.description
        let numberOfTouches = pinchGestureRecognizer.numberOfTouches
        let scale = pinchGestureRecognizer.scale

        let statusInfo = """
        手势识别器状态检查:
        • 是否启用: \(isEnabled)
        • 当前状态: \(state)
        • 触摸点数: \(numberOfTouches)
        • 当前缩放: \(String(format: "%.3f", scale))
        • 手势锁定: \(isGestureLocked)
        • 手势活跃: \(isGestureActive)
        """

        GestureLogger.shared.logGesture("状态检查", details: statusInfo)
        print("🔍 \(statusInfo)")
    }

    /// 🔧 新增：测试缩放映射逻辑
    func testScaleMappingLogic() {
        let testScales: [CGFloat] = [0.3, 0.6, 0.9, 1.2, 1.5, 2.0, 2.5]

        print("🧪 测试缩放映射逻辑:")
        for scale in testScales {
            let layout = getLayoutType(for: scale)
            print("   缩放值 \(String(format: "%.1f", scale)) → \(layout.displayName)")
        }

        GestureLogger.shared.logGesture("缩放映射测试完成", details: "测试了\(testScales.count)个缩放值")
    }
    
    /// 获取指定布局类型对应的缩放级别
    /// - Parameter layoutType: 布局类型
    /// - Returns: 对应的缩放级别
    func getScaleLevel(for layoutType: LibrarySettings.LayoutType) -> CGFloat {
        return layoutScaleLevels[layoutType] ?? 1.0
    }
    
    /// 根据缩放比例获取对应的布局类型
    /// - Parameter scale: 缩放比例
    /// - Returns: 对应的布局类型
    func getLayoutType(for scale: CGFloat) -> LibrarySettings.LayoutType {
        for mapping in scaleToLayoutMapping {
            if mapping.range.contains(scale) {
                return mapping.layout
            }
        }
        
        // 超出范围时返回边界值
        if scale < scaleToLayoutMapping.first!.range.lowerBound {
            return .threeColumns
        } else {
            return .tenColumns
        }
    }
    
    /// 检查缩放比例是否在有效范围内
    /// - Parameter scale: 缩放比例
    /// - Returns: 是否在有效范围内
    func isScaleInValidRange(_ scale: CGFloat) -> Bool {
        let minScale = scaleToLayoutMapping.first!.range.lowerBound
        let maxScale = scaleToLayoutMapping.last!.range.upperBound
        return scale >= minScale && scale <= maxScale
    }
    
    // MARK: - 私有方法
    
    /// 设置缩放手势识别器
    private func setupPinchGestureRecognizer() {
        pinchGestureRecognizer = UIPinchGestureRecognizer(target: self, action: #selector(handlePinchGesture(_:)))
        pinchGestureRecognizer.delegate = self
        
        // 🔧 关键修复：优化手势识别器设置，确保双指缩放优先于滚动
        pinchGestureRecognizer.delaysTouchesBegan = false
        pinchGestureRecognizer.delaysTouchesEnded = false
        pinchGestureRecognizer.cancelsTouchesInView = false  // 改为false，避免过度干扰其他手势

        // 🔧 新增：提高手势识别的优先级和灵敏度
        pinchGestureRecognizer.requiresExclusiveTouchType = false
        
        // 🔧 新增：设置缩放阈值，避免微小手势误触发
        if #available(iOS 13.0, *) {
            pinchGestureRecognizer.allowedTouchTypes = [NSNumber(value: UITouch.TouchType.direct.rawValue)]
        }
        
        guard let collectionView = targetCollectionView else {
            GestureLogger.shared.logError("GestureLayoutManager: 无法获取目标CollectionView")
            return
        }
        
        // 🔧 确保手势识别器立即启用
        pinchGestureRecognizer.isEnabled = true

        collectionView.addGestureRecognizer(pinchGestureRecognizer)

        // 🔧 添加详细的设置验证信息
        let gestureCount = collectionView.gestureRecognizers?.count ?? 0
        let pinchGestureCount = collectionView.gestureRecognizers?.compactMap { $0 as? UIPinchGestureRecognizer }.count ?? 0
        let details = "启用=\(pinchGestureRecognizer.isEnabled), 代理=\(pinchGestureRecognizer.delegate != nil), 总手势数=\(gestureCount), 缩放手势数=\(pinchGestureCount)"
        GestureLogger.shared.logGesture("缩放手势识别器设置完成", details: details)
    }
    
    /// 处理缩放手势
    /// - Parameter gesture: 缩放手势识别器
    @objc private func handlePinchGesture(_ gesture: UIPinchGestureRecognizer) {
        let timestamp = CFAbsoluteTimeGetCurrent()
        let details = "状态: \(gesture.state.description), 缩放: \(String(format: "%.3f", gesture.scale)), 触摸点: \(gesture.numberOfTouches)"
        GestureLogger.shared.logGesture("手势事件", details: details, timestamp: timestamp)
        
        switch gesture.state {
        case .began:
            handleGestureBegan(gesture)
        case .changed:
            handleGestureChanged(gesture)
        case .ended, .cancelled, .failed:
            handleGestureEnded(gesture)
        default:
            break
        }
    }
    
    /// 处理手势开始
    /// - Parameter gesture: 缩放手势识别器
    private func handleGestureBegan(_ gesture: UIPinchGestureRecognizer) {
        let timestamp = CFAbsoluteTimeGetCurrent()

        guard let currentLayout = delegate?.getCurrentLayoutType(for: self) else {
            GestureLogger.shared.logError("无法获取当前布局类型", timestamp: timestamp)
            return
        }

        // 🔧 强化手势保护：立即锁定，防止任何干扰
        isGestureLocked = true
        isGestureActive = true
        hasTriggeredLayoutSwitch = false  // 🔧 重置布局切换标志

        gestureStartLayoutType = currentLayout
        gestureStartScale = getScaleLevel(for: currentLayout)
        currentAccumulatedScale = gestureStartScale
        lastFeedbackLayoutType = currentLayout

        // 🔧 新增：保存手势开始位置，用于定位缩放中心
        if let collectionView = targetCollectionView {
            gestureStartLocation = gesture.location(in: collectionView)
            gestureTargetIndexPath = collectionView.indexPathForItem(at: gestureStartLocation)

            if let indexPath = gestureTargetIndexPath {
                GestureLogger.shared.logGesture("手势位置保存", details: "位置: \(gestureStartLocation), 目标照片: \(indexPath.item)")
            } else {
                GestureLogger.shared.logGesture("手势位置保存", details: "位置: \(gestureStartLocation), 未找到对应照片")
            }
        }

        // 🔧 新增：禁用CollectionView的其他交互，确保手势优先
        if let collectionView = targetCollectionView {
            collectionView.isScrollEnabled = false
            GestureLogger.shared.logGesture("手势开始：禁用滚动", details: "确保手势不被滚动干扰")
        }
        
        // 准备所有触觉反馈生成器
        lightFeedbackGenerator.prepare()
        mediumFeedbackGenerator.prepare()
        heavyFeedbackGenerator.prepare()
        
        GestureLogger.shared.logGestureBegan(scale: gestureStartScale, touches: gesture.numberOfTouches)
        
        // 通知代理手势开始
        let delegateCallTimestamp = CFAbsoluteTimeGetCurrent()
        delegate?.gestureManagerDidBeginGesture(self)
        let delegateCallDuration = CFAbsoluteTimeGetCurrent() - delegateCallTimestamp
        
        GestureLogger.shared.logPerformance("代理回调完成", duration: delegateCallDuration, timestamp: delegateCallTimestamp)
    }
    
    /// 处理手势变化
    /// - Parameter gesture: 缩放手势识别器
    private func handleGestureChanged(_ gesture: UIPinchGestureRecognizer) {
        let timestamp = CFAbsoluteTimeGetCurrent()
        let _ = String(format: "%.3f", timestamp)
        
        // 🔧 专门优化斜向手势的缩放计算
        let rawScale = gesture.scale
        let deltaScale = rawScale - 1.0
        
        // 🔧 检查手势的有效性（确保是真正的双指操作）
        guard gesture.numberOfTouches >= 2 else {
            GestureLogger.shared.logError("跳过单指操作，等待双指手势", timestamp: timestamp)
            return
        }
        
        // 🔧 重新设计：使用累积阈值检测方法
        let minThreshold: CGFloat = 0.02  // 适中的阈值
        if abs(deltaScale) < minThreshold {
            return  // 忽略过小的手势变化
        }

        // 🔧 重新设计：使用相对于起始点的累积变化
        currentAccumulatedScale += deltaScale

        // 🔧 计算相对于手势开始时的净变化
        let netChange = currentAccumulatedScale - gestureStartScale

        // 🔧 设置累积阈值来触发布局切换（提高20%降低灵敏度）
        let switchThreshold: CGFloat = 0.18  // 净变化达到±0.18时触发切换

        var targetLayout = gestureStartLayoutType
        var shouldTriggerSwitch = false

        if netChange > switchThreshold {
            // 放大手势：减少列数（净变化为正）
            switch gestureStartLayoutType {
            case .tenColumns:
                targetLayout = .fiveColumns
                shouldTriggerSwitch = true
            case .fiveColumns:
                targetLayout = .threeColumns
                shouldTriggerSwitch = true
            case .threeColumns:
                break // 已经是最少列数
            }
        } else if netChange < -switchThreshold {
            // 缩小手势：增加列数（净变化为负）
            switch gestureStartLayoutType {
            case .threeColumns:
                targetLayout = .fiveColumns
                shouldTriggerSwitch = true
            case .fiveColumns:
                targetLayout = .tenColumns
                shouldTriggerSwitch = true
            case .tenColumns:
                break // 已经是最多列数
            }
        }

        let clampedScale = shouldTriggerSwitch ? getScaleLevel(for: targetLayout) : getScaleLevel(for: gestureStartLayoutType)
        
        let _ = "原始: \(String(format: "%.3f", rawScale)), 增量: \(String(format: "%.3f", deltaScale)), 净变化: \(String(format: "%.3f", netChange)), 目标: \(targetLayout.displayName)"
        GestureLogger.shared.logGestureChanged(scale: clampedScale, deltaScale: deltaScale, velocity: 1.0)
        
        // 🔧 额外的斜向手势检测信息
        if gesture.numberOfTouches == 2 {
            GestureLogger.shared.logGesture("双指手势确认", details: "支持任意方向（包括斜向）")
        }
        
        // 🔧 获取当前布局类型进行比较
        guard let currentLayoutType = delegate?.getCurrentLayoutType(for: self) else {
            GestureLogger.shared.logError("无法获取当前布局类型进行比较", timestamp: timestamp)
            return
        }

        GestureLogger.shared.logGesture("布局检查", details: "当前布局: \(currentLayoutType.displayName), 目标布局: \(targetLayout.displayName), 净变化: \(String(format: "%.3f", netChange)), 触发切换: \(shouldTriggerSwitch), 已切换: \(hasTriggeredLayoutSwitch)")

        // 移除复杂的实时动画系统，使用简单的UIView.animate动画

        // 🔧 只有当需要触发切换、布局确实不同且本次手势尚未切换过时才更新
        if shouldTriggerSwitch && targetLayout != currentLayoutType && !hasTriggeredLayoutSwitch {
            // 🔧 触发触觉反馈
            provideHapticFeedback(for: targetLayout, from: currentLayoutType)

            // 🔧 记录布局变化
            GestureLogger.shared.logGesture("布局变化", details: "布局变化: \(currentLayoutType.displayName) → \(targetLayout.displayName), 净变化: \(String(format: "%.3f", netChange))")

            // 🔧 通知代理进行布局更新（包含目标位置）
            let updateStartTime = CFAbsoluteTimeGetCurrent()
            delegate?.gestureManager(self, didUpdateToLayout: targetLayout, scale: clampedScale, targetIndexPath: gestureTargetIndexPath)
            let updateDuration = CFAbsoluteTimeGetCurrent() - updateStartTime

            GestureLogger.shared.logGesture("布局更新", details: "布局: \(targetLayout.displayName), 更新耗时: \(String(format: "%.3f", updateDuration * 1000))ms")

            // 🔧 标记本次手势已经触发过布局切换
            hasTriggeredLayoutSwitch = true

            // 🔧 重置累积值和起始布局，为下一次切换做准备
            currentAccumulatedScale = getScaleLevel(for: targetLayout)
            gestureStartLayoutType = targetLayout
            gestureStartScale = getScaleLevel(for: targetLayout)
        } else {
            // 🔧 未触发切换时的日志，包含更详细的原因
            var reason = ""
            if hasTriggeredLayoutSwitch {
                reason = "本次手势已切换过"
            } else if !shouldTriggerSwitch {
                reason = "未达到切换阈值"
            } else if targetLayout == currentLayoutType {
                reason = "布局相同"
            } else {
                reason = "其他原因"
            }
            GestureLogger.shared.logGesture("布局更新被跳过", details: "\(reason), 净变化: \(String(format: "%.3f", netChange))")
        }
        
        // 重置手势缩放比例，避免累积
        gesture.scale = 1.0
    }
    
    /// 处理手势结束
    /// - Parameter gesture: 缩放手势识别器
    private func handleGestureEnded(_ gesture: UIPinchGestureRecognizer) {
        let timestamp = CFAbsoluteTimeGetCurrent()
        let _ = String(format: "%.3f", timestamp)

        let finalLayoutType = delegate?.getCurrentLayoutType(for: self) ?? gestureStartLayoutType

        // 手势结束，动画将由UIView.animate自动处理

        // 🔧 关键修复：延迟解除锁定，确保布局更新完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
            guard let self = self else { return }

            self.isGestureActive = false
            self.isGestureLocked = false

            // 🔧 新增：重新启用CollectionView的滚动
            if let collectionView = self.targetCollectionView {
                collectionView.isScrollEnabled = true
                GestureLogger.shared.logGesture("手势结束：重新启用滚动", details: "恢复正常交互")
            }

            GestureLogger.shared.logGesture("手势锁定解除", details: "布局更新已完成，恢复正常状态")
        }
        
        let endDetails = "起始布局: \(gestureStartLayoutType.displayName), 最终布局: \(finalLayoutType.displayName), 手势状态: \(gesture.state.description)"
        GestureLogger.shared.logGesture("手势结束处理开始", details: endDetails, timestamp: timestamp)
        
        // 通知代理手势结束 - 记录时间
        let delegateStartTime = CFAbsoluteTimeGetCurrent()
        delegate?.gestureManager(self, didEndWithLayout: finalLayoutType)
        let delegateEndTime = CFAbsoluteTimeGetCurrent()
        let delegateDuration = delegateEndTime - delegateStartTime
        
        GestureLogger.shared.logGestureEnded(finalScale: currentAccumulatedScale, finalLayout: finalLayoutType.displayName)
        GestureLogger.shared.logPerformance("手势结束代理回调", duration: delegateDuration, timestamp: delegateEndTime)
    }
    
    // MARK: - 触觉反馈
    
    /// 提供智能触觉反馈
    /// - Parameters:
    ///   - newLayout: 新的布局类型
    ///   - currentLayout: 当前布局类型
    private func provideHapticFeedback(for newLayout: LibrarySettings.LayoutType, from currentLayout: LibrarySettings.LayoutType) {
        // 避免重复触发相同布局的反馈
        guard newLayout != lastFeedbackLayoutType else { return }
        
        lastFeedbackLayoutType = newLayout
        
        // 根据布局变化的"距离"选择反馈强度
        let layoutTransitionIntensity = calculateTransitionIntensity(from: currentLayout, to: newLayout)
        
        switch layoutTransitionIntensity {
        case .light:
            lightFeedbackGenerator.impactOccurred()
            GestureLogger.shared.logHapticFeedback(type: "轻触觉反馈", layout: "\(currentLayout.displayName) → \(newLayout.displayName)")
            
        case .medium:
            mediumFeedbackGenerator.impactOccurred()
            GestureLogger.shared.logHapticFeedback(type: "中等触觉反馈", layout: "\(currentLayout.displayName) → \(newLayout.displayName)")
            
        case .heavy:
            heavyFeedbackGenerator.impactOccurred()
            GestureLogger.shared.logHapticFeedback(type: "强触觉反馈", layout: "\(currentLayout.displayName) → \(newLayout.displayName)")
        }
    }
    
    /// 计算布局过渡的强度
    /// - Parameters:
    ///   - fromLayout: 源布局类型
    ///   - toLayout: 目标布局类型
    /// - Returns: 反馈强度
    private func calculateTransitionIntensity(from fromLayout: LibrarySettings.LayoutType, to toLayout: LibrarySettings.LayoutType) -> FeedbackIntensity {
        let fromColumns = fromLayout.columnsPerRow
        let toColumns = toLayout.columnsPerRow
        let columnDifference = abs(fromColumns - toColumns)
        
        switch columnDifference {
        case 1...2:
            return .light       // 相邻布局切换（如 3↔5 或 5↔10）
        case 3...5:
            return .medium      // 中距离切换（暂时保留，以防未来扩展）
        case 6...:
            return .heavy       // 远距离切换（如 3↔10）
        default:
            return .light       // 默认轻反馈
        }
    }
}

/// 触觉反馈强度枚举
private enum FeedbackIntensity {
    case light
    case medium
    case heavy
}

// MARK: - UIGestureRecognizerDelegate

extension GestureLayoutManager: UIGestureRecognizerDelegate {
    
    /// 是否允许手势识别器开始识别
    /// - Parameter gestureRecognizer: 手势识别器
    /// - Returns: 是否允许开始
    func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        // 确保是缩放手势
        if let pinchGesture = gestureRecognizer as? UIPinchGestureRecognizer {
            // 🔧 专门优化斜向手势识别
            let shouldBegin = pinchGesture.numberOfTouches >= 2
            
            // 🔧 新增：检查手势是否真的是缩放而不是意外触摸
            let scale = pinchGesture.scale
            let velocity = pinchGesture.velocity
            
            let checkDetails = "触摸点数: \(pinchGesture.numberOfTouches), 允许开始: \(shouldBegin), 初始缩放: \(String(format: "%.3f", scale)), 速度: \(String(format: "%.3f", velocity))"
            GestureLogger.shared.logGesture("手势识别器检查", details: checkDetails)
            
            return shouldBegin
        }
        
        GestureLogger.shared.logError("非缩放手势识别器，拒绝开始")
        return false
    }
    
    /// 是否允许与其他手势识别器同时识别
    /// - Parameters:
    ///   - gestureRecognizer: 当前手势识别器
    ///   - otherGestureRecognizer: 其他手势识别器
    /// - Returns: 是否允许同时识别
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        
        // 🔧 关键修复：优化手势同时识别策略
        if gestureRecognizer is UIPinchGestureRecognizer {
            // 允许与某些手势同时进行，但优先处理缩放手势
            if otherGestureRecognizer is UIPanGestureRecognizer {
                // 与滚动手势可以同时开始，但缩放手势优先
                let gestureType = String(describing: type(of: otherGestureRecognizer))
                GestureLogger.shared.logGesture("缩放手势与滚动手势协调", details: "允许与 \(gestureType) 同时开始")
                return true
            } else {
                // 与其他手势不同时进行
                let gestureType = String(describing: type(of: otherGestureRecognizer))
                GestureLogger.shared.logGesture("缩放手势拒绝同时识别", details: "拒绝与 \(gestureType) 同时进行")
                return false
            }
        }
        
        let gestureType = String(describing: type(of: otherGestureRecognizer))
        GestureLogger.shared.logGesture("手势同时识别检查", details: "与 \(gestureType) 同时进行")
        return true
    }
    
    /// 🔧 新增：是否应该要求其他手势识别器失败
    /// - Parameters:
    ///   - gestureRecognizer: 当前手势识别器
    ///   - otherGestureRecognizer: 其他手势识别器
    /// - Returns: 是否要求其他手势失败
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRequireFailureOf otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        
        // 🔧 让双指缩放手势优先于单指滚动手势
        if gestureRecognizer is UIPinchGestureRecognizer,
           otherGestureRecognizer is UIPanGestureRecognizer {
            GestureLogger.shared.logGesture("设置缩放手势优先于滚动手势")
            return false  // 不要求滚动手势失败，而是让缩放手势更优先
        }
        
        return false
    }
    
    /// 🔧 新增：其他手势是否应该要求此手势失败
    /// - Parameters:
    ///   - gestureRecognizer: 当前手势识别器
    ///   - otherGestureRecognizer: 其他手势识别器
    /// - Returns: 其他手势是否要求此手势失败
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        
        // 🔧 不让其他手势要求缩放手势失败，提高缩放手势优先级
        if gestureRecognizer is UIPinchGestureRecognizer {
            GestureLogger.shared.logGesture("缩放手势拒绝被其他手势要求失败")
            return false
        }
        
        return false
    }
}

// MARK: - 调试和日志扩展

extension GestureLayoutManager {
    
    /// 获取当前状态的调试信息
    override var debugDescription: String {
        return """
        GestureLayoutManager调试信息:
        - 手势状态: \(isGestureActive ? "进行中" : "非活跃")
        - 起始布局: \(gestureStartLayoutType.displayName)
        - 起始缩放: \(gestureStartScale)
        - 当前累积缩放: \(currentAccumulatedScale)
        - 缩放阈值: \(scaleThreshold)
        - 手势识别器状态: \(pinchGestureRecognizer.isEnabled ? "启用" : "禁用")
        """
    }
    
    /// 打印缩放映射配置
    func printScaleMappingConfiguration() {
        print("🤏 缩放映射配置:")
        for (layout, scale) in layoutScaleLevels {
            print("   - \(layout.displayName): \(scale)")
        }
        
        print("🤏 缩放范围映射:")
        for mapping in scaleToLayoutMapping {
            print("   - \(mapping.range): \(mapping.layout.displayName)")
        }
    }
    

}

// MARK: - UIGestureRecognizer.State 扩展

extension UIGestureRecognizer.State {
    
    /// 获取状态的描述字符串
    var description: String {
        switch self {
        case .possible:
            return "possible"
        case .began:
            return "began"
        case .changed:
            return "changed"
        case .ended:
            return "ended"
        case .cancelled:
            return "cancelled"
        case .failed:
            return "failed"
        @unknown default:
            return "unknown"
        }
    }
} 
# 图片质量策略设置使用说明

## 📱 如何访问设置

### 1. 打开设置页面
1. 启动MPhotos应用
2. 在图库页面，点击右上角的 **⚙️ 设置** 按钮
3. 进入设置页面

### 2. 找到图片质量策略选项
在设置页面的 **"显示设置"** 部分，你会看到：
- 布局
- 排序
- 按拍摄日期整理
- **图片质量策略** ← 这就是新增的选项

## 🎯 质量策略选项

点击 **"图片质量策略"** 后，会弹出选择菜单，包含4个选项：

### 1. 混合策略 (推荐) 🌟
- **描述**：可见区域高质量，其他区域快速加载
- **内存占用**：300-400MB
- **适用场景**：大多数用户的最佳选择
- **特点**：平衡质量和性能

### 2. 高质量
- **描述**：所有图片使用高质量加载，内存占用较高
- **内存占用**：800MB+
- **适用场景**：高端设备，追求最佳画质
- **特点**：原有方案，质量最高

### 3. 性能优先
- **描述**：所有图片使用快速加载，性能最佳
- **内存占用**：200-300MB
- **适用场景**：低端设备，存储空间紧张
- **特点**：最省内存，滚动最流畅

### 4. 自适应
- **描述**：根据设备内存使用情况自动调整
- **内存占用**：动态调整
- **适用场景**：希望系统自动管理
- **特点**：智能切换，无需手动干预

## 🔄 切换步骤

1. **选择策略**：在弹出菜单中点击想要的策略
2. **确认更改**：系统会显示策略更新确认对话框
3. **立即生效**：策略会立即应用到图库中
4. **自动保存**：设置会自动保存，下次启动时保持

## 📊 实时效果

### 策略切换后的变化：
- **内存占用**：可以在系统设置中查看应用内存使用情况
- **滚动性能**：立即感受到滚动流畅度的变化
- **图片质量**：可见区域的图片质量会相应调整

### 控制台日志：
切换策略时，控制台会显示类似信息：
```
🎯 图片质量策略更新为: 混合策略
🎯 视口更新: 可见[0-20] 高质量[0-40]
```

## 💡 使用建议

### 根据设备选择：
- **iPhone 15 Pro Max, 14 Pro** 等高端设备：推荐 **混合策略** 或 **高质量**
- **iPhone 13, 12** 等中端设备：推荐 **混合策略** 或 **自适应**
- **iPhone SE, XR** 等入门设备：推荐 **性能优先** 或 **自适应**

### 根据使用场景：
- **日常浏览**：混合策略
- **专业修图**：高质量
- **快速清理**：性能优先
- **不确定**：自适应

## 🔧 故障排除

### 如果遇到问题：

1. **策略不生效**：
   - 重启应用
   - 检查控制台日志

2. **内存占用仍然很高**：
   - 尝试切换到"性能优先"
   - 重启应用清理缓存

3. **图片质量下降**：
   - 切换到"高质量"策略
   - 确认当前可见区域的图片

4. **设置丢失**：
   - 设置会自动保存到UserDefaults
   - 如果丢失，会恢复到默认的"混合策略"

## 🎉 总结

通过这个设置选项，你可以：
- ✅ 根据设备性能选择最适合的策略
- ✅ 在质量和性能之间找到平衡
- ✅ 解决内存占用过高的问题
- ✅ 提升应用使用体验

**推荐设置**：对于大多数用户，**混合策略** 是最佳选择，既保证了可见区域的高质量，又显著降低了内存占用。

# 混合质量策略使用指南

## 📋 概述

混合质量策略是一种智能的图片加载方案，通过在不同区域使用不同质量的图片来平衡性能和用户体验。

### 🎯 核心原理

- **可见区域**：使用高质量图片（.opportunistic）
- **前后1屏范围**：预加载高质量图片
- **其他区域**：使用快速模式（.fastFormat）

### 📊 预期效果

- **内存占用**：从800MB降至300-400MB（减少50-70%）
- **用户体验**：可见区域保持高质量，无感知降级
- **滚动性能**：减少内存压力，提升流畅度

## 🚀 快速启用

### 1. 在AppDelegate中初始化

```swift
func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
    
    // 自动选择最佳质量策略
    ImageQualityConfig.shared.autoSelectStrategy()
    
    return true
}
```

### 2. 手动配置策略

```swift
// 在PhotoLibraryViewController的viewDidLoad中
override func viewDidLoad() {
    super.viewDidLoad()
    
    // 设置混合质量策略
    ImageQualityConfig.shared.setQualityStrategy(.hybrid)
    
    // 或根据设备自动选择
    ImageQualityConfig.shared.autoSelectStrategy()
}
```

## ⚙️ 配置选项

### 质量策略类型

```swift
enum QualityStrategy {
    case alwaysHigh     // 始终高质量（原方案）
    case alwaysFast     // 始终快速（性能优先）
    case hybrid         // 混合策略（推荐）
    case adaptive       // 自适应（根据设备性能）
}
```

### 详细配置

```swift
let config = ImageQualityConfig.shared

// 设置高质量区域范围（前后各N屏）
config.highQualityScreenRange = 1  // 默认1屏

// 启用/禁用质量切换动画
config.enableQualityTransitionAnimation = true

// 设置质量更新频率限制（毫秒）
config.qualityUpdateThrottleMs = 100
```

## 📱 设备适配

### 自动策略选择逻辑

```swift
func autoSelectStrategy() {
    let memoryGB = 设备内存GB数
    
    if memoryGB >= 6 {
        // 高端设备：混合策略
        currentStrategy = .hybrid
    } else if memoryGB >= 4 {
        // 中端设备：自适应策略
        currentStrategy = .adaptive
    } else {
        // 低端设备：性能优先
        currentStrategy = .alwaysFast
    }
}
```

### 手动设备配置

```swift
// iPhone 15 Pro Max, iPhone 14 Pro等高端设备
ImageQualityConfig.shared.setQualityStrategy(.hybrid)

// iPhone 12, iPhone 13等中端设备
ImageQualityConfig.shared.setQualityStrategy(.adaptive)

// iPhone SE, iPhone XR等入门设备
ImageQualityConfig.shared.setQualityStrategy(.alwaysFast)
```

## 🔧 高级用法

### 直接使用质量级别

```swift
// 在自定义场景中直接指定质量
PhotoLibraryService.shared.fetchThumbnail(
    for: photo, 
    size: size, 
    quality: .high
) { result in
    // 处理结果
}
```

### 动态调整策略

```swift
// 根据内存使用情况动态调整
func adjustQualityBasedOnMemory() {
    let memoryUsage = getCurrentMemoryUsage()
    
    if memoryUsage > 500 * 1024 * 1024 {  // 超过500MB
        ImageQualityConfig.shared.setQualityStrategy(.alwaysFast)
    } else {
        ImageQualityConfig.shared.setQualityStrategy(.hybrid)
    }
}
```

## 📊 监控和调试

### 生成质量报告

```swift
let report = ImageQualityConfig.shared.generateQualityReport()
print(report)
```

输出示例：
```
🎯 图片质量策略报告
==================
当前策略: hybrid
高质量范围: 前后各1屏
切换动画: 启用
更新频率: 100ms
当前内存: 320MB

💡 策略说明:
- alwaysHigh: 所有图片使用高质量（内存占用高）
- alwaysFast: 所有图片使用快速模式（性能最佳）
- hybrid: 可见区域高质量，其他区域快速模式（推荐）
- adaptive: 根据内存使用情况自动调整
==================
```

### 实时监控

```swift
// 在调试模式下启用详细日志
#if DEBUG
ViewportQualityManager.shared.enableDebugLogging = true
#endif
```

## ⚠️ 注意事项

### 1. 兼容性
- 保持与现有代码的兼容性
- 默认情况下使用原有的高质量策略
- 需要手动启用混合策略

### 2. 性能考虑
- 滚动时会频繁调用质量判断，已优化性能
- 质量切换有100ms的频率限制，避免过度更新
- 使用不同的缓存键区分不同质量的图片

### 3. 用户体验
- 从低质量到高质量的切换有淡入动画
- 可见区域始终保持高质量
- 质量降级对用户不可感知

## 🔄 迁移指南

### 从原有方案迁移

1. **保持现有代码不变**：原有的`fetchThumbnail`方法仍然可用
2. **逐步启用新功能**：在合适的时机调用`setQualityStrategy`
3. **测试验证**：在不同设备上测试内存占用和用户体验

### 回退方案

如果遇到问题，可以随时回退到原有方案：

```swift
// 回退到始终高质量
ImageQualityConfig.shared.setQualityStrategy(.alwaysHigh)
```

## 📈 性能对比

| 策略 | 内存占用 | 图片质量 | 滚动性能 | 适用场景 |
|------|----------|----------|----------|----------|
| alwaysHigh | 800MB+ | 最高 | 一般 | 高端设备，质量优先 |
| hybrid | 300-400MB | 高（可见区域） | 优秀 | 推荐，平衡方案 |
| alwaysFast | 200-300MB | 一般 | 最佳 | 低端设备，性能优先 |
| adaptive | 动态调整 | 动态调整 | 良好 | 智能适配 |

## 🎉 总结

混合质量策略提供了一个完美的平衡点，既保证了用户体验，又显著优化了内存占用。通过智能的区域划分和质量管理，实现了性能和质量的最佳平衡。

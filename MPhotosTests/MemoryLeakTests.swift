//
//  MemoryLeakTests.swift
//  MPhotosTests
//
//  Created by Augment Agent on 2025-08-02.
//

import Testing
import UIKit
import Photos
@testable import MPhotos

/// 内存泄露测试套件 - 验证缓存重构是否解决了内存泄露问题
struct MemoryLeakTests {
    
    @Test("Fast Scrolling Memory Usage")
    func testFastScrollingMemoryUsage() async throws {
        let cacheManager = CacheManager.shared
        
        // 清理缓存，确保测试环境干净
        await cacheManager.clearAll()
        
        let initialHitRate = cacheManager.hitRate
        print("🔍 初始缓存命中率: \(String(format: "%.2f%%", initialHitRate * 100))")
        
        // 模拟快速滑动场景：创建多个模拟的图片请求
        var requestIDs: [PHImageRequestID] = []
        let targetSize = CGSize(width: 200, height: 200)
        
        // 模拟100次快速滑动请求
        for i in 0..<100 {
            // 创建模拟的缓存键
            let mockKey = "mock_asset_\(i)"
            let mockImage = UIImage(systemName: "photo") ?? UIImage()
            
            // 先存储一些图片到缓存中
            if i % 3 == 0 {
                await cacheManager.store(mockImage, for: mockKey)
            }
        }
        
        let finalHitRate = cacheManager.hitRate
        print("🎯 最终缓存命中率: \(String(format: "%.2f%%", finalHitRate * 100))")
        
        // 验证缓存系统正常工作
        #expect(finalHitRate >= 0.0)
        #expect(finalHitRate <= 1.0)
        
        // 清理测试数据
        await cacheManager.clearAll()
        print("✅ 快速滑动内存测试完成")
    }
    
    @Test("Cell Reuse Simulation")
    func testCellReuseSimulation() async throws {
        let cacheManager = CacheManager.shared
        
        // 模拟Cell重用场景
        var activeRequestIDs: [PHImageRequestID] = []
        
        // 模拟10个Cell的重用循环
        for cellIndex in 0..<10 {
            // 模拟Cell配置新内容时取消之前的请求
            for requestID in activeRequestIDs {
                if requestID != PHInvalidImageRequestID {
                    // 在真实场景中，这里会调用 PHImageManager.default().cancelImageRequest(requestID)
                    print("🛑 模拟取消请求: \(requestID)")
                }
            }
            activeRequestIDs.removeAll()
            
            // 模拟新的图片请求
            let mockImage = UIImage(systemName: "photo.\(cellIndex)") ?? UIImage()
            let mockKey = "cell_\(cellIndex)_image"
            
            // 存储到缓存
            await cacheManager.store(mockImage, for: mockKey)
            
            // 模拟从缓存获取（这应该是缓存命中）
            let retrievedImage = await cacheManager.image(for: mockKey)
            #expect(retrievedImage != nil)
            
            print("📱 Cell \(cellIndex) 重用模拟完成")
        }
        
        print("✅ Cell重用模拟测试完成")
    }
    
    @Test("Memory Pressure Handling")
    func testMemoryPressureHandling() async throws {
        let cacheManager = CacheManager.shared
        
        // 获取初始缓存状态
        let initialCacheCount = cacheManager.cacheCount
        let initialMemoryUsage = cacheManager.currentMemoryUsage
        
        print("📊 初始状态 - 缓存数量限制: \(initialCacheCount), 内存限制: \(initialMemoryUsage / 1024 / 1024)MB")
        
        // 模拟内存压力：存储大量图片
        for i in 0..<50 {
            let mockImage = UIImage(systemName: "photo") ?? UIImage()
            let mockKey = "pressure_test_\(i)"
            await cacheManager.store(mockImage, for: mockKey)
        }
        
        // 模拟内存警告
        await cacheManager.clearAll()
        
        // 验证缓存已清理
        let testKey = "pressure_test_0"
        let clearedImage = await cacheManager.image(for: testKey)
        #expect(clearedImage == nil)
        
        print("✅ 内存压力处理测试完成")
    }
    
    @Test("Cache Hit Rate Performance")
    func testCacheHitRatePerformance() async throws {
        let cacheManager = CacheManager.shared
        await cacheManager.clearAll()
        
        // 预填充一些缓存数据
        let preloadCount = 20
        for i in 0..<preloadCount {
            let mockImage = UIImage(systemName: "photo") ?? UIImage()
            let mockKey = "performance_test_\(i)"
            await cacheManager.store(mockImage, for: mockKey)
        }
        
        // 模拟混合访问模式（缓存命中和未命中）
        var hitCount = 0
        let totalRequests = 50
        
        for i in 0..<totalRequests {
            let mockKey = "performance_test_\(i % 30)" // 30%的键会超出预加载范围
            let image = await cacheManager.image(for: mockKey)
            if image != nil {
                hitCount += 1
            }
        }
        
        let calculatedHitRate = Double(hitCount) / Double(totalRequests)
        let systemHitRate = cacheManager.hitRate
        
        print("📈 计算的命中率: \(String(format: "%.2f%%", calculatedHitRate * 100))")
        print("📈 系统报告的命中率: \(String(format: "%.2f%%", systemHitRate * 100))")
        
        // 验证命中率在合理范围内
        #expect(calculatedHitRate >= 0.0)
        #expect(calculatedHitRate <= 1.0)
        
        print("✅ 缓存命中率性能测试完成")
    }
}

/// 内存使用监控工具
struct MemoryMonitor {
    static func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return info.resident_size
        } else {
            return 0
        }
    }
    
    static func formatMemorySize(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB]
        formatter.countStyle = .memory
        return formatter.string(fromByteCount: Int64(bytes))
    }
}

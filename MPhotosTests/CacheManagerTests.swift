//
//  CacheManagerTests.swift
//  MPhotosTests
//
//  Created by Augment Agent on 2025-08-02.
//

import Testing
import UIKit
import Photos
@testable import MPhotos

/// CacheManager 测试套件
struct CacheManagerTests {
    
    @Test("CacheManager Singleton")
    func testCacheManagerSingleton() async throws {
        let manager1 = CacheManager.shared
        let manager2 = CacheManager.shared
        
        #expect(manager1 === manager2) // 应该是同一个实例
    }
    
    @Test("Cache Store and Retrieve")
    func testCacheStoreAndRetrieve() async throws {
        let manager = CacheManager.shared
        let testImage = UIImage(systemName: "photo") ?? UIImage()
        let testKey = "test_image_key"
        
        // 存储图片
        await manager.store(testImage, for: testKey)
        
        // 检索图片
        let retrievedImage = await manager.image(for: testKey)
        
        #expect(retrievedImage != nil)
        #expect(retrievedImage === testImage) // 应该是同一个对象
    }
    
    @Test("Cache Remove")
    func testCacheRemove() async throws {
        let manager = CacheManager.shared
        let testImage = UIImage(systemName: "photo") ?? UIImage()
        let testKey = "test_remove_key"
        
        // 存储图片
        await manager.store(testImage, for: testKey)
        
        // 验证存在
        let beforeRemove = await manager.image(for: testKey)
        #expect(beforeRemove != nil)
        
        // 移除图片
        await manager.removeImage(for: testKey)
        
        // 验证已移除
        let afterRemove = await manager.image(for: testKey)
        #expect(afterRemove == nil)
    }
    
    @Test("Cache Clear All")
    func testCacheClearAll() async throws {
        let manager = CacheManager.shared
        let testImage = UIImage(systemName: "photo") ?? UIImage()
        
        // 存储多个图片
        await manager.store(testImage, for: "key1")
        await manager.store(testImage, for: "key2")
        await manager.store(testImage, for: "key3")
        
        // 验证存在
        let image1 = await manager.image(for: "key1")
        let image2 = await manager.image(for: "key2")
        let image3 = await manager.image(for: "key3")
        
        #expect(image1 != nil)
        #expect(image2 != nil)
        #expect(image3 != nil)
        
        // 清空所有缓存
        await manager.clearAll()
        
        // 验证已清空
        let afterClear1 = await manager.image(for: "key1")
        let afterClear2 = await manager.image(for: "key2")
        let afterClear3 = await manager.image(for: "key3")
        
        #expect(afterClear1 == nil)
        #expect(afterClear2 == nil)
        #expect(afterClear3 == nil)
    }
    
    @Test("Cache Key Generation")
    func testCacheKeyGeneration() async throws {
        let manager = CacheManager.shared
        
        // 创建模拟PHAsset（使用localIdentifier）
        let testIdentifier = "test-asset-id-12345"
        let size = CGSize(width: 200, height: 200)
        let contentMode = PHImageContentMode.aspectFill
        
        // 由于无法直接创建PHAsset，我们测试缓存键的格式
        let expectedKey = "\(testIdentifier)_200x200_\(contentMode.rawValue)"
        
        // 验证缓存键格式正确
        #expect(expectedKey == "test-asset-id-12345_200x200_1")
    }
    
    @Test("Cache Configuration")
    func testCacheConfiguration() async throws {
        let manager = CacheManager.shared
        
        // 测试设置缓存限制
        let newLimit = 100 * 1024 * 1024 // 100MB
        manager.setCacheLimit(newLimit)
        
        let newCountLimit = 500
        manager.setCountLimit(newCountLimit)
        
        // 验证设置生效（通过当前值检查）
        #expect(manager.currentMemoryUsage == newLimit)
        #expect(manager.cacheCount == newCountLimit)
    }
    
    @Test("Hit Rate Calculation")
    func testHitRateCalculation() async throws {
        let manager = CacheManager.shared
        
        // 初始命中率应该是0或基于之前的测试
        let initialHitRate = manager.hitRate
        #expect(initialHitRate >= 0.0)
        #expect(initialHitRate <= 1.0)
    }
}

/// Mock CacheManager for testing
class MockCacheManager: CacheManaging {
    private var storage: [String: UIImage] = [:]
    private var _hitCount = 0
    private var _missCount = 0
    
    var storeImageCalled = false
    var loadImageCalled = false
    var requestImageCalled = false
    
    func store(_ image: UIImage, for key: String) async {
        storeImageCalled = true
        storage[key] = image
    }
    
    func image(for key: String) async -> UIImage? {
        loadImageCalled = true
        return storage[key]
    }
    
    func removeImage(for key: String) async {
        storage.removeValue(forKey: key)
    }
    
    func clearAll() async {
        storage.removeAll()
    }
    
    func requestImage(for asset: PHAsset, targetSize: CGSize, contentMode: PHImageContentMode) async -> UIImage? {
        requestImageCalled = true
        return UIImage(systemName: "photo")
    }
    
    @discardableResult
    func requestImage(for asset: PHAsset, targetSize: CGSize, completion: @escaping (UIImage?) -> Void) -> PHImageRequestID {
        requestImageCalled = true
        
        // 模拟异步回调
        DispatchQueue.main.async {
            completion(UIImage(systemName: "photo"))
        }
        
        return 1 // 返回有效的测试ID
    }
    
    func preloadAssets(_ assets: [PHAsset], targetSize: CGSize) {}
    func stopPreloading(_ assets: [PHAsset], targetSize: CGSize) {}
    func stopAllPreloading() {}
    
    var currentMemoryUsage: Int { return 0 }
    var cacheCount: Int { return storage.count }
    var hitRate: Double {
        let total = _hitCount + _missCount
        guard total > 0 else { return 0.0 }
        return Double(_hitCount) / Double(total)
    }
    
    func setCacheLimit(_ limit: Int) {}
    func setCountLimit(_ limit: Int) {}
    
    func cacheKey(for asset: PHAsset, size: CGSize, contentMode: PHImageContentMode) -> String {
        return "\(asset.localIdentifier)_\(Int(size.width))x\(Int(size.height))_\(contentMode.rawValue)"
    }
}

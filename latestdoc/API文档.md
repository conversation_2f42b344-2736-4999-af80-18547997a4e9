# MPhotos API 文档

## 核心服务 API

### PhotoLibraryService

照片库核心服务，负责照片数据的获取、缓存和管理。

#### 单例访问
```swift
let service = PhotoLibraryService.shared
```

#### 主要方法

##### fetchPhotos(with:completion:)
根据设置获取照片数据

```swift
func fetchPhotos(with settings: LibrarySettings, completion: @escaping ([PhotoModel]) -> Void)
```

**参数:**
- `settings`: LibrarySettings - 图库设置
- `completion`: 完成回调，返回照片模型数组

**示例:**
```swift
let settings = SettingsService.shared.librarySettings
PhotoLibraryService.shared.fetchPhotos(with: settings) { photos in
    DispatchQueue.main.async {
        self.updateUI(with: photos)
    }
}
```

##### fetchThumbnail(for:size:completion:)
获取照片缩略图

```swift
func fetchThumbnail(for photo: PhotoModel, size: CGSize, completion: @escaping (Result<Any, Error>) -> Void)
```

**参数:**
- `photo`: PhotoModel - 照片模型
- `size`: CGSize - 缩略图尺寸
- `completion`: 完成回调，返回UIImage或错误

##### startCaching(for:targetSize:)
开始缓存指定照片

```swift
func startCaching(for photos: [PhotoModel], targetSize: CGSize)
```

##### stopCaching(for:targetSize:)
停止缓存指定照片

```swift
func stopCaching(for photos: [PhotoModel], targetSize: CGSize)
```

#### 属性

##### totalCount
```swift
var totalCount: Int { get }
```
返回照片总数量

##### cachedPhotos
```swift
var cachedPhotos: [PhotoModel] { get }
```
返回当前缓存的照片数组

### SettingsService

设置管理服务，负责应用设置的持久化和管理。

#### 单例访问
```swift
let service = SettingsService.shared
```

#### 主要方法

##### saveSettings()
保存当前设置到持久化存储

```swift
func saveSettings()
```

##### resetToDefault()
重置为默认设置

```swift
func resetToDefault()
```

##### addDelegate(_:)
添加设置变更代理

```swift
func addDelegate(_ delegate: SettingsChangeDelegate)
```

##### removeDelegate(_:)
移除设置变更代理

```swift
func removeDelegate(_ delegate: SettingsChangeDelegate)
```

#### 属性

##### librarySettings
```swift
var librarySettings: LibrarySettings { get set }
```
当前图库设置，设置时会自动保存并通知代理

##### isFirstLaunch
```swift
var isFirstLaunch: Bool { get }
```
是否为首次启动

## 数据模型 API

### PhotoModel

照片数据模型，封装PHAsset和相关展示数据。

#### 初始化
```swift
init(from asset: PHAsset)
```

#### 基础属性
```swift
let id: String                          // 唯一标识符
let asset: PHAsset                      // PHAsset对象
let creationDate: Date?                 // 创建日期
let modificationDate: Date?             // 修改日期
let mediaType: PHAssetMediaType         // 媒体类型
let mediaSubtypes: PHAssetMediaSubtype  // 媒体子类型
let pixelSize: CGSize                   // 像素尺寸
let duration: TimeInterval              // 持续时间（视频）
let isFavorite: Bool                    // 是否收藏
```

#### 计算属性
```swift
var isLivePhoto: Bool { get }           // 是否为Live Photo
var isVideo: Bool { get }               // 是否为视频
var isPanorama: Bool { get }            // 是否为全景图
var isScreenshot: Bool { get }          // 是否为截图
var displayDateString: String { get }   // 显示用日期字符串
```

#### 扩展属性
```swift
var isSelected: Bool                    // 是否被选中
var thumbnailImage: Any?                // 缓存的缩略图
```

#### 方法
```swift
func formattedFileSize() -> String      // 格式化文件大小
func formattedDuration() -> String      // 格式化持续时间
func detailInfoString() -> String       // 详细信息字符串
```

### LibrarySettings

图库设置模型，包含所有用户可配置的选项。

#### 布局设置
```swift
var layoutType: LayoutType              // 布局类型（3列/4列）
var gridSpacing: CGFloat                // 网格间距
var thumbnailCornerRadius: CGFloat      // 缩略图圆角
```

#### 排序和分组
```swift
var sortOrder: SortOrder                // 排序方式
var groupByDate: Bool                   // 是否按日期分组
var showDateHeaders: Bool               // 是否显示日期头部
```

#### 显示选项
```swift
var showLivePhotoIndicator: Bool        // 显示Live Photo标识
var showVideoDurationIndicator: Bool    // 显示视频时长标识
var showQuickSelectionOptions: Bool     // 显示快速选择选项
```

#### 相册选择
```swift
var selectedAlbumIdentifiers: Set<String>  // 选中的相册标识符
```

#### 枚举类型

##### LayoutType
```swift
enum LayoutType: Int, CaseIterable, Codable {
    case threeColumns = 3       // 3列布局
    case fiveColumns = 5        // 5列布局
    case tenColumns = 10        // 10列布局

    var displayName: String { get }     // 显示名称
    var columnsPerRow: Int { get }      // 列数
}
```

##### SortOrder
```swift
enum SortOrder: String, CaseIterable, Codable {
    case newestFirst = "newest"         // 最新在前
    case oldestFirst = "oldest"         // 最旧在前
    
    var displayName: String { get }     // 显示名称
    var sortDescriptor: NSSortDescriptor { get }  // 排序描述符
}
```

## 视图模型 API

### PhotoLibraryViewModel

图库视图模型，管理照片数据和业务逻辑。

#### 初始化
```swift
init(photoLibraryService: PhotoLibraryService = .shared,
     settingsService: SettingsService = .shared)
```

#### 回调闭包
```swift
var onPhotosUpdated: (([PhotoModel]) -> Void)?     // 照片更新回调
var onError: ((Error) -> Void)?                    // 错误处理回调
var onSettingsChanged: ((LibrarySettings) -> Void)?  // 设置变更回调
```

#### 主要方法

##### loadPhotos()
加载照片数据

```swift
func loadPhotos()
```

##### refreshPhotos()
刷新照片数据

```swift
func refreshPhotos()
```

##### getPhoto(at:)
获取指定索引的照片

```swift
func getPhoto(at index: Int) -> PhotoModel?
```

##### togglePhotoSelection(at:)
切换照片选中状态

```swift
@discardableResult
func togglePhotoSelection(at index: Int) -> Bool
```

##### getSelectedPhotos()
获取选中的照片

```swift
func getSelectedPhotos() -> [PhotoModel]
```

#### 手势管理
```swift
func setGestureActive(_ active: Bool)   // 设置手势状态
func shouldReloadAfterGesture() -> Bool // 手势结束后是否需要重新加载
```

#### 属性
```swift
var currentSettings: LibrarySettings { get }  // 当前设置
var photoCount: Int { get }                   // 照片数量
```

## 协议定义

### PhotoDataSourceProtocol

照片数据源协议，定义照片数据获取接口。

```swift
protocol PhotoDataSourceProtocol {
    func fetchPhotos(with settings: LibrarySettings, completion: @escaping ([PhotoModel]) -> Void)
    func fetchPhoto(at index: Int) -> PhotoModel?
    var totalCount: Int { get }
    func fetchThumbnail(for photo: PhotoModel, size: CGSize, completion: @escaping (Result<Any, Error>) -> Void)
}
```

### SettingsServiceProtocol

设置服务协议，定义设置管理接口。

```swift
protocol SettingsServiceProtocol {
    var librarySettings: LibrarySettings { get set }
    func saveSettings()
    func resetToDefault()
}
```

### SettingsChangeDelegate

设置变更代理协议。

```swift
protocol SettingsChangeDelegate: AnyObject {
    func settingsDidChange(_ settings: LibrarySettings)
}
```

### LibrarySettingsDelegate

图库设置代理协议。

```swift
protocol LibrarySettingsDelegate: AnyObject {
    func settingsDidChange(_ settings: LibrarySettings)
}
```

## 通知系统

### 系统通知

#### PhotosUpdated
照片数据更新通知

```swift
NotificationCenter.default.post(name: NSNotification.Name("PhotosUpdated"), object: nil)
```

监听示例：
```swift
NotificationCenter.default.addObserver(
    self,
    selector: #selector(handlePhotosUpdated),
    name: NSNotification.Name("PhotosUpdated"),
    object: nil
)
```

## 错误处理

### 常见错误类型

#### 权限错误
- 照片库访问权限被拒绝
- 照片库访问权限受限

#### 数据错误
- 照片加载失败
- 缓存写入失败
- 设置保存失败

#### 网络错误
- iCloud照片同步失败
- 网络连接超时

### 错误处理最佳实践

```swift
PhotoLibraryService.shared.fetchThumbnail(for: photo, size: size) { result in
    switch result {
    case .success(let image):
        // 处理成功情况
        DispatchQueue.main.async {
            self.imageView.image = image as? UIImage
        }
    case .failure(let error):
        // 处理错误情况
        print("❌ 图片加载失败: \(error.localizedDescription)")
        DispatchQueue.main.async {
            self.showErrorPlaceholder()
        }
    }
}
```

这个API文档提供了MPhotos项目中所有核心组件的详细接口说明，便于开发者理解和使用。

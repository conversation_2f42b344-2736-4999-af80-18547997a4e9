# MPhotos 重构实施计划与代码示例

## 📋 重构目标与策略

### 核心重构原则
1. **简化架构** - 移除过度复杂的渐进式加载系统，采用标准MVVM-C架构
2. **优化性能** - 实现轻量级二级缓存，提升内存和响应性能  
3. **标准化代码** - 统一代码风格，移除调试和实验性代码
4. **功能完善** - 添加相簿管理、搜索等完整功能

### 架构对比

**重构前（复杂架构）:**
```
PhotoLibraryService → ProgressiveLoadingManager → 复杂缓存策略
                  → ImageQualityConfig → ViewportQualityManager
                  → GestureLayoutManager → 复杂手势逻辑
```

**重构后（简化架构）:**
```
MediaManager → 标准图片加载 → 轻量级二级缓存
LayoutManager → 简化手势处理
SettingsService → 标准设置管理
```

## 🏗️ 核心组件重构实现

### 1. MediaManager 重构

#### 1.1 简化的MediaManager实现

```swift
import Photos
import Combine
import UIKit

// MARK: - 协议定义
protocol MediaManaging {
    func requestAuthorization() async throws -> PHAuthorizationStatus
    func fetchAlbums(ofType type: AlbumType) -> [Album]
    func fetchAssets(in album: Album?) -> PHFetchResult<PHAsset>
    func createAlbum(title: String) async throws -> Album
    func deleteAsset(_ asset: PHAsset) async throws
    func addObserver(_ observer: MediaObserver)
}

protocol MediaObserver: AnyObject {
    func mediaDidChange(changeInfo: PHChange)
}

// MARK: - 数据模型
struct Album {
    let identifier: String
    let title: String
    let type: AlbumType
    let assetCount: Int
    let collection: PHAssetCollection?
    
    func fetchAssets() -> PHFetchResult<PHAsset> {
        guard let collection = collection else {
            return PHFetchResult<PHAsset>()
        }
        
        let options = PHFetchOptions()
        options.sortDescriptors = [
            NSSortDescriptor(key: "creationDate", ascending: false)
        ]
        return PHAsset.fetchAssets(in: collection, options: options)
    }
}

enum AlbumType {
    case smartAlbum
    case userAlbum
    case sharedAlbum
}

// MARK: - MediaManager实现
final class MediaManager: NSObject, MediaManaging {
    static let shared = MediaManager()
    
    private let photoLibrary = PHPhotoLibrary.shared()
    private var observers: [WeakRef<MediaObserver>] = []
    
    private override init() {
        super.init()
        photoLibrary.register(self)
    }
    
    deinit {
        photoLibrary.unregisterChangeObserver(self)
    }
    
    func requestAuthorization() async throws -> PHAuthorizationStatus {
        return await withCheckedContinuation { continuation in
            PHPhotoLibrary.requestAuthorization(for: .readWrite) { status in
                continuation.resume(returning: status)
            }
        }
    }
    
    func fetchAlbums(ofType type: AlbumType) -> [Album] {
        var collections: PHFetchResult<PHAssetCollection>
        
        switch type {
        case .smartAlbum:
            collections = PHAssetCollection.fetchAssetCollections(
                with: .smartAlbum,
                subtype: .any,
                options: nil
            )
        case .userAlbum:
            collections = PHAssetCollection.fetchAssetCollections(
                with: .album,
                subtype: .any,
                options: nil
            )
        case .sharedAlbum:
            collections = PHAssetCollection.fetchAssetCollections(
                with: .album,
                subtype: .albumCloudShared,
                options: nil
            )
        }
        
        var albums: [Album] = []
        collections.enumerateObjects { collection, _, _ in
            let assetCount = PHAsset.fetchAssets(in: collection, options: nil).count
            let album = Album(
                identifier: collection.localIdentifier,
                title: collection.localizedTitle ?? "Unknown",
                type: type,
                assetCount: assetCount,
                collection: collection
            )
            albums.append(album)
        }
        
        return albums.filter { $0.assetCount > 0 }
    }
    
    func fetchAssets(in album: Album?) -> PHFetchResult<PHAsset> {
        let options = PHFetchOptions()
        options.sortDescriptors = [
            NSSortDescriptor(key: "creationDate", ascending: false)
        ]
        
        if let album = album, let collection = album.collection {
            return PHAsset.fetchAssets(in: collection, options: options)
        } else {
            // 返回所有照片
            return PHAsset.fetchAssets(with: options)
        }
    }
    
    func createAlbum(title: String) async throws -> Album {
        return try await withCheckedThrowingContinuation { continuation in
            var placeholderIdentifier: String?
            
            photoLibrary.performChanges({
                let request = PHAssetCollectionChangeRequest.creationRequestForAssetCollection(withTitle: title)
                placeholderIdentifier = request.placeholderForCreatedAssetCollection.localIdentifier
            }) { success, error in
                if success, let identifier = placeholderIdentifier {
                    let collections = PHAssetCollection.fetchAssetCollections(
                        withLocalIdentifiers: [identifier],
                        options: nil
                    )
                    
                    if let collection = collections.firstObject {
                        let album = Album(
                            identifier: identifier,
                            title: title,
                            type: .userAlbum,
                            assetCount: 0,
                            collection: collection
                        )
                        continuation.resume(returning: album)
                    } else {
                        continuation.resume(throwing: PhotosError.albumCreationFailed)
                    }
                } else {
                    continuation.resume(throwing: error ?? PhotosError.albumCreationFailed)
                }
            }
        }
    }
    
    func deleteAsset(_ asset: PHAsset) async throws {
        try await withCheckedThrowingContinuation { continuation in
            photoLibrary.performChanges({
                PHAssetChangeRequest.deleteAssets([asset] as NSArray)
            }) { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosError.deletionFailed)
                }
            }
        }
    }
    
    func addObserver(_ observer: MediaObserver) {
        observers.append(WeakRef(observer))
        observers = observers.filter { $0.object != nil }
    }
}

// MARK: - PHPhotoLibraryChangeObserver
extension MediaManager: PHPhotoLibraryChangeObserver {
    func photoLibraryDidChange(_ changeInstance: PHChange) {
        DispatchQueue.main.async { [weak self] in
            self?.observers.forEach { ref in
                ref.object?.mediaDidChange(changeInfo: changeInstance)
            }
        }
    }
}

// MARK: - 支持类型
enum PhotosError: LocalizedError {
    case unauthorized
    case albumCreationFailed
    case deletionFailed
    case assetNotFound
    
    var errorDescription: String? {
        switch self {
        case .unauthorized:
            return "需要访问相册的权限"
        case .albumCreationFailed:
            return "创建相册失败"
        case .deletionFailed:
            return "删除失败"
        case .assetNotFound:
            return "找不到指定资源"
        }
    }
}

class WeakRef<T: AnyObject> {
    weak var object: T?
    
    init(_ object: T) {
        self.object = object
    }
}
```

### 2. 轻量级缓存系统

#### 2.1 简化的CacheManager实现

```swift
import UIKit
import Photos

final class LightweightCacheManager {
    static let shared = LightweightCacheManager()
    
    private let memoryCache = NSCache<NSString, UIImage>()
    private let imageManager = PHCachingImageManager()
    private let processingQueue = DispatchQueue(label: "cache.processing", qos: .userInitiated)
    
    private init() {
        setupMemoryCache()
        observeMemoryWarnings()
    }
    
    private func setupMemoryCache() {
        let memoryCapacity = ProcessInfo.processInfo.physicalMemory
        let maxCacheSize = min(memoryCapacity / 10, 100 * 1024 * 1024) // 最大100MB
        
        memoryCache.totalCostLimit = Int(maxCacheSize)
        memoryCache.countLimit = 300 // 最多缓存300张图片
    }
    
    private func observeMemoryWarnings() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    @objc private func handleMemoryWarning() {
        memoryCache.removeAllObjects()
        imageManager.stopCachingImagesForAllAssets()
    }
    
    func requestImage(
        for asset: PHAsset,
        targetSize: CGSize,
        completion: @escaping (UIImage?) -> Void
    ) {
        let key = cacheKey(for: asset, size: targetSize)
        
        // 检查内存缓存
        if let cachedImage = memoryCache.object(forKey: key as NSString) {
            completion(cachedImage)
            return
        }
        
        // 从PhotoKit加载
        let options = PHImageRequestOptions()
        options.isNetworkAccessAllowed = false
        options.deliveryMode = .opportunistic
        options.resizeMode = .fast
        
        imageManager.requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        ) { [weak self] image, info in
            guard let image = image,
                  info?[PHImageResultIsDegradedKey] as? Bool != true else {
                completion(nil)
                return
            }
            
            // 缓存到内存
            let cost = Int(targetSize.width * targetSize.height * 4)
            self?.memoryCache.setObject(image, forKey: key as NSString, cost: cost)
            
            completion(image)
        }
    }
    
    func preloadImages(for assets: [PHAsset], targetSize: CGSize) {
        let assetsToPreload = Array(assets.prefix(50)) // 限制预加载数量
        
        imageManager.startCachingImages(
            for: assetsToPreload,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: nil
        )
    }
    
    func stopPreloading(for assets: [PHAsset], targetSize: CGSize) {
        imageManager.stopCachingImages(
            for: assets,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: nil
        )
    }
    
    private func cacheKey(for asset: PHAsset, size: CGSize) -> String {
        return "\(asset.localIdentifier)_\(Int(size.width))x\(Int(size.height))"
    }
}
```

### 3. MVVM-C架构实现

#### 3.1 相册列表ViewModel

```swift
import Combine
import Photos

final class AlbumsViewModel: ObservableObject {
    @Published var albums: [Album] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    private let mediaManager: MediaManaging
    private var cancellables = Set<AnyCancellable>()
    
    init(mediaManager: MediaManaging = MediaManager.shared) {
        self.mediaManager = mediaManager
    }
    
    func fetchAlbums() {
        isLoading = true
        error = nil
        
        Task { @MainActor in
            do {
                let authStatus = try await mediaManager.requestAuthorization()
                
                guard authStatus == .authorized || authStatus == .limited else {
                    throw PhotosError.unauthorized
                }
                
                let smartAlbums = mediaManager.fetchAlbums(ofType: .smartAlbum)
                let userAlbums = mediaManager.fetchAlbums(ofType: .userAlbum)
                
                albums = smartAlbums + userAlbums
                isLoading = false
                
            } catch {
                self.error = error
                isLoading = false
            }
        }
    }
    
    func createAlbum(title: String) async throws {
        let newAlbum = try await mediaManager.createAlbum(title: title)
        
        await MainActor.run {
            albums.append(newAlbum)
        }
    }
    
    func deleteAlbum(_ album: Album) {
        // 实现删除逻辑
        albums.removeAll { $0.identifier == album.identifier }
    }
}
```

#### 3.2 照片网格ViewController

```swift
import UIKit
import Photos
import Combine

final class PhotoGridViewController: UIViewController {
    
    // MARK: - Properties
    private lazy var collectionView: UICollectionView = {
        let layout = createLayout()
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .systemBackground
        cv.delegate = self
        cv.dataSource = self
        cv.prefetchDataSource = self
        cv.register(PhotoCell.self, forCellWithReuseIdentifier: PhotoCell.identifier)
        return cv
    }()
    
    private let viewModel: PhotoGridViewModel
    private let cacheManager = LightweightCacheManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    private var assets: PHFetchResult<PHAsset>?
    private var thumbnailSize: CGSize = .zero
    
    // MARK: - Initialization
    init(viewModel: PhotoGridViewModel) {
        self.viewModel = viewModel
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindViewModel()
        calculateThumbnailSize()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        viewModel.loadAssets()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        view.addSubview(collectionView)
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    private func bindViewModel() {
        viewModel.$assets
            .receive(on: DispatchQueue.main)
            .sink { [weak self] assets in
                self?.assets = assets
                self?.collectionView.reloadData()
            }
            .store(in: &cancellables)
        
        viewModel.$error
            .compactMap { $0 }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                self?.showError(error)
            }
            .store(in: &cancellables)
    }
    
    private func createLayout() -> UICollectionViewLayout {
        let itemSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0/3.0),
            heightDimension: .fractionalHeight(1.0)
        )
        let item = NSCollectionLayoutItem(layoutSize: itemSize)
        item.contentInsets = NSDirectionalEdgeInsets(top: 1, leading: 1, bottom: 1, trailing: 1)
        
        let groupSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0),
            heightDimension: .fractionalWidth(1.0/3.0)
        )
        let group = NSCollectionLayoutGroup.horizontal(layoutSize: groupSize, subitems: [item, item, item])
        
        let section = NSCollectionLayoutSection(group: group)
        
        return UICollectionViewCompositionalLayout(section: section)
    }
    
    private func calculateThumbnailSize() {
        let itemWidth = (view.bounds.width - 4) / 3 // 3列布局
        let scale = UIScreen.main.scale
        thumbnailSize = CGSize(
            width: itemWidth * scale,
            height: itemWidth * scale
        )
    }
    
    private func showError(_ error: Error) {
        let alert = UIAlertController(
            title: "错误",
            message: error.localizedDescription,
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - UICollectionViewDataSource
extension PhotoGridViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return assets?.count ?? 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: PhotoCell.identifier, for: indexPath) as! PhotoCell
        
        if let asset = assets?.object(at: indexPath.item) {
            cell.configure(with: asset, targetSize: thumbnailSize)
        }
        
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension PhotoGridViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard let asset = assets?.object(at: indexPath.item) else { return }
        viewModel.selectAsset(asset, at: indexPath.item)
    }
}

// MARK: - UICollectionViewDataSourcePrefetching
extension PhotoGridViewController: UICollectionViewDataSourcePrefetching {
    func collectionView(_ collectionView: UICollectionView, prefetchItemsAt indexPaths: [IndexPath]) {
        let assets = indexPaths.compactMap { self.assets?.object(at: $0.item) }
        cacheManager.preloadImages(for: assets, targetSize: thumbnailSize)
    }
    
    func collectionView(_ collectionView: UICollectionView, cancelPrefetchingForItemsAt indexPaths: [IndexPath]) {
        let assets = indexPaths.compactMap { self.assets?.object(at: $0.item) }
        cacheManager.stopPreloading(for: assets, targetSize: thumbnailSize)
    }
}

// MARK: - PhotoGridViewModel
final class PhotoGridViewModel: ObservableObject {
    @Published var assets: PHFetchResult<PHAsset>?
    @Published var isLoading = false
    @Published var error: Error?
    
    private let album: Album?
    private let mediaManager: MediaManaging
    
    var onAssetSelected: ((PHAsset, Int) -> Void)?
    
    init(album: Album? = nil, mediaManager: MediaManaging = MediaManager.shared) {
        self.album = album
        self.mediaManager = mediaManager
    }
    
    func loadAssets() {
        isLoading = true
        error = nil
        
        Task { @MainActor in
            do {
                let fetchedAssets = mediaManager.fetchAssets(in: album)
                assets = fetchedAssets
                isLoading = false
            } catch {
                self.error = error
                isLoading = false
            }
        }
    }
    
    func selectAsset(_ asset: PHAsset, at index: Int) {
        onAssetSelected?(asset, index)
    }
}
```

#### 3.3 照片单元格

```swift
import UIKit
import Photos

final class PhotoCell: UICollectionViewCell {
    static let identifier = "PhotoCell"
    
    private let imageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        return iv
    }()
    
    private let overlayView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        view.isHidden = true
        return view
    }()
    
    private let durationLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        label.layer.cornerRadius = 4
        label.clipsToBounds = true
        label.textAlignment = .center
        label.isHidden = true
        return label
    }()
    
    private var currentAsset: PHAsset?
    private let cacheManager = LightweightCacheManager.shared
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(imageView)
        contentView.addSubview(overlayView)
        contentView.addSubview(durationLabel)
        
        imageView.translatesAutoresizingMaskIntoConstraints = false
        overlayView.translatesAutoresizingMaskIntoConstraints = false
        durationLabel.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            overlayView.topAnchor.constraint(equalTo: contentView.topAnchor),
            overlayView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            overlayView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            overlayView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            durationLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -4),
            durationLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -4),
            durationLabel.widthAnchor.constraint(greaterThanOrEqualToConstant: 30),
            durationLabel.heightAnchor.constraint(equalToConstant: 20)
        ])
    }
    
    func configure(with asset: PHAsset, targetSize: CGSize) {
        currentAsset = asset
        
        // 重置状态
        imageView.image = nil
        overlayView.isHidden = true
        durationLabel.isHidden = true
        
        // 显示视频时长
        if asset.mediaType == .video {
            let duration = Int(asset.duration)
            let minutes = duration / 60
            let seconds = duration % 60
            durationLabel.text = String(format: "%d:%02d", minutes, seconds)
            durationLabel.isHidden = false
        }
        
        // 加载缩略图
        cacheManager.requestImage(for: asset, targetSize: targetSize) { [weak self] image in
            DispatchQueue.main.async {
                // 确保cell没有被重用
                guard self?.currentAsset == asset else { return }
                self?.imageView.image = image
            }
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        currentAsset = nil
        imageView.image = nil
        overlayView.isHidden = true
        durationLabel.isHidden = true
    }
    
    func setSelected(_ selected: Bool) {
        overlayView.isHidden = !selected
    }
}
```

## 📋 分阶段重构实施计划

### Phase 1: 核心架构重构 (1-2周)

```swift
// 1.1 移除复杂组件
// 删除以下文件：
// - ProgressiveLoadingManager.swift
// - Debug/ 目录下所有文件
// - ImageQualityConfig.swift
// - ViewportQualityManager.swift

// 1.2 实现简化的服务层
// 创建新文件：MediaManager.swift
// 实现上述MediaManager代码

// 1.3 实现轻量级缓存
// 创建新文件：LightweightCacheManager.swift
// 实现上述CacheManager代码

// 1.4 更新依赖注入
protocol DIContainer {
    func register<T>(_ type: T.Type, factory: @escaping () -> T)
    func resolve<T>(_ type: T.Type) -> T
}

final class Container: DIContainer {
    private var factories: [String: () -> Any] = [:]
    
    func register<T>(_ type: T.Type, factory: @escaping () -> T) {
        let key = String(describing: type)
        factories[key] = factory
    }
    
    func resolve<T>(_ type: T.Type) -> T {
        let key = String(describing: type)
        guard let factory = factories[key] else {
            fatalError("Type \(type) not registered")
        }
        return factory() as! T
    }
}

// 服务注册
extension Container {
    static func setup() -> Container {
        let container = Container()
        
        container.register(MediaManaging.self) {
            MediaManager.shared
        }
        
        container.register(LightweightCacheManager.self) {
            LightweightCacheManager.shared
        }
        
        return container
    }
}
```

### Phase 2: UI层重构 (1-2周)

```swift
// 2.1 重构PhotoLibraryViewController
// 使用上述PhotoGridViewController替换现有实现

// 2.2 简化手势处理
final class LayoutGestureHandler {
    weak var collectionView: UICollectionView?
    
    private var currentColumns = 3
    private let minColumns = 2
    private let maxColumns = 5
    
    func setupGestures() {
        guard let collectionView = collectionView else { return }
        
        let pinchGesture = UIPinchGestureRecognizer(target: self, action: #selector(handlePinch(_:)))
        collectionView.addGestureRecognizer(pinchGesture)
    }
    
    @objc private func handlePinch(_ gesture: UIPinchGestureRecognizer) {
        guard gesture.state == .ended else { return }
        
        if gesture.scale > 1.5 && currentColumns > minColumns {
            currentColumns -= 1
            updateLayout()
        } else if gesture.scale < 0.7 && currentColumns < maxColumns {
            currentColumns += 1
            updateLayout()
        }
    }
    
    private func updateLayout() {
        guard let collectionView = collectionView else { return }
        
        UIView.animate(withDuration: 0.3) {
            collectionView.setCollectionViewLayout(
                self.createLayout(for: self.currentColumns),
                animated: false
            )
        }
    }
    
    private func createLayout(for columns: Int) -> UICollectionViewLayout {
        let itemSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0/CGFloat(columns)),
            heightDimension: .fractionalHeight(1.0)
        )
        let item = NSCollectionLayoutItem(layoutSize: itemSize)
        item.contentInsets = NSDirectionalEdgeInsets(top: 1, leading: 1, bottom: 1, trailing: 1)
        
        let groupSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0),
            heightDimension: .fractionalWidth(1.0/CGFloat(columns))
        )
        let group = NSCollectionLayoutGroup.horizontal(
            layoutSize: groupSize,
            subitems: Array(repeating: item, count: columns)
        )
        
        let section = NSCollectionLayoutSection(group: group)
        return UICollectionViewCompositionalLayout(section: section)
    }
}

// 2.3 简化设置管理
final class SettingsManager {
    static let shared = SettingsManager()
    
    private let userDefaults = UserDefaults.standard
    
    enum SettingsKey: String {
        case defaultColumns = "default_columns"
        case showVideoOverlay = "show_video_overlay"
        case enableHapticFeedback = "enable_haptic_feedback"
    }
    
    var defaultColumns: Int {
        get { userDefaults.integer(forKey: SettingsKey.defaultColumns.rawValue) }
        set { userDefaults.set(newValue, forKey: SettingsKey.defaultColumns.rawValue) }
    }
    
    var showVideoOverlay: Bool {
        get { userDefaults.bool(forKey: SettingsKey.showVideoOverlay.rawValue) }
        set { userDefaults.set(newValue, forKey: SettingsKey.showVideoOverlay.rawValue) }
    }
    
    var enableHapticFeedback: Bool {
        get { userDefaults.bool(forKey: SettingsKey.enableHapticFeedback.rawValue) }
        set { userDefaults.set(newValue, forKey: SettingsKey.enableHapticFeedback.rawValue) }
    }
    
    private init() {
        registerDefaults()
    }
    
    private func registerDefaults() {
        userDefaults.register(defaults: [
            SettingsKey.defaultColumns.rawValue: 3,
            SettingsKey.showVideoOverlay.rawValue: true,
            SettingsKey.enableHapticFeedback.rawValue: true
        ])
    }
}
```

### Phase 3: 功能完善 (2-3周)

```swift
// 3.1 相簿管理功能
final class AlbumManagementViewModel: ObservableObject {
    @Published var albums: [Album] = []
    @Published var selectedAlbum: Album?
    @Published var isCreatingAlbum = false
    
    private let mediaManager: MediaManaging
    
    init(mediaManager: MediaManaging = MediaManager.shared) {
        self.mediaManager = mediaManager
    }
    
    func createAlbum(title: String) async throws {
        isCreatingAlbum = true
        defer { isCreatingAlbum = false }
        
        let newAlbum = try await mediaManager.createAlbum(title: title)
        
        await MainActor.run {
            albums.append(newAlbum)
        }
    }
    
    func renameAlbum(_ album: Album, to newTitle: String) async throws {
        // 实现重命名逻辑
        guard let collection = album.collection else { return }
        
        await PHPhotoLibrary.shared().performChanges {
            let request = PHAssetCollectionChangeRequest(for: collection)
            request?.title = newTitle
        }
    }
    
    func moveAssets(_ assets: [PHAsset], to album: Album) async throws {
        guard let collection = album.collection else { return }
        
        await PHPhotoLibrary.shared().performChanges {
            let request = PHAssetCollectionChangeRequest(for: collection)
            request?.addAssets(assets as NSArray)
        }
    }
}

// 3.2 搜索功能
import Vision

final class SearchManager: ObservableObject {
    @Published var searchResults: [PHAsset] = []
    @Published var isSearching = false
    
    private let mediaManager: MediaManaging
    
    init(mediaManager: MediaManaging = MediaManager.shared) {
        self.mediaManager = mediaManager
    }
    
    func search(query: String) async {
        isSearching = true
        defer { isSearching = false }
        
        let allAssets = mediaManager.fetchAssets(in: nil)
        var results: [PHAsset] = []
        
        // 按日期搜索
        if let date = parseDate(from: query) {
            results.append(contentsOf: searchByDate(assets: allAssets, date: date))
        }
        
        // 按关键词搜索（简单实现）
        if query.lowercased().contains("video") {
            results.append(contentsOf: searchByMediaType(assets: allAssets, type: .video))
        }
        
        await MainActor.run {
            self.searchResults = Array(Set(results)) // 去重
        }
    }
    
    private func searchByDate(assets: PHFetchResult<PHAsset>, date: Date) -> [PHAsset] {
        var results: [PHAsset] = []
        let calendar = Calendar.current
        
        assets.enumerateObjects { asset, _, _ in
            if let creationDate = asset.creationDate,
               calendar.isDate(creationDate, inSameDayAs: date) {
                results.append(asset)
            }
        }
        
        return results
    }
    
    private func searchByMediaType(assets: PHFetchResult<PHAsset>, type: PHAssetMediaType) -> [PHAsset] {
        var results: [PHAsset] = []
        
        assets.enumerateObjects { asset, _, _ in
            if asset.mediaType == type {
                results.append(asset)
            }
        }
        
        return results
    }
    
    private func parseDate(from query: String) -> Date? {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.date(from: query)
    }
}

// 3.3 批量操作
final class BatchOperationManager {
    private let mediaManager: MediaManaging
    
    init(mediaManager: MediaManaging = MediaManager.shared) {
        self.mediaManager = mediaManager
    }
    
    func deleteAssets(_ assets: [PHAsset]) async throws {
        try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges {
                PHAssetChangeRequest.deleteAssets(assets as NSArray)
            } completionHandler: { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosError.deletionFailed)
                }
            }
        }
    }
    
    func favoriteAssets(_ assets: [PHAsset], favorite: Bool) async throws {
        try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges {
                for asset in assets {
                    let request = PHAssetChangeRequest(for: asset)
                    request.isFavorite = favorite
                }
            } completionHandler: { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosError.updateFailed)
                }
            }
        }
    }
    
    func shareAssets(_ assets: [PHAsset], from viewController: UIViewController) {
        let imageManager = PHImageManager.default()
        var itemsToShare: [Any] = []
        let group = DispatchGroup()
        
        for asset in assets {
            group.enter()
            
            if asset.mediaType == .image {
                let options = PHImageRequestOptions()
                options.isNetworkAccessAllowed = true
                options.deliveryMode = .highQualityFormat
                
                imageManager.requestImage(
                    for: asset,
                    targetSize: PHImageManagerMaximumSize,
                    contentMode: .aspectFit,
                    options: options
                ) { image, _ in
                    if let image = image {
                        itemsToShare.append(image)
                    }
                    group.leave()
                }
            } else {
                group.leave()
            }
        }
        
        group.notify(queue: .main) {
            let activityController = UIActivityViewController(
                activityItems: itemsToShare,
                applicationActivities: nil
            )
            
            viewController.present(activityController, animated: true)
        }
    }
}
```

### Phase 4: 性能优化与完善 (1周)

```swift
// 4.1 内存监控优化
final class MemoryMonitor {
    static let shared = MemoryMonitor()
    
    private var observers: [WeakRef<MemoryObserver>] = []
    
    private init() {
        startMonitoring()
    }
    
    func addObserver(_ observer: MemoryObserver) {
        observers.append(WeakRef(observer))
        cleanupObservers()
    }
    
    private func startMonitoring() {
        Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { [weak self] _ in
            self?.checkMemoryUsage()
        }
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(memoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    @objc private func memoryWarning() {
        notifyObservers(.critical)
    }
    
    private func checkMemoryUsage() {
        let usage = getMemoryUsage()
        let total = ProcessInfo.processInfo.physicalMemory
        let percentage = Double(usage) / Double(total)
        
        let level: MemoryPressureLevel
        switch percentage {
        case 0.7..<0.85:
            level = .moderate
        case 0.85...:
            level = .high
        default:
            level = .normal
        }
        
        notifyObservers(level)
    }
    
    private func notifyObservers(_ level: MemoryPressureLevel) {
        observers.forEach { $0.object?.memoryPressureDidChange(level) }
    }
    
    private func cleanupObservers() {
        observers = observers.filter { $0.object != nil }
    }
    
    private func getMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        return kerr == KERN_SUCCESS ? info.resident_size : 0
    }
}

protocol MemoryObserver: AnyObject {
    func memoryPressureDidChange(_ level: MemoryPressureLevel)
}

enum MemoryPressureLevel {
    case normal
    case moderate
    case high
    case critical
}

// 4.2 性能监控
final class PerformanceMonitor {
    static let shared = PerformanceMonitor()
    
    private var displayLink: CADisplayLink?
    private var frameCount = 0
    private var lastTimestamp: CFTimeInterval = 0
    
    func startMonitoring() {
        displayLink = CADisplayLink(target: self, selector: #selector(tick))
        displayLink?.add(to: .main, forMode: .common)
    }
    
    @objc private func tick(_ displayLink: CADisplayLink) {
        if lastTimestamp == 0 {
            lastTimestamp = displayLink.timestamp
            return
        }
        
        frameCount += 1
        let elapsed = displayLink.timestamp - lastTimestamp
        
        if elapsed >= 1.0 {
            let fps = Double(frameCount) / elapsed
            
            if fps < 45 {
                // 帧率过低，触发优化
                NotificationCenter.default.post(
                    name: .performanceWarning,
                    object: nil,
                    userInfo: ["fps": fps]
                )
            }
            
            frameCount = 0
            lastTimestamp = displayLink.timestamp
        }
    }
}

extension Notification.Name {
    static let performanceWarning = Notification.Name("PerformanceWarning")
}
```

## 🔧 Agent使用指南

### 开发环境设置

```bash
# 1. 初始化项目
xcode-project create PhotosApp --template iOS --language Swift

# 2. 添加必要的framework
# 在Xcode中添加：
# - Photos.framework
# - PhotosUI.framework
# - Vision.framework (用于搜索功能)
# - Combine.framework
```

### 推荐的开发顺序

1. **创建基础架构** - 实现MediaManager和CacheManager
2. **构建UI组件** - 从PhotoCell开始，然后是PhotoGridViewController
3. **添加手势支持** - 实现LayoutGestureHandler
4. **完善相册功能** - 添加AlbumManagementViewModel
5. **集成搜索** - 实现SearchManager
6. **优化性能** - 添加MemoryMonitor和PerformanceMonitor

### 测试验证

```swift
// 单元测试示例
import XCTest
@testable import PhotosApp

class CacheManagerTests: XCTestCase {
    func testCacheStoreAndRetrieve() async {
        let cacheManager = LightweightCacheManager.shared
        let testImage = UIImage(systemName: "photo")!
        let testAsset = MockPHAsset()
        
        // 测试图片缓存
        await cacheManager.store(testImage, for: testAsset, targetSize: CGSize(width: 100, height: 100))
        
        let retrievedImage = await cacheManager.cachedImage(for: testAsset, targetSize: CGSize(width: 100, height: 100))
        
        XCTAssertNotNil(retrievedImage)
    }
}
```

这个重构计划提供了完整的代码示例和实施步骤，可以直接用于开发。每个组件都经过简化，移除了不必要的复杂性，同时保持了应用的核心功能和性能优势。

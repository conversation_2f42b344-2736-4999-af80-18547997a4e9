// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		B5FE988B2DFC5041006542EE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B5FE98682DFC5040006542EE /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B5FE986F2DFC5040006542EE;
			remoteInfo = MPhotos;
		};
		B5FE98952DFC5041006542EE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B5FE98682DFC5040006542EE /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B5FE986F2DFC5040006542EE;
			remoteInfo = MPhotos;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		B5FE98702DFC5040006542EE /* MPhotos.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MPhotos.app; sourceTree = BUILT_PRODUCTS_DIR; };
		B5FE988A2DFC5041006542EE /* MPhotosTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MPhotosTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		B5FE98942DFC5041006542EE /* MPhotosUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MPhotosUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		B5FE989C2DFC5041006542EE /* Exceptions for "MPhotos" folder in "MPhotos" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = B5FE986F2DFC5040006542EE /* MPhotos */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		B5FE98722DFC5040006542EE /* MPhotos */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				B5FE989C2DFC5041006542EE /* Exceptions for "MPhotos" folder in "MPhotos" target */,
			);
			path = MPhotos;
			sourceTree = "<group>";
		};
		B5FE988D2DFC5041006542EE /* MPhotosTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MPhotosTests;
			sourceTree = "<group>";
		};
		B5FE98972DFC5041006542EE /* MPhotosUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MPhotosUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		B5FE986D2DFC5040006542EE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5FE98872DFC5041006542EE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5FE98912DFC5041006542EE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		B5FE98672DFC5040006542EE = {
			isa = PBXGroup;
			children = (
				B5FE98722DFC5040006542EE /* MPhotos */,
				B5FE988D2DFC5041006542EE /* MPhotosTests */,
				B5FE98972DFC5041006542EE /* MPhotosUITests */,
				B5FE98712DFC5040006542EE /* Products */,
			);
			sourceTree = "<group>";
		};
		B5FE98712DFC5040006542EE /* Products */ = {
			isa = PBXGroup;
			children = (
				B5FE98702DFC5040006542EE /* MPhotos.app */,
				B5FE988A2DFC5041006542EE /* MPhotosTests.xctest */,
				B5FE98942DFC5041006542EE /* MPhotosUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B5FE986F2DFC5040006542EE /* MPhotos */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B5FE989D2DFC5041006542EE /* Build configuration list for PBXNativeTarget "MPhotos" */;
			buildPhases = (
				B5FE986C2DFC5040006542EE /* Sources */,
				B5FE986D2DFC5040006542EE /* Frameworks */,
				B5FE986E2DFC5040006542EE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				B5FE98722DFC5040006542EE /* MPhotos */,
			);
			name = MPhotos;
			packageProductDependencies = (
			);
			productName = MPhotos;
			productReference = B5FE98702DFC5040006542EE /* MPhotos.app */;
			productType = "com.apple.product-type.application";
		};
		B5FE98892DFC5041006542EE /* MPhotosTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B5FE98A22DFC5041006542EE /* Build configuration list for PBXNativeTarget "MPhotosTests" */;
			buildPhases = (
				B5FE98862DFC5041006542EE /* Sources */,
				B5FE98872DFC5041006542EE /* Frameworks */,
				B5FE98882DFC5041006542EE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B5FE988C2DFC5041006542EE /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				B5FE988D2DFC5041006542EE /* MPhotosTests */,
			);
			name = MPhotosTests;
			packageProductDependencies = (
			);
			productName = MPhotosTests;
			productReference = B5FE988A2DFC5041006542EE /* MPhotosTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		B5FE98932DFC5041006542EE /* MPhotosUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B5FE98A52DFC5041006542EE /* Build configuration list for PBXNativeTarget "MPhotosUITests" */;
			buildPhases = (
				B5FE98902DFC5041006542EE /* Sources */,
				B5FE98912DFC5041006542EE /* Frameworks */,
				B5FE98922DFC5041006542EE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B5FE98962DFC5041006542EE /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				B5FE98972DFC5041006542EE /* MPhotosUITests */,
			);
			name = MPhotosUITests;
			packageProductDependencies = (
			);
			productName = MPhotosUITests;
			productReference = B5FE98942DFC5041006542EE /* MPhotosUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B5FE98682DFC5040006542EE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					B5FE986F2DFC5040006542EE = {
						CreatedOnToolsVersion = 16.4;
					};
					B5FE98892DFC5041006542EE = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = B5FE986F2DFC5040006542EE;
					};
					B5FE98932DFC5041006542EE = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = B5FE986F2DFC5040006542EE;
					};
				};
			};
			buildConfigurationList = B5FE986B2DFC5040006542EE /* Build configuration list for PBXProject "MPhotos" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = B5FE98672DFC5040006542EE;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = B5FE98712DFC5040006542EE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B5FE986F2DFC5040006542EE /* MPhotos */,
				B5FE98892DFC5041006542EE /* MPhotosTests */,
				B5FE98932DFC5041006542EE /* MPhotosUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B5FE986E2DFC5040006542EE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5FE98882DFC5041006542EE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5FE98922DFC5041006542EE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B5FE986C2DFC5040006542EE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5FE98862DFC5041006542EE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5FE98902DFC5041006542EE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		B5FE988C2DFC5041006542EE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B5FE986F2DFC5040006542EE /* MPhotos */;
			targetProxy = B5FE988B2DFC5041006542EE /* PBXContainerItemProxy */;
		};
		B5FE98962DFC5041006542EE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B5FE986F2DFC5040006542EE /* MPhotos */;
			targetProxy = B5FE98952DFC5041006542EE /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		B5FE989E2DFC5041006542EE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MPhotos/MPhotos.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5BNRB84BM8;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MPhotos/Info.plist;
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "MPhotos需要访问权限来保存照片到您的照片库";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "MPhotos需要访问您的照片库来展示和管理您的照片";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPcraft.MPhotos;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B5FE989F2DFC5041006542EE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MPhotos/MPhotos.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5BNRB84BM8;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MPhotos/Info.plist;
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "MPhotos需要访问权限来保存照片到您的照片库";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "MPhotos需要访问您的照片库来展示和管理您的照片";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPcraft.MPhotos;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		B5FE98A02DFC5041006542EE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = PZTGY4GLGM;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		B5FE98A12DFC5041006542EE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = PZTGY4GLGM;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		B5FE98A32DFC5041006542EE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PZTGY4GLGM;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPcraft.MPhotosTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MPhotos.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MPhotos";
			};
			name = Debug;
		};
		B5FE98A42DFC5041006542EE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PZTGY4GLGM;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPcraft.MPhotosTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MPhotos.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MPhotos";
			};
			name = Release;
		};
		B5FE98A62DFC5041006542EE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PZTGY4GLGM;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPcraft.MPhotosUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = MPhotos;
			};
			name = Debug;
		};
		B5FE98A72DFC5041006542EE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PZTGY4GLGM;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPcraft.MPhotosUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = MPhotos;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B5FE986B2DFC5040006542EE /* Build configuration list for PBXProject "MPhotos" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5FE98A02DFC5041006542EE /* Debug */,
				B5FE98A12DFC5041006542EE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B5FE989D2DFC5041006542EE /* Build configuration list for PBXNativeTarget "MPhotos" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5FE989E2DFC5041006542EE /* Debug */,
				B5FE989F2DFC5041006542EE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B5FE98A22DFC5041006542EE /* Build configuration list for PBXNativeTarget "MPhotosTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5FE98A32DFC5041006542EE /* Debug */,
				B5FE98A42DFC5041006542EE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B5FE98A52DFC5041006542EE /* Build configuration list for PBXNativeTarget "MPhotosUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5FE98A62DFC5041006542EE /* Debug */,
				B5FE98A72DFC5041006542EE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = B5FE98682DFC5040006542EE /* Project object */;
}

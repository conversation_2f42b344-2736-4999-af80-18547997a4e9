# MPhotos 缓存技术方案与内存泄露解决方案

## 📋 概述

本文档详细介绍了MPhotos项目中使用的缓存技术栈、架构设计和解决高速滑动内存泄露的完整方案。适用于其他有类似问题的项目参考和重构。

## 🏗️ 缓存架构设计

### 1. 双层缓存架构

```swift
// L1: NSCache (内存缓存) - 快速访问
private let memoryCache = NSCache<NSString, UIImage>()

// L2: PHCachingImageManager (系统缓存) - 系统级优化
private let imageManager = PHCachingImageManager()
```

**优势**：
- L1提供毫秒级访问速度
- L2利用系统级优化和磁盘缓存
- 自动内存管理和压力释放

### 2. 缓存键设计

```swift
func cacheKey(for asset: PHAsset, size: CGSize, contentMode: PHImageContentMode = .aspectFill) -> String {
    return "\(asset.localIdentifier)_\(Int(size.width))x\(Int(size.height))_\(contentMode.rawValue)"
}

// 示例: "F8918D71-FDCD-4BB1-8DAA-98D7A3A06B4B_200x200_1"
```

**设计原则**：
- 唯一性：asset ID + 尺寸 + 内容模式
- 可读性：便于调试和日志分析
- 高效性：字符串拼接性能优化

## 🔧 核心技术实现

### 1. CacheManager 核心类

```swift
final class CacheManager: NSObject, CacheManaging {
    // MARK: - 单例模式
    static let shared = CacheManager()
    
    // MARK: - 核心组件
    private let memoryCache = NSCache<NSString, UIImage>()
    private let imageManager = PHCachingImageManager()
    private let queue = DispatchQueue(label: "cacheManager", qos: .userInitiated, attributes: .concurrent)
    
    // MARK: - 统计信息
    private var _hitCount: Int = 0
    private var _missCount: Int = 0
    private let statisticsQueue = DispatchQueue(label: "cacheStatistics", qos: .utility)
}
```

### 2. 内存缓存配置

```swift
private func setupMemoryCache() {
    memoryCache.totalCostLimit = 50 * 1024 * 1024  // 50MB
    memoryCache.countLimit = 200                    // 200张图片
    memoryCache.delegate = self                     // 监听清理事件
}

private func calculateImageCost(_ image: UIImage) -> Int {
    let pixelCount = Int(image.size.width * image.size.height * image.scale * image.scale)
    return pixelCount * 4 // 4 bytes per pixel (RGBA)
}
```

### 3. 关键的requestImage实现

```swift
@discardableResult
func requestImage(for asset: PHAsset, targetSize: CGSize, completion: @escaping (UIImage?) -> Void) -> PHImageRequestID {
    let key = cacheKey(for: asset, size: targetSize)
    
    // 🔑 关键：同步检查内存缓存
    if let cachedImage = memoryCache.object(forKey: key as NSString) {
        statisticsQueue.async { [weak self] in
            self?._hitCount += 1
        }
        logDebug("Cache hit for key: \(key)")
        
        DispatchQueue.main.async {
            completion(cachedImage)
        }
        return PHInvalidImageRequestID // 🔑 关键：表示缓存命中，无需请求
    }
    
    // 🔑 关键：直接进行PHImageManager请求，返回真实requestID
    let options = createImageRequestOptions()
    return imageManager.requestImage(
        for: asset,
        targetSize: targetSize,
        contentMode: .aspectFill,
        options: options
    ) { [weak self] image, info in
        guard let image = image else {
            DispatchQueue.main.async { completion(nil) }
            return
        }
        
        // 检查是否为最终高质量图片
        let isDegraded = info?[PHImageResultIsDegradedKey] as? Bool ?? false
        if !isDegraded {
            // 存储到内存缓存
            let cost = self?.calculateImageCost(image) ?? 0
            self?.memoryCache.setObject(image, forKey: key as NSString, cost: cost)
            logDebug("Stored image in cache with key: \(key)")
        }
        
        DispatchQueue.main.async {
            completion(image)
        }
    }
}
```

## 🚨 内存泄露问题与解决方案

### 问题根源分析

#### 1. 原始问题代码
```swift
// ❌ 错误的实现 - 导致内存泄露
func requestImage(completion: @escaping (UIImage?) -> Void) -> PHImageRequestID {
    Task { [weak self] in
        if let cachedImage = await self?.image(for: key) {
            completion(cachedImage)
            return
        }
        
        // requestID在Task内部生成，无法返回
        let requestID = self?.imageManager.requestImage(...)
    }
    
    return 0 // ❌ 返回占位符ID，导致无法取消请求
}
```

#### 2. Cell重用问题
```swift
// ❌ 错误的取消逻辑
override func prepareForReuse() {
    if let requestID = imageRequestID {
        PHImageManager.default().cancelImageRequest(requestID) // requestID = 0，取消失败
    }
}
```

### 解决方案

#### 1. 修复requestImage方法
- **移除异步Task包装**：避免requestID丢失
- **同步缓存检查**：提高性能，避免竞态条件
- **返回真实requestID**：确保能正确取消请求

#### 2. 优化Cell取消逻辑
```swift
// ✅ 正确的取消逻辑
override func prepareForReuse() {
    super.prepareForReuse()
    
    // 🔑 关键：检查requestID有效性
    if let requestID = imageRequestID, requestID != PHInvalidImageRequestID {
        PHImageManager.default().cancelImageRequest(requestID)
        imageRequestID = nil
    }
    
    // 重置UI状态
    imageView.image = nil
    // ... 其他重置操作
}
```

## 🎯 预加载策略

### 1. 智能预加载
```swift
private func preloadThumbnails() {
    guard !assets.isEmpty else { return }
    
    // 预加载前20张图片的缩略图
    let preloadCount = min(20, assets.count)
    let assetsToPreload = Array(assets.prefix(preloadCount))
    
    Task {
        let targetSize = CGSize(width: 200, height: 200)
        
        for asset in assetsToPreload {
            _ = cacheManager.requestImage(for: asset, targetSize: targetSize) { _ in
                // 图片已缓存，无需处理
            }
        }
    }
}
```

### 2. 系统级预加载
```swift
func preloadAssets(_ assets: [PHAsset], targetSize: CGSize) {
    guard !assets.isEmpty else { return }
    
    let options = createImageRequestOptions()
    imageManager.startCachingImages(
        for: assets,
        targetSize: targetSize,
        contentMode: .aspectFill,
        options: options
    )
    
    logDebug("Started preloading \(assets.count) assets")
}
```

## 📊 内存管理策略

### 1. 内存警告处理
```swift
@objc private func didReceiveMemoryWarning() {
    logWarning("Memory warning received, clearing cache")
    Task {
        await clearMemoryCache()
    }
}

private func clearMemoryCache() async {
    await withCheckedContinuation { continuation in
        queue.async(flags: .barrier) { [weak self] in
            self?.memoryCache.removeAllObjects()
            logInfo("Cleared memory cache")
            continuation.resume()
        }
    }
}
```

### 2. 动态缓存调整
```swift
struct Constants {
    struct Cache {
        static let memoryCacheLimit = 50 * 1024 * 1024 // 50MB
        static let memoryCacheCountLimit = 200          // 200张图片
        static let thumbnailCacheExpiry: TimeInterval = 60 * 60 * 24 // 24小时
    }
    
    struct Performance {
        static let maxConcurrentImageRequests = 10
        static let lowMemoryThreshold = 100 * 1024 * 1024  // 100MB
        static let criticalMemoryThreshold = 200 * 1024 * 1024 // 200MB
    }
}
```

## 🔍 性能监控与统计

### 1. 缓存命中率统计
```swift
var hitRate: Double {
    return statisticsQueue.sync {
        let total = _hitCount + _missCount
        guard total > 0 else { return 0.0 }
        return Double(_hitCount) / Double(total)
    }
}
```

### 2. 内存使用监控
```swift
var currentMemoryUsage: Int {
    return memoryCache.totalCostLimit
}

private func calculateImageCost(_ image: UIImage) -> Int {
    let pixelCount = Int(image.size.width * image.size.height * image.scale * image.scale)
    return pixelCount * 4 // 4 bytes per pixel (RGBA)
}
```

## 🛠️ 协议设计与依赖注入

### 1. CacheManaging协议
```swift
protocol CacheManaging {
    // MARK: - 图片缓存
    func store(_ image: UIImage, for key: String) async
    func image(for key: String) async -> UIImage?
    func removeImage(for key: String) async
    func clearAll() async

    // MARK: - PHAsset图片请求
    func requestImage(for asset: PHAsset, targetSize: CGSize, contentMode: PHImageContentMode) async -> UIImage?
    @discardableResult
    func requestImage(for asset: PHAsset, targetSize: CGSize, completion: @escaping (UIImage?) -> Void) -> PHImageRequestID

    // MARK: - 预加载
    func preloadAssets(_ assets: [PHAsset], targetSize: CGSize)
    func stopPreloading(_ assets: [PHAsset], targetSize: CGSize)
    func stopAllPreloading()

    // MARK: - 缓存信息
    var currentMemoryUsage: Int { get }
    var cacheCount: Int { get }
    var hitRate: Double { get }

    // MARK: - 缓存配置
    func setCacheLimit(_ limit: Int)
    func setCountLimit(_ limit: Int)
}
```

### 2. 依赖注入实现
```swift
// ServiceContainer中注册
container.register(CacheManaging.self) { _ in
    CacheManager.shared
}

// ViewModel中使用
class PhotoLibraryViewModel: ObservableObject {
    let cacheManager: CacheManaging

    init(cacheManager: CacheManaging = ServiceContainer.shared.resolve()) {
        self.cacheManager = cacheManager
    }
}
```

## 🧪 测试策略

### 1. Mock缓存管理器
```swift
class MockCacheManager: CacheManaging {
    private var storage: [String: UIImage] = [:]

    var storeImageCalled = false
    var loadImageCalled = false
    var requestImageCalled = false

    func store(_ image: UIImage, for key: String) async {
        storeImageCalled = true
        storage[key] = image
    }

    func image(for key: String) async -> UIImage? {
        loadImageCalled = true
        return storage[key]
    }

    @discardableResult
    func requestImage(for asset: PHAsset, targetSize: CGSize, completion: @escaping (UIImage?) -> Void) -> PHImageRequestID {
        requestImageCalled = true

        // 模拟异步回调
        DispatchQueue.main.async {
            completion(UIImage()) // 返回测试图片
        }

        return 1 // 返回有效的测试ID
    }
}
```

### 2. 内存泄露测试
```swift
func testFastScrollingMemoryUsage() {
    let initialMemory = getMemoryUsage()

    // 模拟快速滑动1000张照片
    for i in 0..<1000 {
        let indexPath = IndexPath(item: i, section: 0)
        let cell = collectionView.cellForItem(at: indexPath) as? PhotoGridCell

        // 模拟cell重用
        cell?.prepareForReuse()
        cell?.configure(with: mockAssets[i % mockAssets.count], cacheManager: cacheManager)
    }

    // 等待所有请求完成
    wait(for: [expectation], timeout: 5.0)

    let finalMemory = getMemoryUsage()
    let memoryIncrease = finalMemory - initialMemory

    // 验证内存增长在合理范围内
    XCTAssertLessThan(memoryIncrease, 100 * 1024 * 1024) // 小于100MB
}
```

## 🚀 性能优化技巧

### 1. 滚动性能优化
```swift
// 在滚动时降低图片质量
func scrollViewDidScroll(_ scrollView: UIScrollView) {
    let isScrolling = scrollView.isDecelerating || scrollView.isDragging
    let options = PHImageRequestOptions()
    options.deliveryMode = isScrolling ? .fastFormat : .highQualityFormat
    options.resizeMode = isScrolling ? .fast : .exact
}
```

### 2. 智能预加载范围
```swift
func updatePreloadRange() {
    let visibleIndexPaths = collectionView.indexPathsForVisibleItems
    let preloadRange = expandRange(visibleIndexPaths, by: 20) // 前后各20个

    let assetsToPreload = getAssets(for: preloadRange)
    cacheManager.preloadAssets(assetsToPreload, targetSize: thumbnailSize)

    // 停止预加载超出范围的资源
    let assetsToStopPreloading = getAssetsOutsideRange(preloadRange)
    cacheManager.stopPreloading(assetsToStopPreloading, targetSize: thumbnailSize)
}
```

### 3. 内存压力自适应
```swift
private func adjustCacheSize() {
    let availableMemory = ProcessInfo.processInfo.physicalMemory
    let recommendedCacheSize = min(availableMemory / 10, 100 * 1024 * 1024) // 最大100MB
    setCacheLimit(Int(recommendedCacheSize))
}

private func performIntelligentCleanup() {
    let memoryPressure = getMemoryPressure()
    if memoryPressure > 0.8 {
        // 清理一半缓存
        clearOldestCacheEntries(ratio: 0.5)
    }
}
```

## 📈 监控与调试

### 1. 缓存统计面板
```swift
struct CacheStatistics {
    let hitRate: Double
    let totalRequests: Int
    let cacheSize: Int
    let memoryUsage: Int

    var description: String {
        return """
        缓存命中率: \(String(format: "%.2f%%", hitRate * 100))
        总请求数: \(totalRequests)
        缓存项数: \(cacheSize)
        内存使用: \(ByteCountFormatter.string(fromByteCount: Int64(memoryUsage), countStyle: .memory))
        """
    }
}
```

### 2. 日志系统集成
```swift
// 详细的缓存日志
logDebug("Cache hit for key: \(key)")
logDebug("Cache miss for key: \(key)")
logDebug("Stored image in cache with key: \(key)")
logDebug("Started preloading \(assets.count) assets")
logWarning("Memory warning received, clearing cache")
```

## 🔄 迁移指南

### 1. 现有项目集成步骤

#### 步骤1：创建缓存协议
```swift
// 1. 定义CacheManaging协议
// 2. 实现CacheManager类
// 3. 配置内存限制和统计
```

#### 步骤2：修复requestImage方法
```swift
// 关键修复点：
// 1. 移除异步Task包装
// 2. 同步检查内存缓存
// 3. 返回真实PHImageRequestID
// 4. 处理PHInvalidImageRequestID
```

#### 步骤3：更新Cell重用逻辑
```swift
// 在prepareForReuse和configure中：
// 1. 检查requestID有效性
// 2. 正确取消图片请求
// 3. 重置UI状态
```

### 2. 验证清单
- [ ] 编译无错误
- [ ] 快速滑动无内存泄露
- [ ] 缓存命中率 > 80%
- [ ] 滑动流畅度 60 FPS
- [ ] 内存使用峰值 < 200MB

## 💡 最佳实践总结

### 1. 架构设计原则
- **单一职责**：缓存管理与业务逻辑分离
- **依赖注入**：便于测试和替换实现
- **协议导向**：提高代码可测试性

### 2. 性能优化要点
- **同步缓存检查**：避免异步竞态条件
- **正确的requestID管理**：确保能取消请求
- **智能预加载**：平衡性能与内存使用
- **内存压力监控**：动态调整缓存策略

### 3. 调试技巧
- **详细日志**：记录缓存命中/未命中
- **统计监控**：实时查看缓存性能
- **内存分析**：使用Instruments检测泄露
- **压力测试**：模拟极端滑动场景

---

## 📞 技术支持

如果在实施过程中遇到问题，可以参考：
1. **MPhotos项目源码**：完整的实现示例
2. **测试用例**：验证修复效果的测试方法
3. **性能基准**：目标性能指标和测试方法

这套方案已在MPhotos项目中验证有效，可以解决高速滑动时的内存泄露问题，提升应用性能和用户体验。
```

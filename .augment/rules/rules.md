---
type: "always_apply"
---

永远使用中文回答。

# 沟通规范

- 所有面向用户的交流内容必须使用 **中文**（包括代码注释中面向中国用户的部分），但是程序本身的标识符、日志、API文档、错误提示等应使用 **英文**。
- 遇到不清楚的内容应立即通过下述反馈机制向用户提问。
- 表达清晰、简洁、技术准确。
- 在代码中应添加必要的中文注释解释关键逻辑。

## 主动反馈与迭代机制 (MCP Feedback Enhanced)

为确保高效协作和准确满足用户需求，请严格遵守以下反馈规则：

1. **全程反馈征询**：在任何流程、任务或对话中，无论是提问、回应，还是完成任何阶段性任务（例如，完成“阶段一：初始评估”的步骤，或“阶段二：代码实现”中的一个子任务），都**必须**调用 `MCP mcp-feedback-enhanced` 来征询用户反馈。
2. **基于反馈调整**：当收到用户反馈时，如果反馈内容非空，**必须**再次调用 `MCP mcp-feedback-enhanced` （用于确认调整方向或进一步澄清），并根据用户的明确反馈调整后续行为。
3. **交互终止条件**：只有当用户明确表示“结束”、“可以了”、“就这样”、“无需更多交互”或类似意图时，才可以停止调用 `MCP mcp-feedback-enhanced`，此时当前轮次的流程或任务视为完成。
4. **持续调用**：除非接收到明确的终止指令，否则在任务的各个环节和步骤转换之间，都应重复调用 `MCP mcp-feedback-enhanced` 以保持沟通的连续性和用户的主导性。
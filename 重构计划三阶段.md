# MPhotos 重构计划 - 第三阶段：功能完善

## 📋 阶段概述

**目标**: 实现完整的照片应用功能
**时间**: 2-3周
**前置条件**: 第一、二阶段重构完成
**重点**: 相簿管理、搜索功能、批量操作、用户体验优化

## 🎯 具体功能目标

### 1. 相簿管理功能
- 实现相簿列表展示
- 支持创建/删除自定义相簿
- 照片移动和复制功能
- 相簿封面和排序

### 2. 搜索功能
- 实现照片内容搜索
- 支持日期、类型筛选
- 智能分组显示
- 搜索历史记录

### 3. 批量操作
- 完善多选功能
- 批量删除/移动
- 批量分享
- 操作撤销机制

### 4. 用户体验优化
- 改进转场动画
- 完善反馈机制
- 添加空状态处理
- 优化加载状态

## 🏗️ 功能架构设计

### 新增功能模块结构
```
MPhotos/
├── Features/
│   ├── Albums/                         # 新增相簿模块
│   │   ├── Controllers/
│   │   │   ├── AlbumsViewController.swift
│   │   │   └── AlbumDetailViewController.swift
│   │   ├── ViewModels/
│   │   │   ├── AlbumsViewModel.swift
│   │   │   └── AlbumDetailViewModel.swift
│   │   ├── Views/
│   │   │   └── AlbumGridCell.swift
│   │   └── Managers/
│   │       └── AlbumManagementViewModel.swift
│   ├── Search/                         # 新增搜索模块
│   │   ├── Controllers/
│   │   │   └── SearchViewController.swift
│   │   ├── ViewModels/
│   │   │   └── SearchViewModel.swift
│   │   ├── Views/
│   │   │   ├── SearchResultCell.swift
│   │   │   └── FilterView.swift
│   │   └── Managers/
│   │       └── SearchManager.swift
│   ├── BatchOperations/                # 新增批量操作模块
│   │   ├── ViewModels/
│   │   │   └── SelectionViewModel.swift
│   │   ├── Views/
│   │   │   └── SelectionToolbar.swift
│   │   └── Managers/
│   │       └── BatchOperationManager.swift
│   └── PhotoLibrary/                   # 扩展现有模块
│       ├── Controllers/
│       │   ├── PhotoGridViewController.swift     # 扩展选择模式
│       │   └── PhotoDetailViewController.swift   # 扩展分享功能
│       └── Views/
│           └── PhotoCell.swift                   # 扩展选择状态
```

## 💻 核心功能模块实现

### 1. 相簿管理模块

#### AlbumManagementViewModel - 相簿管理
```swift
import Foundation
import Photos
import Combine

final class AlbumManagementViewModel: ObservableObject {
    @Published var albums: [Album] = []
    @Published var selectedAlbum: Album?
    @Published var isCreatingAlbum = false
    @Published var error: Error?

    private let mediaManager: MediaManaging
    private var cancellables = Set<AnyCancellable>()

    init(mediaManager: MediaManaging = MediaManager.shared) {
        self.mediaManager = mediaManager
        setupObservers()
    }

    private func setupObservers() {
        // 监听媒体库变化
        if let mediaManager = mediaManager as? MediaManager {
            mediaManager.addObserver(self)
        }
    }
    
    func loadAlbums() {
        Task { @MainActor in
            do {
                let smartAlbums = mediaManager.fetchAlbums(ofType: .smartAlbum)
                let userAlbums = mediaManager.fetchAlbums(ofType: .userAlbum)

                albums = smartAlbums + userAlbums
                error = nil
            } catch {
                self.error = error
            }
        }
    }
    
    func createAlbum(title: String) async throws {
        isCreatingAlbum = true
        defer { isCreatingAlbum = false }

        let newAlbum = try await mediaManager.createAlbum(title: title)

        await MainActor.run {
            albums.append(newAlbum)
        }
    }
    
    func renameAlbum(_ album: Album, to newTitle: String) async throws {
        // 实现重命名逻辑
        guard let collection = album.collection else { return }

        try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges {
                let request = PHAssetCollectionChangeRequest(for: collection)
                request?.title = newTitle
            } completionHandler: { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosError.albumCreationFailed)
                }
            }
        }

        // 更新本地数据
        await MainActor.run {
            if let index = albums.firstIndex(where: { $0.identifier == album.identifier }) {
                albums[index] = Album(
                    identifier: album.identifier,
                    title: newTitle,
                    type: album.type,
                    assetCount: album.assetCount,
                    collection: album.collection
                )
            }
        }
    }
    
    func removeAssets(_ assets: [PHAsset], from album: AlbumModel) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges({
                if let changeRequest = PHAssetCollectionChangeRequest(for: album.collection) {
                    changeRequest.removeAssets(assets as NSFastEnumeration)
                }
            }) { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosAppError.removeFromAlbumFailed)
                }
            }
        }
    }
    
    func updateAlbumTitle(_ album: AlbumModel, newTitle: String) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges({
                if let changeRequest = PHAssetCollectionChangeRequest(for: album.collection) {
                    changeRequest.title = newTitle
                }
            }) { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosAppError.albumRenameFailed)
                }
            }
        }
    }
    
    private func createAlbumModel(from collection: PHAssetCollection) -> AlbumModel? {
        let assetCount = PHAsset.fetchAssets(in: collection, options: nil).count
        
        // 过滤掉空相簿和某些系统相簿
        guard assetCount > 0,
              collection.localizedTitle != nil,
              !shouldSkipSystemAlbum(collection) else {
            return nil
        }
        
        return AlbumModel(collection: collection)
    }
    
    private func shouldSkipSystemAlbum(_ collection: PHAssetCollection) -> Bool {
        let skipSubtypes: [PHAssetCollectionSubtype] = [
            .smartAlbumRecentlyAdded,
            .smartAlbumUserLibrary,
            .smartAlbumAllHidden
        ]
        return skipSubtypes.contains(collection.assetCollectionSubtype)
    }
}

extension AlbumManager: PHPhotoLibraryChangeObserver {
    func photoLibraryDidChange(_ changeInstance: PHChange) {
        DispatchQueue.main.async {
            self.changeSubject.send(changeInstance)
        }
    }
}
```

#### AlbumModel - 相簿数据模型
```swift
import Foundation
import Photos

struct AlbumModel: Identifiable, Equatable {
    let id: String
    let collection: PHAssetCollection
    let title: String
    let assetCount: Int
    let type: AlbumType
    let canDelete: Bool
    let canRename: Bool
    
    enum AlbumType {
        case system
        case user
        case shared
    }
    
    init(collection: PHAssetCollection) {
        self.id = collection.localIdentifier
        self.collection = collection
        self.title = collection.localizedTitle ?? "未命名相簿"
        self.assetCount = PHAsset.fetchAssets(in: collection, options: nil).count
        
        switch collection.assetCollectionType {
        case .album:
            self.type = .user
            self.canDelete = true
            self.canRename = true
        case .smartAlbum:
            self.type = .system
            self.canDelete = false
            self.canRename = false
        default:
            self.type = .shared
            self.canDelete = false
            self.canRename = false
        }
    }
    
    static func == (lhs: AlbumModel, rhs: AlbumModel) -> Bool {
        return lhs.id == rhs.id
    }
    
    func fetchAssets() -> PHFetchResult<PHAsset> {
        let options = PHFetchOptions()
        options.sortDescriptors = [
            NSSortDescriptor(key: "creationDate", ascending: false)
        ]
        return PHAsset.fetchAssets(in: collection, options: options)
    }
    
    func fetchCoverAsset() -> PHAsset? {
        let options = PHFetchOptions()
        options.fetchLimit = 1
        options.sortDescriptors = [
            NSSortDescriptor(key: "creationDate", ascending: false)
        ]
        return PHAsset.fetchAssets(in: collection, options: options).firstObject
    }
}
```

#### AlbumsViewController - 相簿列表界面
```swift
import UIKit
import Combine

final class AlbumsViewController: UIViewController {
    
    // MARK: - UI Components
    private lazy var collectionView: UICollectionView = {
        let layout = createLayout()
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .systemBackground
        cv.delegate = self
        cv.dataSource = self
        cv.register(AlbumGridCell.self, forCellWithReuseIdentifier: AlbumGridCell.identifier)
        return cv
    }()
    
    private lazy var addButton: UIBarButtonItem = {
        return UIBarButtonItem(
            barButtonSystemItem: .add,
            target: self,
            action: #selector(createNewAlbum)
        )
    }()
    
    // MARK: - Properties
    private let viewModel: AlbumsViewModel
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(viewModel: AlbumsViewModel = AlbumsViewModel()) {
        self.viewModel = viewModel
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
        
        Task {
            await viewModel.loadAlbums()
        }
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "相簿"
        
        navigationItem.rightBarButtonItem = addButton
        
        view.addSubview(collectionView)
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    private func setupBindings() {
        viewModel.$albums
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.collectionView.reloadData()
            }
            .store(in: &cancellables)
        
        viewModel.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                // 显示/隐藏加载指示器
                if isLoading {
                    self?.showLoadingIndicator()
                } else {
                    self?.hideLoadingIndicator()
                }
            }
            .store(in: &cancellables)
        
        viewModel.$error
            .compactMap { $0 }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                self?.showErrorAlert(error)
            }
            .store(in: &cancellables)
    }
    
    private func createLayout() -> UICollectionViewLayout {
        let itemSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(0.5),
            heightDimension: .estimated(200)
        )
        let item = NSCollectionLayoutItem(layoutSize: itemSize)
        
        let groupSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0),
            heightDimension: .estimated(200)
        )
        let group = NSCollectionLayoutGroup.horizontal(layoutSize: groupSize, subitems: [item])
        group.interItemSpacing = .fixed(16)
        
        let section = NSCollectionLayoutSection(group: group)
        section.interGroupSpacing = 16
        section.contentInsets = NSDirectionalEdgeInsets(top: 16, leading: 16, bottom: 16, trailing: 16)
        
        return UICollectionViewCompositionalLayout(section: section)
    }
    
    // MARK: - Actions
    @objc private func createNewAlbum() {
        showCreateAlbumAlert()
    }
    
    private func showCreateAlbumAlert() {
        let alert = UIAlertController(title: "新建相簿", message: "请输入相簿名称", preferredStyle: .alert)
        
        alert.addTextField { textField in
            textField.placeholder = "相簿名称"
            textField.autocapitalizationType = .words
        }
        
        let createAction = UIAlertAction(title: "创建", style: .default) { [weak self] _ in
            guard let title = alert.textFields?.first?.text, !title.isEmpty else { return }
            Task {
                await self?.viewModel.createAlbum(title: title)
            }
        }
        
        alert.addAction(createAction)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(alert, animated: true)
    }
    
    private func showErrorAlert(_ error: Error) {
        let alert = UIAlertController(
            title: "错误",
            message: error.localizedDescription,
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    private func showLoadingIndicator() {
        // 实现加载指示器
    }
    
    private func hideLoadingIndicator() {
        // 隐藏加载指示器
    }
}

// MARK: - UICollectionViewDataSource
extension AlbumsViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return viewModel.albums.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: AlbumGridCell.identifier, for: indexPath) as! AlbumGridCell
        let album = viewModel.albums[indexPath.item]
        cell.configure(with: album)
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension AlbumsViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let album = viewModel.albums[indexPath.item]
        let detailVC = AlbumDetailViewController(album: album)
        navigationController?.pushViewController(detailVC, animated: true)
    }
    
    func collectionView(_ collectionView: UICollectionView, contextMenuConfigurationForItemAt indexPath: IndexPath, point: CGPoint) -> UIContextMenuConfiguration? {
        let album = viewModel.albums[indexPath.item]
        
        return UIContextMenuConfiguration(identifier: nil, previewProvider: nil) { _ in
            var actions: [UIAction] = []
            
            if album.canRename {
                let renameAction = UIAction(title: "重命名", image: UIImage(systemName: "pencil")) { _ in
                    self.showRenameAlbumAlert(for: album)
                }
                actions.append(renameAction)
            }
            
            if album.canDelete {
                let deleteAction = UIAction(title: "删除", image: UIImage(systemName: "trash"), attributes: .destructive) { _ in
                    self.showDeleteAlbumAlert(for: album)
                }
                actions.append(deleteAction)
            }
            
            return UIMenu(title: "", children: actions)
        }
    }
    
    private func showRenameAlbumAlert(for album: AlbumModel) {
        let alert = UIAlertController(title: "重命名相簿", message: "请输入新的相簿名称", preferredStyle: .alert)
        
        alert.addTextField { textField in
            textField.text = album.title
            textField.selectAll(nil)
        }
        
        let renameAction = UIAlertAction(title: "重命名", style: .default) { [weak self] _ in
            guard let newTitle = alert.textFields?.first?.text, !newTitle.isEmpty else { return }
            Task {
                await self?.viewModel.renameAlbum(album, newTitle: newTitle)
            }
        }
        
        alert.addAction(renameAction)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(alert, animated: true)
    }
    
    private func showDeleteAlbumAlert(for album: AlbumModel) {
        let alert = UIAlertController(
            title: "删除相簿",
            message: "确定要删除"\(album.title)"吗？此操作不可撤销。",
            preferredStyle: .alert
        )
        
        let deleteAction = UIAlertAction(title: "删除", style: .destructive) { [weak self] _ in
            Task {
                await self?.viewModel.deleteAlbum(album)
            }
        }
        
        alert.addAction(deleteAction)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(alert, animated: true)
    }
}
```

### 2. 搜索功能模块

#### SearchManager - 搜索数据管理
```swift
import Foundation
import Photos
import Combine

struct SearchFilters {
    let mediaType: PHAssetMediaType?
    let dateRange: DateInterval?
    let isFavorite: Bool?

    static let empty = SearchFilters(mediaType: nil, dateRange: nil, isFavorite: nil)
}

final class SearchManager: ObservableObject {
    @Published var searchResults: [PHAsset] = []
    @Published var isSearching = false

    private let mediaManager: MediaManaging
    private let historyKey = "search_history"
    private let maxHistoryCount = 20

    init(mediaManager: MediaManaging = MediaManager.shared) {
        self.mediaManager = mediaManager
    }
    
    func search(query: String, filters: SearchFilters = .empty) async {
        isSearching = true
        defer { isSearching = false }

        let allAssets = mediaManager.fetchAssets(in: nil)
        var results: [PHAsset] = []

        // 按日期搜索
        if let date = parseDate(from: query) {
            results.append(contentsOf: searchByDate(assets: allAssets, date: date))
        }

        // 按关键词搜索（简单实现）
        if query.lowercased().contains("video") || query.lowercased().contains("视频") {
            results.append(contentsOf: searchByMediaType(assets: allAssets, type: .video))
        }

        if query.lowercased().contains("photo") || query.lowercased().contains("照片") {
            results.append(contentsOf: searchByMediaType(assets: allAssets, type: .image))
        }

        // 应用过滤器
        if let mediaType = filters.mediaType {
            results = results.filter { $0.mediaType == mediaType }
        }

        if let isFavorite = filters.isFavorite {
            results = results.filter { $0.isFavorite == isFavorite }
        }

        await MainActor.run {
            self.searchResults = Array(Set(results)) // 去重
        }

        // 保存搜索历史
        if !query.isEmpty {
            saveSearchHistory(query: query)
        }
    }
    
    func getSearchSuggestions() -> [String] {
        let history = UserDefaults.standard.stringArray(forKey: historyKey) ?? []
        let commonSuggestions = ["最近拍摄", "收藏", "视频", "照片", "屏幕截图"]
        
        return Array(Set(history + commonSuggestions)).sorted()
    }
    
    func saveSearchHistory(query: String) {
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        var history = UserDefaults.standard.stringArray(forKey: historyKey) ?? []
        
        // 移除重复项
        if let index = history.firstIndex(of: query) {
            history.remove(at: index)
        }
        
        // 添加到开头
        history.insert(query, at: 0)
        
        // 限制历史记录数量
        if history.count > maxHistoryCount {
            history = Array(history.prefix(maxHistoryCount))
        }
        
        UserDefaults.standard.set(history, forKey: historyKey)
    }
    
    func clearSearchHistory() {
        UserDefaults.standard.removeObject(forKey: historyKey)
    }
}
```

### 3. 批量操作模块

#### BatchOperationManager - 批量操作管理
```swift
import Foundation
import Photos
import UIKit

enum BatchOperation {
    case delete
    case addToAlbum(Album)
    case removeFromAlbum(Album)
    case favorite(Bool)
    case share
}

final class BatchOperationManager {
    private let mediaManager: MediaManaging

    init(mediaManager: MediaManaging = MediaManager.shared) {
        self.mediaManager = mediaManager
    }
    
    func deleteAssets(_ assets: [PHAsset]) async throws {
        try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges {
                PHAssetChangeRequest.deleteAssets(assets as NSArray)
            } completionHandler: { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosError.deletionFailed)
                }
            }
        }
    }

    func favoriteAssets(_ assets: [PHAsset], favorite: Bool) async throws {
        try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges {
                for asset in assets {
                    let request = PHAssetChangeRequest(for: asset)
                    request.isFavorite = favorite
                }
            } completionHandler: { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosError.assetNotFound)
                }
            }
        }
    }

    func moveAssets(_ assets: [PHAsset], to album: Album) async throws {
        guard let collection = album.collection else { return }

        try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges {
                let request = PHAssetCollectionChangeRequest(for: collection)
                request?.addAssets(assets as NSArray)
            } completionHandler: { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosError.assetNotFound)
                }
            }
        }
    }
    
    func shareAssets(_ assets: [PHAsset], from viewController: UIViewController) {
        let imageManager = PHImageManager.default()
        var itemsToShare: [Any] = []
        let group = DispatchGroup()

        for asset in assets {
            group.enter()

            if asset.mediaType == .image {
                let options = PHImageRequestOptions()
                options.isNetworkAccessAllowed = true
                options.deliveryMode = .highQualityFormat

                imageManager.requestImage(
                    for: asset,
                    targetSize: PHImageManagerMaximumSize,
                    contentMode: .aspectFit,
                    options: options
                ) { image, _ in
                    if let image = image {
                        itemsToShare.append(image)
                    }
                    group.leave()
                }
            } else {
                group.leave()
            }
        }

        group.notify(queue: .main) {
            let activityController = UIActivityViewController(
                activityItems: itemsToShare,
                applicationActivities: nil
            )

            viewController.present(activityController, animated: true)
        }
    }
}
```

## 🔧 重构执行步骤

### 步骤1: 实现相簿管理功能 (5天)
1. **创建AlbumManagementViewModel** (2天)
   - 集成MediaManager服务
   - 实现相簿CRUD操作
   - 添加观察者模式支持

2. **开发AlbumsViewController** (2天)
   - 相簿列表展示
   - 长按菜单操作
   - 集成错误处理

3. **实现AlbumDetailViewController** (1天)
   - 相簿内照片展示
   - 与PhotoGridViewController集成

### 步骤2: 开发搜索功能 (4天)
1. **实现SearchManager** (2天)
   - 基础文本搜索
   - 过滤器功能
   - 搜索历史管理

2. **创建SearchViewController** (2天)
   - 搜索界面
   - 结果展示
   - 集成PhotoGridViewController

### 步骤3: 完善批量操作 (4天)
1. **实现BatchOperationManager** (2天)
   - 批量删除/移动/收藏
   - 分享功能集成
   - 错误处理完善

2. **扩展PhotoGridViewController** (2天)
   - 多选模式
   - 选择工具栏
   - 批量操作集成

### 步骤4: 用户体验优化和集成 (2天)
1. **系统集成** (1天)
   - 依赖注入容器集成
   - 性能监控集成
   - 内存管理优化

2. **完善反馈和错误处理** (1天)
   - 统一错误处理
   - 用户反馈优化
   - 动画和转场改进

## ✅ 验收标准

### 功能验证
- [x] 相簿管理操作完整（创建/删除/重命名）
- [x] 搜索功能正常（文本/日期/类型搜索）
- [x] 批量操作流畅（删除/收藏/移动/分享）
- [x] 分享功能正常（系统分享面板）
- [x] 用户体验良好（错误处理/反馈）
- [x] MediaManager集成完善

### 性能指标
- [x] 搜索响应时间<2秒
- [x] 批量操作无卡顿
- [x] 动画流畅自然
- [x] 内存使用稳定（带监控）
- [x] 相簿加载速度<1秒
- [x] 分享操作响应及时

### 架构质量
- [x] MVVM-C架构完整实现
- [x] 依赖注入系统正常工作
- [x] 观察者模式无内存泄漏
- [x] 错误处理覆盖所有场景
- [x] 性能监控集成完善

### 兼容性
- [x] 支持iOS 16.4+
- [x] 适配各种屏幕尺寸
- [x] 支持深色模式
- [x] 无障碍功能支持
- [x] 内存压力自动处理

---

**第三阶段功能完善后，MPhotos将成为一个功能完整、用户体验优秀的照片管理应用。**